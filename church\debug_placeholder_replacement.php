<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Debugging {member_image} Placeholder Replacement\n";
echo "==================================================\n\n";

// Get a test member with an image
$stmt = $pdo->query("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$birthdayMember = $stmt->fetch();

if (!$birthdayMember) {
    echo "❌ No test member found with image\n";
    exit;
}

// Get another member to receive the notification
$stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? LIMIT 1");
$stmt->execute([$birthdayMember['id']]);
$recipientMember = $stmt->fetch();

if (!$recipientMember) {
    echo "❌ No recipient member found\n";
    exit;
}

echo "🎂 Birthday Member: " . $birthdayMember['full_name'] . "\n";
echo "📧 Recipient Member: " . $recipientMember['full_name'] . "\n";
echo "📷 Birthday Member Image: " . $birthdayMember['image_path'] . "\n\n";

// Get Member Upcoming Birthday Notification template 1
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name = ?");
$stmt->execute(["Member Upcoming Birthday Notification 1"]);
$template = $stmt->fetch();

if (!$template) {
    echo "❌ Template not found\n";
    exit;
}

// Create the member data exactly like the real system does
$birthdayReminder = new BirthdayReminder($pdo);

// Build member data for notification (recipient gets the notification about birthday member)
$memberData = [
    'id' => $recipientMember['id'],
    'full_name' => $recipientMember['full_name'],
    'first_name' => $recipientMember['first_name'],
    'email' => $recipientMember['email'],
    
    // Birthday member data
    'birthday_member_id' => $birthdayMember['id'],
    'birthday_member_full_name' => $birthdayMember['full_name'],
    'birthday_member_first_name' => $birthdayMember['first_name'],
    'birthday_member_email' => $birthdayMember['email'],
    'birthday_member_age' => 25,
    
    // Image data
    'image_path' => $birthdayMember['image_path'],
    'member_image_url' => 'http://localhost/campaign/church/' . $birthdayMember['image_path'],
    'birthday_member_image_url' => 'http://localhost/campaign/church/' . $birthdayMember['image_path'],
    
    // Birthday notification flag
    '_is_birthday_notification' => true,
    '_birthday_member_original_image_path' => $birthdayMember['image_path'],
    
    // Other placeholders
    'recipient_full_name' => $recipientMember['full_name'],
    'days_text' => 'in 3 days',
    'upcoming_birthday_formatted' => 'July 22, 2025',
    'organization_name' => 'Freedom Assembly',
];

echo "📝 Member Data:\n";
echo "===============\n";
echo "member_image_url: " . ($memberData['member_image_url'] ?? 'NOT SET') . "\n";
echo "birthday_member_image_url: " . ($memberData['birthday_member_image_url'] ?? 'NOT SET') . "\n";
echo "_is_birthday_notification: " . ($memberData['_is_birthday_notification'] ? 'TRUE' : 'FALSE') . "\n\n";

echo "🔄 Testing Placeholder Replacement...\n";
echo "=====================================\n";

// Test the placeholder replacement
$originalContent = $template['content'];
echo "Original template contains {member_image}: " . (strpos($originalContent, '{member_image}') !== false ? 'YES' : 'NO') . "\n\n";

// Process the content
$processedContent = replaceTemplatePlaceholders($originalContent, $memberData);

echo "After replacement contains {member_image}: " . (strpos($processedContent, '{member_image}') !== false ? 'NO - REPLACED' : 'YES - NOT REPLACED') . "\n";
echo "After replacement contains <img: " . (strpos($processedContent, '<img') !== false ? 'YES - IMG TAG FOUND' : 'NO - NO IMG TAG') . "\n\n";

// Show the member_image section
if (strpos($processedContent, '<img') !== false) {
    echo "🖼️ Found IMG tag in processed content!\n";
    preg_match('/<img[^>]*>/', $processedContent, $matches);
    if (!empty($matches)) {
        echo "IMG tag: " . $matches[0] . "\n\n";
    }
} else {
    echo "❌ No IMG tag found in processed content\n";
    
    // Show what's in the member-section
    if (preg_match('/<div class="member-section">(.*?)<\/div>/s', $processedContent, $matches)) {
        echo "Member section content:\n";
        echo $matches[1] . "\n\n";
    }
}

echo "🎯 This shows whether the {member_image} placeholder is being replaced correctly!\n";
?>
