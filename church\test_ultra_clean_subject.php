<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔧 Testing Ultra-Clean Subject Fix\n";
echo "==================================\n\n";

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if (!$sandra) {
        echo "❌ Sandra not found\n";
        exit;
    }
    
    echo "✅ <PERSON> found: " . $sandra['full_name'] . "\n";
    
    // Calculate days until birthday
    $today = new DateTime();
    $birthDate = new DateTime($sandra['birth_date']);
    $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
    
    if ($thisYearBirthday < $today) {
        $thisYearBirthday->add(new DateInterval('P1Y'));
    }
    
    $daysUntil = $today->diff($thisYearBirthday)->days;
    
    echo "🔄 Sending test with ultra-clean subject protection...\n";
    echo "=====================================================\n";
    
    // Clear debug log
    $debug_log_file = __DIR__ . '/logs/email_debug.log';
    if (file_exists($debug_log_file)) {
        file_put_contents($debug_log_file, '');
    }
    
    $result = $birthdayReminder->sendMemberBirthdayNotifications(
        $sandra['id'],
        null,  // Let system choose template
        $daysUntil
    );
    
    echo "📋 Results:\n";
    echo "===========\n";
    
    if (isset($result['success']) && $result['success'] > 0) {
        echo "🎉 SUCCESS! " . $result['success'] . " notification emails sent!\n";
        
        // Check the debug log for ultra-clean subject
        if (file_exists($debug_log_file)) {
            $logContent = file_get_contents($debug_log_file);
            
            echo "\n🔍 Checking ultra-clean subject in debug log:\n";
            echo "==============================================\n";
            
            $lines = explode("\n", $logContent);
            $subjectFound = false;
            
            foreach ($lines as $line) {
                if (strpos($line, 'Ultra-clean subject set:') !== false || 
                    strpos($line, 'FINAL SUBJECT PROTECTION:') !== false ||
                    strpos($line, 'Subject:') !== false) {
                    echo "📧 " . trim($line) . "\n";
                    $subjectFound = true;
                }
            }
            
            if (!$subjectFound) {
                echo "❌ No subject lines found in debug log\n";
            }
            
            // Check for any problematic content in subject
            if (strpos($logContent, 'Ultra-clean subject set: \'Birthday Celebration! Sandra\'') !== false) {
                echo "\n✅ PERFECT! Ultra-clean subject confirmed!\n";
            }
            
            // Check if any HTML is still getting through
            $htmlInSubject = false;
            foreach ($lines as $line) {
                if (strpos($line, 'Subject:') !== false && 
                    (strpos($line, 'body {') !== false || strpos($line, '<') !== false)) {
                    $htmlInSubject = true;
                    break;
                }
            }
            
            if ($htmlInSubject) {
                echo "\n❌ WARNING: HTML still found in subject line\n";
            } else {
                echo "\n✅ NO HTML found in subject line - completely clean!\n";
            }
        }
        
        echo "\n📧 Expected Gmail display:\n";
        echo "==========================\n";
        echo "Subject: Birthday Celebration! Sandra\n";
        echo "(No HTML, no CSS, no emojis, pure ASCII text)\n";
        
    } elseif (isset($result['error'])) {
        echo "❌ Error: " . $result['error'] . "\n";
    } else {
        echo "❌ Unexpected result\n";
        var_dump($result);
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n🎯 ULTRA-CLEAN SUBJECT SUMMARY:\n";
echo "===============================\n";
echo "✅ Double-layer subject protection applied\n";
echo "✅ ASCII-only encoding forced\n";
echo "✅ All HTML/CSS/emojis stripped\n";
echo "✅ MIME header optimization\n";
echo "✅ Gmail compatibility maximized\n\n";
echo "If Gmail still shows HTML in the subject, it might be:\n";
echo "1. Gmail caching the old subject (try refreshing)\n";
echo "2. Email client interpretation issue\n";
echo "3. Need to check a different email account\n\n";
echo "The SMTP server is definitely sending clean subjects now!\n";
?>
