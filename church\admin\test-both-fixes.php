<?php
/**
 * Test Both Fixes - Email Template and Modal Centering
 */

// Security check
if (!defined('ADMIN_ACCESS')) {
    define('ADMIN_ACCESS', true);
}

// Include the configuration file
require_once '../config.php';

// Set page variables
$page_title = 'Test Both Fixes';
$page_header = 'Email Template & Modal Centering Test';
$page_description = 'Verify both the email template duplicate name fix and modal centering fix';

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Success Alert -->
            <div class="alert alert-success">
                <h5><i class="bi bi-check-circle-fill me-2"></i>Both Fixes Applied Successfully!</h5>
                <p class="mb-0">The email template duplicate name issue and modal centering issue have been resolved.</p>
            </div>
            
            <!-- Email Template Fix Status -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="bi bi-envelope-fill me-2"></i>Email Template Fix Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>✅ Fixed Templates:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>Template 58:</strong> Clean Birthday Notification
                                    <br><small class="text-muted">Removed duplicate member names after image</small>
                                </li>
                                <li class="list-group-item">
                                    <strong>Template 37:</strong> Member Upcoming Birthday Notification 1
                                    <br><small class="text-muted">Removed duplicate member names after image</small>
                                </li>
                                <li class="list-group-item">
                                    <strong>Template 47:</strong> Member Upcoming Birthday Notification 3
                                    <br><small class="text-muted">Removed duplicate member names after image</small>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>📧 What Was Fixed:</h6>
                            <div class="alert alert-info">
                                <strong>Before:</strong> Member image followed by duplicate names<br>
                                <code>🖼️ [Member Image]<br>Sandra Stern<br>🎉 Sandra Stern 🎉</code>
                                <hr>
                                <strong>After:</strong> Only member image<br>
                                <code>🖼️ [Member Image]</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modal Centering Fix Status -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="bi bi-window-stack me-2"></i>Modal Centering Fix Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>✅ Modal Improvements:</h6>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <strong>Positioning:</strong> Changed from sidebar-relative to viewport-centered
                                </li>
                                <li class="list-group-item">
                                    <strong>Centering:</strong> Implemented flexbox centering
                                </li>
                                <li class="list-group-item">
                                    <strong>Width:</strong> Optimized modal widths for better UX
                                </li>
                                <li class="list-group-item">
                                    <strong>Responsive:</strong> Works on all screen sizes
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>🎯 Test Modal Centering:</h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal1">
                                    Test Small Modal (Confirmation)
                                </button>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#testModal2">
                                    Test Medium Modal (Form)
                                </button>
                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#testModal3">
                                    Test Large Modal (Content)
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Test Results -->
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0"><i class="bi bi-clipboard-check me-2"></i>Verification Instructions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📧 Email Template Verification:</h6>
                            <ol>
                                <li>Check your Gmail inbox for the latest birthday notification</li>
                                <li>Look for Sandra Stern's birthday notification</li>
                                <li>Verify that you see ONLY her image</li>
                                <li>Confirm there are NO duplicate names below the image</li>
                                <li>The email should be clean and professional</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6>🎯 Modal Centering Verification:</h6>
                            <ol>
                                <li>Click the "Test Modal" buttons above</li>
                                <li>Verify modals appear centered in the viewport</li>
                                <li>Check that modals are not positioned to one side</li>
                                <li>Test on different screen sizes if possible</li>
                                <li>Confirm modals have appropriate widths</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Modal 1 - Small/Confirmation -->
<div class="modal fade" id="testModal1" tabindex="-1" aria-labelledby="testModal1Label" aria-hidden="true">
    <div class="modal-dialog modal-confirmation">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModal1Label">Confirmation Modal Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is a small confirmation modal (450px width).</p>
                <p>It should be perfectly centered in your viewport.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Test Modal 2 - Medium/Form -->
<div class="modal fade" id="testModal2" tabindex="-1" aria-labelledby="testModal2Label" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModal2Label">Form Modal Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="testName" class="form-label">Name</label>
                        <input type="text" class="form-control" id="testName">
                    </div>
                    <div class="mb-3">
                        <label for="testEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="testEmail">
                    </div>
                </form>
                <p>This is a form modal (650px width) that should be centered.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Test Modal 3 - Large/Content -->
<div class="modal fade" id="testModal3" tabindex="-1" aria-labelledby="testModal3Label" aria-hidden="true">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModal3Label">Large Content Modal Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Left Column</h6>
                        <p>This is a large modal (800px width) with more content.</p>
                        <p>It should be perfectly centered in the viewport.</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Right Column</h6>
                        <p>The modal width has been optimized for better user experience.</p>
                        <p>No more overwhelming wide modals!</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Log modal events for verification
document.addEventListener('show.bs.modal', function(e) {
    console.log('✅ Modal showing:', e.target.id);
    console.log('🎯 Modal should be centered in viewport');
});

document.addEventListener('shown.bs.modal', function(e) {
    const modal = e.target;
    const modalDialog = modal.querySelector('.modal-dialog');
    const rect = modalDialog.getBoundingClientRect();
    
    console.log('📊 Modal position:', {
        left: rect.left,
        top: rect.top,
        width: rect.width,
        height: rect.height,
        centerX: rect.left + rect.width / 2,
        centerY: rect.top + rect.height / 2,
        viewportCenterX: window.innerWidth / 2,
        viewportCenterY: window.innerHeight / 2
    });
    
    // Check if modal is reasonably centered
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const viewportCenterX = window.innerWidth / 2;
    const viewportCenterY = window.innerHeight / 2;
    
    const horizontalDiff = Math.abs(centerX - viewportCenterX);
    const verticalDiff = Math.abs(centerY - viewportCenterY);
    
    if (horizontalDiff < 50 && verticalDiff < 100) {
        console.log('✅ Modal is properly centered!');
    } else {
        console.log('⚠️ Modal centering may need adjustment');
    }
});
</script>

<?php include 'includes/footer.php'; ?>
