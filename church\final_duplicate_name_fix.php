<?php
require_once 'config.php';

echo "🔧 Final Fix for All Duplicate Member Names\n";
echo "===========================================\n\n";

try {
    // Fix Template 58 completely - remove ALL name displays after image
    echo "🎯 Final fix for Template 58...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 58");
    $stmt->execute();
    $template58 = $stmt->fetch();
    
    if ($template58) {
        $content = $template58['content'];
        
        // Remove ALL h2 and h3 tags with member names after {member_image}
        $content = preg_replace(
            '/(\{member_image\})\s*<h2[^>]*>🎉[^<]*\{birthday_member_name\}[^<]*🎉<\/h2>/',
            '$1',
            $content
        );
        
        // Remove any remaining h3 tags with member names
        $content = preg_replace(
            '/(\{member_image\})\s*<h3[^>]*>\{birthday_member_[^}]+\}<\/h3>/',
            '$1',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
        $stmt->execute([$content]);
        
        echo "✅ Template 58 completely fixed\n";
        
        // Verify the fix
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 300);
            echo "📄 Result:\n" . $preview . "\n\n";
        }
    }
    
    // Fix Template 37 completely - remove the member-name div
    echo "🎯 Final fix for Template 37...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 37");
    $stmt->execute();
    $template37 = $stmt->fetch();
    
    if ($template37) {
        $content = $template37['content'];
        
        // Remove the member-name div completely
        $content = preg_replace(
            '/<div class="member-name">🎉[^<]*\{birthday_member_full_name\}[^<]*🎉<\/div>/',
            '',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 37");
        $stmt->execute([$content]);
        
        echo "✅ Template 37 completely fixed\n";
        
        // Verify the fix
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 300);
            echo "📄 Result:\n" . $preview . "\n\n";
        }
    }
    
    echo "🎉 ALL DUPLICATE MEMBER NAMES REMOVED!\n";
    echo "📧 Email templates now show only the member image without any redundant name text.\n\n";
    
    // Send a final test
    echo "🧪 Sending final test notification...\n";
    
    // Get Sandra's details for testing
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%' LIMIT 1");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        require_once 'send_birthday_reminders.php';
        $birthdayReminder = new BirthdayReminder($pdo);
        
        // Clear debug log
        $debug_log_file = __DIR__ . '/logs/email_debug.log';
        if (file_exists($debug_log_file)) {
            file_put_contents($debug_log_file, '');
        }
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, 2);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Final test notification sent!\n";
            echo "📧 Check your email - it should now show only the member image without duplicate names.\n";
        } else {
            echo "❌ Test failed\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
