<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Debugging Admin Notification Logic\n";
echo "======================================\n\n";

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

// Simulate exactly what the admin interface does
echo "🎯 Simulating Admin Interface Logic:\n";
echo "====================================\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if (!$sandra) {
        echo "❌ Sandra not found\n";
        exit;
    }
    
    echo "✅ Sandra found: ID " . $sandra['id'] . "\n";
    
    // Calculate days until birthday (same logic as admin interface)
    $today = new DateTime();
    $birthDate = new DateTime($sandra['birth_date']);
    $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
    
    if ($thisYearBirthday < $today) {
        $thisYearBirthday->add(new DateInterval('P1Y'));
    }
    
    $daysUntil = $today->diff($thisYearBirthday)->days;
    echo "Days until Sandra's birthday: " . $daysUntil . "\n\n";
    
    // Test the exact method call that the admin interface makes
    echo "🔄 Calling sendMemberBirthdayNotifications() exactly like admin interface...\n";
    echo "Parameters:\n";
    echo "  - birthdayMemberId: " . $sandra['id'] . "\n";
    echo "  - templateId: null (let system choose)\n";
    echo "  - daysUntil: " . $daysUntil . "\n\n";
    
    $result = $birthdayReminder->sendMemberBirthdayNotifications(
        $sandra['id'],
        null,  // templateId - let system choose
        $daysUntil
    );
    
    echo "📋 Raw Result from sendMemberBirthdayNotifications():\n";
    echo "====================================================\n";
    var_dump($result);
    echo "\n";
    
    // Analyze the result using the same logic as admin interface
    echo "🔍 Admin Interface Logic Analysis:\n";
    echo "==================================\n";
    
    if (isset($result['error'])) {
        echo "❌ Has error: " . $result['error'] . "\n";
    } elseif (isset($result['success']) && $result['success'] > 0) {
        echo "✅ Success: " . $result['success'] . " emails sent\n";
        if (isset($result['failed']) && $result['failed'] > 0) {
            echo "⚠️ Failed: " . $result['failed'] . " emails failed\n";
        }
    } elseif (isset($result['message'])) {
        echo "ℹ️ Has message: " . $result['message'] . "\n";
    } else {
        echo "❌ No success, no error, no message - this triggers the generic error!\n";
        echo "This is why you see: 'No notification emails were sent. Please check if there are eligible recipients.'\n";
    }
    
    echo "\n🔍 Detailed Analysis:\n";
    echo "=====================\n";
    
    if (is_array($result)) {
        foreach ($result as $key => $value) {
            echo "  $key: ";
            if (is_array($value)) {
                echo "Array(" . count($value) . " items)\n";
                foreach ($value as $subKey => $subValue) {
                    echo "    $subKey: $subValue\n";
                }
            } else {
                echo "$value\n";
            }
        }
    } else {
        echo "Result is not an array: " . gettype($result) . " = " . $result . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 Next Steps:\n";
echo "==============\n";
echo "Based on the result analysis above, I'll identify exactly why\n";
echo "the admin interface is failing and fix the specific issue.\n";
?>
