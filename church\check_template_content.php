<?php
require_once 'config.php';

echo "🔍 Checking Template 58 Content\n";
echo "===============================\n\n";

$stmt = $pdo->prepare('SELECT * FROM email_templates WHERE id = 58');
$stmt->execute();
$template = $stmt->fetch();

if ($template) {
    echo "Template Name: " . $template['template_name'] . "\n";
    echo "Subject: " . $template['subject'] . "\n\n";
    echo "Content:\n";
    echo "========\n";
    echo $template['content'];
    echo "\n\n";
    
    // Check for specific issues
    echo "🔍 Checking for issues:\n";
    echo "=======================\n";
    
    $issues = [];
    
    if (strpos($template['content'], '{days_until_birthday_plural}') !== false) {
        $issues[] = "Found {days_until_birthday_plural} placeholder";
    }
    
    if (strpos($template['content'], '{birthday_date_formatted}') !== false) {
        $issues[] = "Found {birthday_date_formatted} placeholder";
    }
    
    if (strpos($template['content'], '<PERSON>') !== false && strpos($template['content'], '{birthday_member') === false) {
        $issues[] = "Found hardcoded 'Sandra' references";
    }
    
    if (strpos($template['content'], '43 Years') !== false) {
        $issues[] = "Found hardcoded age '43 Years'";
    }
    
    if (strpos($template['content'], '<EMAIL>') !== false) {
        $issues[] = "Found hardcoded email '<EMAIL>'";
    }
    
    if (strpos($template['content'], '{recipient_name}') !== false) {
        $issues[] = "Found {recipient_name} placeholder (should be {first_name})";
    }
    
    if (empty($issues)) {
        echo "✅ No issues found in template!\n";
    } else {
        echo "❌ Issues found:\n";
        foreach ($issues as $issue) {
            echo "  - $issue\n";
        }
        
        echo "\n🔧 Fixing issues...\n";
        
        $content = $template['content'];
        $updated = false;
        
        // Fix all the issues
        $fixes = [
            '{days_until_birthday_plural}' => 's',
            'day{days_until_birthday_plural}' => '{days_text}',
            '{birthday_date_formatted}' => '{birthday_date}',
            'Sandra\'s birthday' => '{birthday_member_full_name}\'s birthday',
            'Sandra Stern' => '{birthday_member_full_name}',
            '🎂 Sandra 🎂' => '🎂 {birthday_member_name} 🎂',
            'celebrate Sandra\'s' => 'celebrate {birthday_member_full_name}\'s',
            '43 Years' => '{birthday_member_age} Years',
            '<EMAIL>' => '{birthday_member_email}',
            'Happy Birthday Sandra!' => 'Happy Birthday {birthday_member_name}!',
            '{recipient_name}' => '{first_name}',
            'Dear {recipient_name}' => 'Dear {first_name}',
        ];
        
        foreach ($fixes as $search => $replace) {
            if (strpos($content, $search) !== false) {
                $content = str_replace($search, $replace, $content);
                $updated = true;
                echo "  ✅ Fixed: '$search' → '$replace'\n";
            }
        }
        
        if ($updated) {
            $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
            $result = $stmt->execute([$content]);
            
            if ($result) {
                echo "\n✅ Template updated successfully!\n";
            } else {
                echo "\n❌ Failed to update template\n";
            }
        }
    }
} else {
    echo "❌ Template 58 not found\n";
}
?>
