<?php
require_once 'config.php';
require_once 'vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

class BirthdayReminder {
    private $pdo;
    private $adminEmail;
    private $failedEmails = [];
    private $sentEmails = [];
    private $trackingEnabled = true;
    private $emailType = 'birthday'; // Default email type
    private $testLimit = 2; // Default test limit

    public function __construct($pdo, $adminEmail = null) {
        $this->pdo = $pdo;
        $this->adminEmail = $adminEmail ?: '<EMAIL>'; // Update default admin email

        // Ensure we have a valid database connection
        $this->ensureConnection();
    }

    /**
     * Add throttling delay between emails based on settings
     */
    private function addThrottlingDelay() {
        // Get configured delay from email settings, default to 3 seconds if not set
        $delay_seconds = 3; // Default delay

        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_sending_delay_seconds' LIMIT 1");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result && !empty($result['setting_value'])) {
                $delay_seconds = floatval($result['setting_value']);
            }
        } catch (Exception $e) {
            // If there's an error, use the default delay
            error_log("Error fetching email delay setting: " . $e->getMessage());
        }

        // Add random variation to the delay (±20%)
        $variation = $delay_seconds * 0.2;
        $actual_delay = $delay_seconds + (mt_rand(-100, 100) / 100) * $variation;
        $actual_delay = max(0.5, $actual_delay); // Minimum 0.5 seconds

        // Convert to microseconds and sleep
        usleep(intval($actual_delay * 1000000));
    }

    /**
     * Get email batch size from settings
     */
    private function getEmailBatchSize() {
        // Get batch size from settings, default to 25 if not set
        $batch_size = 25; // Default batch size
        try {
            $stmt = $this->pdo->prepare("SELECT setting_value FROM email_settings WHERE setting_key = 'email_batch_size' LIMIT 1");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result && !empty($result['setting_value'])) {
                $batch_size = intval($result['setting_value']);
                // Ensure batch size is valid (between 10 and 5000)
                $batch_size = max(10, min(5000, $batch_size));
            }
        } catch (Exception $e) {
            error_log("Error fetching email batch size setting: " . $e->getMessage());
        }

        return $batch_size;
    }

    /**
     * Ensure we have a valid database connection
     * @return bool True if connection is valid
     */
    private function ensureConnection() {
        try {
            if (!($this->pdo instanceof PDO)) {
                error_log("No PDO connection, attempting to create one");
                if (function_exists('getDbConnection')) {
                    $this->pdo = getDbConnection();
                } else {
                    throw new Exception("getDbConnection function not available");
                }
            } else {
                // Test the existing connection
                $this->pdo->query("SELECT 1");
            }
            return true;
        } catch (PDOException $e) {
            error_log("Database connection lost, attempting to reconnect: " . $e->getMessage());
            try {
                if (function_exists('getDbConnection')) {
                    $this->pdo = getDbConnection();
                    return true;
                }
            } catch (Exception $reconnectError) {
                error_log("Failed to reconnect to database: " . $reconnectError->getMessage());
                throw $reconnectError;
            }
        }
        return false;
    }

    /**
     * Set the admin email for notifications
     * @param string $email The admin email address
     */
    public function setAdminEmail($email) {
        if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->adminEmail = $email;
            return true;
        }
        return false;
    }

    public function getUpcomingBirthdays($daysAhead) {
        // Prioritize birth_date over date_of_birth for consistency
        // Use COALESCE to get the preferred birth date field
        $query = "SELECT *,
                  COALESCE(birth_date, date_of_birth) as preferred_birth_date
                  FROM members WHERE
                  (
                    MONTH(DATE_ADD(CURRENT_DATE, INTERVAL ? DAY)) = MONTH(COALESCE(birth_date, date_of_birth)) AND
                    DAY(DATE_ADD(CURRENT_DATE, INTERVAL ? DAY)) = DAY(COALESCE(birth_date, date_of_birth))
                  )
                  AND (birth_date IS NOT NULL OR date_of_birth IS NOT NULL)";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$daysAhead, $daysAhead]);

        $members = $stmt->fetchAll();

        // Ensure each member has a consistent birth_date field
        foreach ($members as &$member) {
            if (empty($member['birth_date']) && !empty($member['date_of_birth'])) {
                $member['birth_date'] = $member['date_of_birth'];
            }
        }

        error_log("Found " . count($members) . " members with birthdays " . ($daysAhead == 0 ? "today" : "in $daysAhead days"));
        return $members;
    }

    /**
     * Get a birthday notification template that contains {member_image} placeholder
     * @return array|null Template data or null if none found
     */
    private function getBirthdayNotificationTemplate() {
        try {
            // First try to get notification templates with {member_image} placeholder
            $query = "SELECT * FROM email_templates
                     WHERE is_birthday_template = 0
                     AND template_name LIKE '%Notification%'
                     AND content LIKE '%{member_image}%'
                     ORDER BY RAND() LIMIT 1";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $template = $stmt->fetch();

            if ($template) {
                error_log("Selected notification template with member image: ID={$template['id']}, Name={$template['template_name']}");
                return $template;
            }

            // Fallback to any notification template
            $query = "SELECT * FROM email_templates
                     WHERE is_birthday_template = 0
                     AND template_name LIKE '%Notification%'
                     ORDER BY RAND() LIMIT 1";
            $stmt = $this->pdo->prepare($query);
            $stmt->execute();
            $template = $stmt->fetch();

            if ($template) {
                error_log("Selected notification template (no member image): ID={$template['id']}, Name={$template['template_name']}");
                return $template;
            }

            error_log("No notification templates found");
            return null;

        } catch (Exception $e) {
            error_log("Error getting birthday notification template: " . $e->getMessage());
            return null;
        }
    }

    private function getEmailTemplate($isBirthday = false) {
        try {
        $query = "SELECT * FROM email_templates WHERE is_birthday_template = ? ORDER BY RAND() LIMIT 1";
        $stmt = $this->pdo->prepare($query);
            $stmt->execute([$isBirthday ? 1 : 0]);
            $template = $stmt->fetch();

            // Validate template
            if (!$template) {
                error_log("No template found with is_birthday_template=" . ($isBirthday ? "1" : "0"));
                return null;
            }

            // Log template selected
            error_log("Random template selected: ID={$template['id']}, Name={$template['template_name']}");

            // Verify content exists
            if (empty($template['content'])) {
                error_log("Warning: Template ID {$template['id']} has empty content");
            }
            
            return $template;
        } catch (Exception $e) {
            error_log("Error getting email template: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Enable or disable email tracking
     * @param bool $enabled Whether tracking is enabled
     */
    public function setTracking($enabled = true) {
        $this->trackingEnabled = $enabled;
    }

    /**
     * Get a specific template by ID
     * @param int $templateId The template ID
     * @return array|false The template data or false if not found
     */
    public function getTemplateById($templateId) {
        try {
        $query = "SELECT * FROM email_templates WHERE id = ?";
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$templateId]);
            $template = $stmt->fetch();
            
            // Validate that we have a valid template with required fields
            if (!$template) {
                error_log("Template not found for ID: $templateId");
                return null;
            }
            
            // Log template data for debugging
            error_log("Template found: ID={$template['id']}, Name={$template['template_name']}");
            
            // Verify content exists
            if (empty($template['content'])) {
                error_log("Warning: Template ID $templateId has empty content");
            }
            
            // Verify subject exists
            if (empty($template['subject'])) {
                error_log("Warning: Template ID $templateId has empty subject");
            }
            
            return $template;
        } catch (Exception $e) {
            error_log("Error getting template by ID $templateId: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Process a template with member data
     * @param string $content The template content
     * @param array $member The member data
     * @param int $daysUntilBirthday Days until birthday
     * @return string The processed content
     */
    private function processTemplate($content, $member, $daysUntilBirthday = 0) {
        // Make sure we have all the necessary fields for proper placeholder replacement
        if (!isset($member['first_name']) && isset($member['full_name'])) {
            $nameParts = explode(' ', $member['full_name'], 2);
            $member['first_name'] = $nameParts[0];
            $member['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
        }

        // Add recipient-specific placeholders for compatibility with bulk email templates
        $member['recipient_full_name'] = $member['full_name'] ?? '';
        $member['recipient_first_name'] = $member['first_name'] ?? '';
        $member['recipient_email'] = $member['email'] ?? '';
        $member['recipient_phone'] = $member['phone_number'] ?? '';

        // Add organization information from settings
        $member['organization_name'] = get_site_setting('organization_name', get_site_setting('site_title', 'Organization'));
        $member['organization_type'] = get_site_setting('organization_type', 'organization');
        $member['church_name'] = $member['organization_name']; // Legacy support

        // Determine which birth date field to use
        $birthDateField = !empty($member['birth_date']) ? 'birth_date' : 'date_of_birth';
        $birthDate = $member[$birthDateField] ?? null;

        // Calculate the correct birthday date for current year
        $birthdayDate = $this->getCorrectBirthdayDateFromBirthDate($birthDate);

        // Add birthday-specific placeholders
        $member['birthday_date'] = $birthDate ? date('F j', strtotime($birthDate)) : date('F j');
        $member['birthday_year'] = $birthDate ? date('Y', strtotime($birthDate)) : '';
        $member['current_year'] = date('Y');
        $member['current_date'] = date('F j, Y');
        $member['current_time'] = date('g:i A');
        $member['upcoming_birthday_date'] = date('F j, Y', strtotime($birthdayDate));
        $member['upcoming_birthday_day'] = date('l', strtotime($birthdayDate));
        $member['upcoming_birthday_formatted'] = date('l, F j, Y', strtotime($birthdayDate));
        $member['days_until_birthday'] = $daysUntilBirthday;
        $member['days_text'] = $daysUntilBirthday == 0 ? 'today' :
                              ($daysUntilBirthday == 1 ? 'tomorrow' : "in $daysUntilBirthday days");

        // Add celebration context for better grammar
        $member['celebration_text'] = $daysUntilBirthday == 0 ? 'celebrating today' :
                                    ($daysUntilBirthday == 1 ? 'celebrating tomorrow' :
                                    "celebrating in $daysUntilBirthday days");

        $member['birthday_context'] = $daysUntilBirthday == 0 ? 'on this special day' :
                                    ($daysUntilBirthday == 1 ? 'tomorrow' :
                                    "in $daysUntilBirthday days");

        // Ensure birthday member placeholders are set
        if (empty($member['birthday_member_full_name'])) {
            $member['birthday_member_full_name'] = $member['full_name'] ?? 'Member';
        }

        if (empty($member['birthday_member_name'])) {
            $member['birthday_member_name'] = $member['first_name'] ?? explode(' ', $member['birthday_member_full_name'])[0] ?? 'Member';
        }

        // Ensure birthday member email and other fields are set
        if (empty($member['birthday_member_email'])) {
            $member['birthday_member_email'] = $member['email'] ?? '';
        }

        // CRITICAL FIX: Add birthday_member_image_url placeholder for regular birthday emails
        // This ensures the placeholder is replaced even in regular birthday emails (not just notifications)
        if (!empty($member['image_path'])) {
            $imagePath = $member['image_path'];
            if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                // Already an absolute URL
                $photoUrl = $imagePath;
            } else {
                // Construct public URL
                $siteUrl = defined('SITE_URL') ? SITE_URL :
                    ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
                $photoUrl = $siteUrl . '/' . ltrim($imagePath, '/');
            }
            $member['birthday_member_image_url'] = $photoUrl;
            $member['birthday_member_photo_url'] = $photoUrl;
            $member['birthday_member_image'] = $photoUrl;
            $member['member_image_url'] = $photoUrl;
            error_log("Set birthday_member_image_url for regular birthday email: " . $photoUrl);
        } else {
            // Use default avatar
            $siteUrl = defined('SITE_URL') ? SITE_URL :
                ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
            $photoUrl = $siteUrl . '/assets/img/default-avatar.png';
            $member['birthday_member_image_url'] = $photoUrl;
            $member['birthday_member_photo_url'] = $photoUrl;
            $member['birthday_member_image'] = $photoUrl;
            $member['member_image_url'] = $photoUrl;
            error_log("Set birthday_member_image_url to default avatar for regular birthday email");
        }

        // Calculate age if birth date is available - use birthday age for birthday templates
        if (!empty($birthDate)) {
            $age = $this->calculateBirthdayAge($birthDate); // Use birthday age for consistency
            $member['birthday_member_age'] = $age;
            $member['age'] = $age;
            $member['age_ordinal'] = $this->getOrdinal($age);
            $member['birthday_member_age_ordinal'] = $this->getOrdinal($age);
            error_log("Using birthday age for template processing: $age (age they will be on their birthday)");
        }

        // Use the standardized placeholder replacement function (include member image for birthday emails)
        $content = replaceTemplatePlaceholders($content, $member, false);

        // Additional check for common patterns that might not be properly replaced
        // Fix "'s birthday" pattern if birthday_member_name is missing
        if (strpos($content, "'s birthday") !== false || strpos($content, "'s special day") !== false) {
            $firstName = $member['birthday_member_name'] ?? $member['first_name'] ?? 'Member';
            $content = str_replace(["'s birthday", "'s special day"], "$firstName's birthday", $content);
        }

        // Check for any remaining {placeholder} patterns and log them
        if (preg_match_all('/{([^}]+)}/', $content, $matches)) {
            error_log("Warning: Found unreplaced placeholders in template: " . implode(', ', $matches[0]));
        }

        return $content;
    }
    
    /**
     * Generate tracking URL
     * @param string $trackingId Unique tracking ID
     * @param int $memberId Member ID
     * @param bool $viewOnline Whether to include view online link
     * @return string Tracking URL
     */
    private function getTrackingUrl($trackingId, $memberId, $viewOnline = false) {
        // Use environment constant for the site URL
        $baseUrl = defined('SITE_URL') ? SITE_URL : 'http://localhost/church';
        
        // Build the URL with parameters
        $params = [
            'id' => urlencode($trackingId),
            'mid' => $memberId
        ];
        
        // Add view parameter if viewing online
        if ($viewOnline) {
            $params['view'] = '1';
        }
        
        // Build the query string
        $queryString = http_build_query($params);
        
        // Return the complete URL
        return $baseUrl . '/track.php?' . $queryString;
    }
    
    /**
     * Save tracking ID to database
     * @param int $memberId Member ID
     * @param string $trackingId Tracking ID
     * @param string $emailContent The email content
     */
    private function saveTrackingId($memberId, $trackingId, $emailContent = '') {
        try {
            // Check if email_content column exists
            $tableInfo = $this->pdo->query("DESCRIBE email_tracking");
            $columns = $tableInfo->fetchAll(PDO::FETCH_COLUMN);
            $hasEmailContent = in_array('email_content', $columns);
            
            // Prepare the query based on column existence
            if ($hasEmailContent) {
                $query = "INSERT INTO email_tracking (member_id, tracking_id, email_type, sent_at, email_content, opened_count) 
                         VALUES (?, ?, ?, NOW(), ?, 0)";
                $params = [$memberId, $trackingId, $this->emailType, $emailContent];
            } else {
                $query = "INSERT INTO email_tracking (member_id, tracking_id, email_type, sent_at, opened_count) 
                         VALUES (?, ?, ?, NOW(), 0)";
                $params = [$memberId, $trackingId, $this->emailType];
            }
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            
            error_log("Created tracking record: Member ID=$memberId, Tracking ID=$trackingId, Type={$this->emailType}");
            return true;
        } catch (Exception $e) {
            error_log("Error saving tracking ID: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send birthday emails to members
     * @param int|null $templateId Template ID to use (or null for default)
     * @param int $daysRange Days range to include (0 for today only, >0 for future days)
     * @param bool $specificDay Whether to send only to members with birthdays exactly on the day
     * @param array|null $testMembers Array of member data for testing (optional)
     * @return array Result statistics
     */
    public function sendBirthdayEmails($templateId = null, $daysRange = 0, $specificDay = false, $testMembers = null) {
        // Initialize email counters
        $this->sentEmails = [];
        $totalSent = 0;
        $totalFailed = 0;
        
        try {
            // Get the template
            $template = $this->getTemplateById($templateId);
            if (!$template) {
                error_log("No template found for birthday emails. Template ID: " . ($templateId ?? 'default'));
                return ['error' => 'No valid template found', 'total_sent' => 0, 'total_failed' => 0];
            }
            
            // Log template details
            error_log("Using template ID: " . $template['id'] . ", Name: " . $template['template_name']);
            
            // Use test members if provided, otherwise get from database
            if ($testMembers !== null) {
                $members = $testMembers;
                error_log("Using " . count($members) . " test members for birthday emails");
            } else {
                // Get all members with birthdays in the range
                $members = $this->getUpcomingBirthdays($daysRange, $specificDay);
                error_log("Found " . count($members) . " members with birthdays " . 
                        ($specificDay ? "exactly" : "within $daysRange days"));
            }
            
            if (empty($members)) {
                error_log("No members found with birthdays in the selected range.");
                return ['error' => 'No members found with birthdays in the selected range', 
                        'total_sent' => 0, 'total_failed' => 0];
            }
            
            // Process each member
            foreach ($members as $member) {
                // For any member without an email, we can't send
                if (empty($member['email'])) {
                    error_log("Member ID " . ($member['id'] ?? 'unknown') . " has no email address");
                    $totalFailed++;
                    continue;
                }
                
                // For test members, the days until birthday might not be set, default to 0
                $daysUntilBirthday = isset($member['days_until_birthday']) ? $member['days_until_birthday'] : 0;
                
                // Send the email
                $result = $this->sendEmail($member, $template, $daysUntilBirthday, 'birthday');
                if ($result) {
                    $totalSent++;
                } else {
                    error_log("Failed to send birthday email to: " . $member['email']);
                    $totalFailed++;
                }
            }
            
            // Return the results
            return [
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed,
                'success' => $totalSent > 0,
                'sent_emails' => $this->sentEmails
            ];
        } catch (Exception $e) {
            error_log("Error in sendBirthdayEmails: " . $e->getMessage());
            return [
                'error' => $e->getMessage(),
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed
            ];
        }
    }

    /**
     * Send birthday reminder emails to members with upcoming birthdays
     * @param int|null $templateId Specific template ID to use (or null for random)
     * @param int $daysAhead Number of days ahead to check for birthdays
     * @return array Results of the email sending
     */
    public function sendBirthdayReminders($templateId = null, $daysAhead = 3) {
        // Reset tracking arrays
        $this->failedEmails = [];
        $this->sentEmails = [];
        
        try {
            // Track statistics
            $totalSent = 0;
            $totalFailed = 0;
            
            // Get the template
            $reminderTemplate = null;
            
            // Check automated settings for reminder days (if none specified)
            if ($daysAhead == 3) { // Only override the default value
                try {
                    $stmt = $this->pdo->prepare("SELECT days_before FROM automated_emails_settings WHERE email_type = 'reminder'");
                    $stmt->execute();
                    $setting = $stmt->fetch();
                    
                    if ($setting && isset($setting['days_before'])) {
                        $daysAhead = (int)$setting['days_before'];
                        error_log("Using days_before from settings: $daysAhead days");
                    }
                } catch (Exception $e) {
                    error_log("Error fetching days_before setting: " . $e->getMessage());
                }
            }
            
            // If no specific template ID was provided, check automated settings
            if (!$templateId) {
                // Try to get a random template from automated settings
                $reminderTemplate = getRandomTemplateForType('reminder');
                if ($reminderTemplate) {
                    error_log("Using automated random reminder template: ID={$reminderTemplate['id']}, Name={$reminderTemplate['template_name']}");
                }
            } else {
                error_log("Fetching reminder template with ID: $templateId");
                $reminderTemplate = $this->getTemplateById($templateId);
            }
            
            // Fall back to the old method if no template was found
            if (!$reminderTemplate) {
                error_log("No template found in automated settings. Falling back to random selection.");
                $reminderTemplate = $this->getEmailTemplate(false);
            }
            
            if (!$reminderTemplate) {
                error_log("No valid reminder template found. Cannot send reminders.");
                return [
                    'total_sent' => 0,
                    'total_failed' => 0,
                    'error' => 'No valid template found'
                ];
            }
            
            error_log("Using reminder template: ID={$reminderTemplate['id']}, Name={$reminderTemplate['template_name']}");
            
            // Get ONLY members with birthdays exactly on the specified day
            $members = $this->getUpcomingBirthdays($daysAhead);
            
            error_log("Found " . count($members) . " members with birthdays in exactly $daysAhead days");
            
            foreach ($members as $member) {
                if (empty($member['email'])) {
                    error_log("Skipping member ID {$member['id']} - no email address");
                    continue;
                }
                
                $success = $this->sendEmail($member, $reminderTemplate, $daysAhead, 'birthday_reminder');
                
                if ($success) {
                    $totalSent++;
                    error_log("Successfully sent birthday reminder to member ID {$member['id']}, email: {$member['email']}");
                } else {
                    $totalFailed++;
                    error_log("Failed to send birthday reminder to member ID {$member['id']}, email: {$member['email']}");
                }
            }
            
            if (!empty($this->sentEmails) || !empty($this->failedEmails)) {
                $this->sendAdminNotification();
            }
            
            return [
                'sent' => $this->sentEmails,
                'failed' => $this->failedEmails,
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed
            ];
            
        } catch (Exception $e) {
            error_log("Exception in sendBirthdayReminders: " . $e->getMessage());
            return [
                'total_sent' => 0,
                'total_failed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send both birthday and reminder emails
     * @param int|null $birthdayTemplateId Specific birthday template ID
     * @param int|null $reminderTemplateId Specific reminder template ID
     * @param int $daysAhead Number of days ahead for reminders
     * @return array Results of the email sending
     */
    public function sendReminders($birthdayTemplateId = null, $reminderTemplateId = null, $daysAhead = 3) {
        // Reset tracking arrays
        $this->failedEmails = [];
        $this->sentEmails = [];
        
        try {
            // Ensure valid connection before starting
            $this->ensureConnection();
            
            // Keep track of members who have already received emails
            $processedMemberIds = [];
            $totalSent = 0;
            $totalFailed = 0;
            
            error_log("Starting combined birthday and reminder email process");
            
            // Check automated settings for reminder days (if none specified)
            if ($daysAhead == 3) { // Only override the default value
                try {
                    $stmt = $this->pdo->prepare("SELECT days_before FROM automated_emails_settings WHERE email_type = 'reminder'");
                    $stmt->execute();
                    $setting = $stmt->fetch();
                    
                    if ($setting && isset($setting['days_before'])) {
                        $daysAhead = (int)$setting['days_before'];
                        error_log("Using days_before from settings: $daysAhead days");
                    }
                } catch (Exception $e) {
                    error_log("Error fetching days_before setting: " . $e->getMessage());
                }
            }
            
            // Send birthday emails for today
            error_log("Processing birthday emails for today");
            $birthdayMembers = $this->getUpcomingBirthdays(0);
            error_log("Found " . count($birthdayMembers) . " members with birthdays today");
            
            // Get template - either specific or random
            $birthdayTemplate = null;
            if ($birthdayTemplateId) {
                error_log("Fetching birthday template with ID: $birthdayTemplateId");
                $birthdayTemplate = $this->getTemplateById($birthdayTemplateId);
            } else {
                // Try to get from automated settings first
                $birthdayTemplate = getRandomTemplateForType('birthday');
                if ($birthdayTemplate) {
                    error_log("Using automated random birthday template: ID={$birthdayTemplate['id']}, Name={$birthdayTemplate['template_name']}");
                } else {
                    error_log("Fetching random birthday template");
                    $birthdayTemplate = $this->getEmailTemplate(true);
                }
            }
            
            if (!$birthdayTemplate) {
                error_log("No birthday template found. Using regular template instead.");
                $birthdayTemplate = $this->getEmailTemplate(false);
            }
            
            if ($birthdayTemplate) {
                error_log("Using birthday template: ID={$birthdayTemplate['id']}, Name={$birthdayTemplate['template_name']}");
                
                foreach ($birthdayMembers as $member) {
                    // Validate member data
                    if (empty($member['email'])) {
                        error_log("Skipping member ID {$member['id']} - no email address");
                        continue;
                    }
                    
                    $success = $this->sendEmail($member, $birthdayTemplate, 0, 'birthday');
                    if ($success) {
                        $totalSent++;
                        error_log("Successfully sent birthday email to member ID {$member['id']}, email: {$member['email']}");
                    } else {
                        $totalFailed++;
                        error_log("Failed to send birthday email to member ID {$member['id']}, email: {$member['email']}");
                    }
                    $processedMemberIds[] = $member['id'];
                }
            } else {
                error_log("No valid birthday template available. Skipping birthday emails.");
            }
            
            // Send reminder for upcoming birthdays
            error_log("Processing birthday reminders for $daysAhead days ahead");
            $upcomingMembers = $this->getUpcomingBirthdays($daysAhead);
            error_log("Found " . count($upcomingMembers) . " members with birthdays in $daysAhead days");
            
            // Get template - either specific or random
            $reminderTemplate = null;
            if ($reminderTemplateId) {
                error_log("Fetching reminder template with ID: $reminderTemplateId");
                $reminderTemplate = $this->getTemplateById($reminderTemplateId);
            } else {
                // Try to get from automated settings first
                $reminderTemplate = getRandomTemplateForType('reminder');
                if ($reminderTemplate) {
                    error_log("Using automated random reminder template: ID={$reminderTemplate['id']}, Name={$reminderTemplate['template_name']}");
                } else {
                    error_log("Fetching random reminder template - prioritizing templates with member images");
                    // FIXED: Prioritize templates with {member_image} for better birthday notifications
                    $reminderTemplate = $this->getBirthdayNotificationTemplate();
                    if (!$reminderTemplate) {
                        error_log("No notification templates with member images found, falling back to any template");
                        $reminderTemplate = $this->getEmailTemplate(false);
                    }
                }
            }
            
            if (!$reminderTemplate) {
                error_log("No valid reminder template found. Skipping reminders.");
            } else {
                error_log("Using reminder template: ID={$reminderTemplate['id']}, Name={$reminderTemplate['template_name']}");
                
                foreach ($upcomingMembers as $member) {
                    // Skip if member already received a birthday email
                    if (in_array($member['id'], $processedMemberIds)) {
                        error_log("Skipping reminder for member ID {$member['id']} as they already received an email");
                        continue;
                    }
                    
                    // Validate member data
                    if (empty($member['email'])) {
                        error_log("Skipping member ID {$member['id']} - no email address");
                        continue;
                    }
                    
                    $success = $this->sendEmail($member, $reminderTemplate, $daysAhead, 'birthday_reminder');
                    if ($success) {
                        $totalSent++;
                        error_log("Successfully sent birthday reminder to member ID {$member['id']}, email: {$member['email']}");
                    } else {
                        $totalFailed++;
                        error_log("Failed to send birthday reminder to member ID {$member['id']}, email: {$member['email']}");
                    }
                    $processedMemberIds[] = $member['id'];
                }
            }
            
            // Send notifications to other members about upcoming birthdays
            error_log("Processing birthday notifications for other members");
            
            // Check automated settings for notification days
            try {
                $stmt = $this->pdo->prepare("SELECT days_before, template_ids FROM automated_emails_settings WHERE email_type = 'notification'");
                $stmt->execute();
                $notificationSettings = $stmt->fetch();
                
                if ($notificationSettings) {
                    $notificationDaysAhead = (int)$notificationSettings['days_before'];
                    
                    // Get upcoming birthdays for notification
                    $upcomingBirthdays = $this->getUpcomingBirthdays($notificationDaysAhead);
                    error_log("Found " . count($upcomingBirthdays) . " members with birthdays in $notificationDaysAhead days for notifications");
                    
                    if (!empty($upcomingBirthdays)) {
                        // Get notification template
                        $notificationTemplate = getRandomTemplateForType('notification');
                        
                        if ($notificationTemplate) {
                            error_log("Using notification template: ID={$notificationTemplate['id']}, Name={$notificationTemplate['template_name']}");
                            
                            // Get all members to notify
                            $stmt = $this->pdo->prepare("SELECT * FROM members WHERE email IS NOT NULL AND email != ''");
                            $stmt->execute();
                            $allMembers = $stmt->fetchAll();
                            
                            foreach ($upcomingBirthdays as $birthdayMember) {
                                foreach ($allMembers as $member) {
                                    // Skip if this is the birthday member
                                    if ($member['id'] == $birthdayMember['id']) {
                                        continue;
                                    }
                                    
                                    // Send notification email
                                    $success = $this->sendEmail(
                                        $member,
                                        $notificationTemplate,
                                        $notificationDaysAhead,
                                        'b_notification',
                                        $birthdayMember
                                    );
                                    
                                    if ($success) {
                                        $totalSent++;
                                        error_log("Successfully sent birthday notification about {$birthdayMember['full_name']} to member ID {$member['id']}, email: {$member['email']}");
                                    } else {
                                        $totalFailed++;
                                        error_log("Failed to send birthday notification about {$birthdayMember['full_name']} to member ID {$member['id']}, email: {$member['email']}");
                                    }
                                }
                            }
                        } else {
                            error_log("No valid notification template found. Skipping notifications to other members.");
                        }
                    } else {
                        error_log("No upcoming birthdays found for notifications.");
                    }
                } else {
                    error_log("No notification settings found. Skipping notifications to other members.");
                }
            } catch (Exception $e) {
                error_log("Error processing notifications: " . $e->getMessage());
            }
            
            // Send admin notification if there were any emails sent
            if (!empty($this->sentEmails) || !empty($this->failedEmails)) {
                error_log("Sending admin notification with results");
                $this->sendAdminNotification();
            }
            
            error_log("Completed combined email process. Total sent: $totalSent, Total failed: $totalFailed");
            
            return [
                'sent' => $this->sentEmails,
                'failed' => $this->failedEmails,
                'total_sent' => $totalSent,
                'total_failed' => $totalFailed
            ];
        } catch (Exception $e) {
            error_log("Exception in sendReminders: " . $e->getMessage());
            return [
                'total_sent' => 0,
                'total_failed' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send email to a member
     * @param array $member Member data
     * @param array $template Email template
     * @param int $daysUntilBirthday Days until birthday (0 for birthday emails)
     * @param string $emailType Type of email (birthday or birthday_reminder)
     * @return bool Whether the email was sent successfully
     */
    private function sendEmail($member, $template, $daysUntilBirthday = 0, $emailType = 'birthday', $birthdayMember = null) {
        try {
            // Set the email type for tracking purposes
            $this->emailType = $emailType;
            
            // Log the email type being processed
            error_log("Processing email type: $emailType for member ID {$member['id']}");
            
            // Validate the template format
            if (!isset($template['subject']) || !isset($template['content'])) {
                error_log("Invalid template format for email type: $emailType");
                return false;
            }
            
            // Validate member has required fields
            if (!isset($member['email']) || empty($member['email'])) {
                error_log("No email address for member in email type: $emailType");
                return false;
            }
            
            // Extract member name for greeting
            $memberName = isset($member['full_name']) ? $member['full_name'] : 'Member';
            $firstName = isset($member['first_name']) ? $member['first_name'] : (explode(' ', $memberName)[0] ?? 'Member');
            
            // Get member ID for tracking
            $memberId = $member['id'] ?? 0;
            
            // Start with the raw template content
            $content = is_array($template) ? $template['content'] : $template;
            $subject = is_array($template) ? $template['subject'] : 'Church Notification';
            
            // Process content based on email type
            if ($emailType == 'birthday_notification' || $emailType == 'b_notification') {
                // Make sure we have the birthday member data
                if (!$birthdayMember) {
                    error_log("Error: Birthday member data missing for $emailType email");
                    return false;
                }

                // Log the email processing for debugging
                error_log("Processing notification email to {$member['full_name']} about {$birthdayMember['full_name']}'s birthday");
                
                // These types need special processing with the birthday member as context
                $subject = $this->processBirthdayMemberTemplate($subject, $member, $birthdayMember, $daysUntilBirthday);
                $content = $this->processBirthdayMemberTemplate($content, $member, $birthdayMember, $daysUntilBirthday);
            } 
            elseif ($emailType == 'birthday' || $emailType == 'birthday_reminder') {
                // Regular birthday emails - process with standard template
                $content = $this->processTemplate($content, $member, $daysUntilBirthday);
                $subject = $this->sanitizeSubjectLine($subject, $member);
            }
            else {
                // For any other type, just do standard processing
                $content = $this->processTemplate($content, $member, $daysUntilBirthday);
                
                // Simple subject sanitization
                if (strpos($subject, '{member_name}') !== false) {
                    $subject = str_replace('{member_name}', $firstName, $subject);
                }
                
                // If we have a birthday member for this email type, pass it to sanitizeSubjectLine
                if ($birthdayMember) {
                    $subject = $this->sanitizeSubjectLine($subject, $member, $birthdayMember);
                } else {
                    $subject = $this->sanitizeSubjectLine($subject, $member);
                }
            }
            
            // Create tracking ID
            $trackingId = uniqid('email_', true);
            
            // Add tracking pixel and save tracking data if enabled
            if ($this->trackingEnabled) {
                // Save to database
                $this->saveTrackingId($memberId, $trackingId, $content);
                
                // Generate tracking pixel HTML
                $trackingUrl = $this->getTrackingUrl($trackingId, $memberId);
                $trackingPixel = '<img src="' . $trackingUrl . '" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">';
                
                // Replace tracking pixel placeholder if it exists
                if (strpos($content, '{tracking_pixel}') !== false) {
                    $content = str_replace('{tracking_pixel}', $trackingPixel, $content);
                } else {
                    // No placeholder, append to the end before </body> if HTML
                    if (stripos($content, '</body>') !== false) {
                        $content = str_replace('</body>', $trackingPixel . '</body>', $content);
                    } else {
                        // If not HTML format, just append to the end
                        $content .= "\n\n" . $trackingPixel;
                    }
                }
            } else {
                // If tracking is disabled, remove the placeholder
                $content = str_replace('{tracking_pixel}', '', $content);
            }
            
            // Create additional data for email delivery
            $memberData = $member;

            // Add organization information
            $memberData['organization_name'] = get_site_setting('organization_name', get_site_setting('site_title', 'Organization'));
            $memberData['organization_type'] = get_site_setting('organization_type', 'organization');
            $memberData['church_name'] = $memberData['organization_name']; // Legacy support

            // Add email template info if available
            if (is_array($template) && isset($template['template_name'])) {
                $memberData['template_name'] = $template['template_name'];
            }
            
            // If this is a birthday-related email (notification to others OR reminder to birthday person), include birthday member info
            if (($emailType == 'birthday_notification' || $emailType == 'b_notification') && $birthdayMember) {
                // Override subject to ensure consistent format regardless of template
                $birthdayMemberFirstName = isset($birthdayMember['full_name']) ? 
                    explode(' ', $birthdayMember['full_name'])[0] : 'Member';
                $subject = "Birthday Celebration! {$birthdayMemberFirstName}";
                error_log("Enforcing clean birthday notification subject: $subject");
                
                // Add birthday member data
                $memberData['birthday_member_name'] = isset($birthdayMember['full_name']) ?
                    explode(' ', $birthdayMember['full_name'])[0] : 'Member';
                $memberData['birthday_member_full_name'] = $birthdayMember['full_name'] ?? 'Member';
                $memberData['birthday_member_email'] = $birthdayMember['email'] ?? '';
                $memberData['birthday_member_phone'] = $birthdayMember['phone_number'] ?? '';
                $memberData['birthday_member_birth_date'] = $birthdayMember['birth_date'] ?? '';

                // CRITICAL FIX: Add explicit flag to indicate this is a birthday notification
                // This will help the security check in config.php properly identify birthday notifications
                $memberData['_is_birthday_notification'] = true;

                // CRITICAL FIX: Ensure recipient email is included for security validation
                $memberData['email'] = $member['email'];
                $memberData['recipient_email'] = $member['email'];
                
                // FIXED: Add the birthday member photo URL with public accessibility checks
                if (!empty($birthdayMember['image_path'])) {
                    // Store the birthday member's original path for image embedding (separate from recipient's path)
                    $memberData['_birthday_member_original_image_path'] = $birthdayMember['image_path'];

                    $imagePath = $birthdayMember['image_path'];

                    if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                        // Already an absolute URL
                        $photoUrl = $imagePath;
                    } else {
                        // Generate the full URL for the photo - CRITICAL: Must be publicly accessible
                        $siteUrl = defined('SITE_URL') ? SITE_URL :
                            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);

                        // CRITICAL: Check for localhost usage that will break email delivery
                        if (strpos($siteUrl, 'localhost') !== false || strpos($siteUrl, '127.0.0.1') !== false) {
                            error_log("CRITICAL WARNING: SITE_URL uses localhost ($siteUrl) - images will NOT be accessible in delivered emails! Update SITE_URL to your public domain.");
                        }

                        $photoUrl = $siteUrl . '/' . ltrim($imagePath, '/');
                    }

                    $memberData['birthday_member_photo_url'] = $photoUrl;
                    $memberData['birthday_member_image'] = $photoUrl;
                    $memberData['birthday_member_image_url'] = $photoUrl; // CRITICAL: Add missing placeholder

                    // Store the photo URL for the {member_image} placeholder
                    // The email processing will handle embedding and HTML generation
                    $memberData['member_image_url'] = $photoUrl;

                    // CRITICAL FIX: Set member_image to URL for now - the sendEmail function will convert to HTML
                    // This ensures compatibility with the image embedding logic in config.php
                    $memberData['member_image'] = $photoUrl;

                    error_log("Birthday member photo URL (must be publicly accessible): " . $photoUrl);

                    // Store the birthday member's original image path for email embedding
                    $memberData['_birthday_member_original_image_path'] = $birthdayMember['image_path'];
                } else {
                    // Use default avatar
                    $siteUrl = defined('SITE_URL') ? SITE_URL :
                        ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);

                    $photoUrl = $siteUrl . '/assets/img/default-avatar.png';
                    $memberData['birthday_member_photo_url'] = $photoUrl;
                    $memberData['birthday_member_image'] = $photoUrl;
                    $memberData['birthday_member_image_url'] = $photoUrl; // CRITICAL: Add missing placeholder

                    // Store the default photo URL for the {member_image} placeholder
                    // The email processing will handle embedding and HTML generation
                    $memberData['member_image_url'] = $photoUrl;

                    // CRITICAL FIX: Set member_image to URL for now - the sendEmail function will convert to HTML
                    // This ensures compatibility with the image embedding logic in config.php
                    $memberData['member_image'] = $photoUrl;

                    // Store the birthday member's original image path for email embedding
                    $memberData['_birthday_member_original_image_path'] = 'assets/img/default-avatar.png';

                    error_log("Using default photo for birthday member");
                }
                
                // Calculate birthday member age if not already provided
                if (!isset($birthdayMember['age']) && !empty($birthdayMember['birth_date'])) {
                    $memberData['birthday_member_age'] = $this->calculateAge($birthdayMember['birth_date']);
                } else if (isset($birthdayMember['age'])) {
                    $memberData['birthday_member_age'] = $birthdayMember['age'];
                }
                
                // Ensure member_name is set correctly for the recipient
                $memberData['member_name'] = $firstName;

                // CRITICAL FIX: For birthday notifications, preserve both birthday member AND recipient image paths
                // Set recipient's image path for email embedding (separate from birthday member's image)
                if (!empty($member['image_path'])) {
                    $memberData['_recipient_original_image_path'] = $member['image_path'];
                    error_log("Birthday notification: Set recipient image path: " . $member['image_path']);
                }

                // CRITICAL FIX: For birthday notifications, the primary _original_image_path should be the BIRTHDAY MEMBER's image
                // This ensures the birthday member's image embeds correctly in the email, not the recipient's image
                if (!empty($birthdayMember['image_path'])) {
                    $memberData['_original_image_path'] = $birthdayMember['image_path'];
                    error_log("Birthday notification: Set birthday member image path for embedding: " . $birthdayMember['image_path']);
                } else {
                    $memberData['_original_image_path'] = 'assets/img/default-avatar.png';
                    error_log("Birthday notification: Using default avatar for birthday member image embedding");
                }
            }
            // CRITICAL FIX: Handle birthday_reminder emails (sent to the birthday person themselves)
            else if ($emailType == 'birthday_reminder') {
                // For birthday reminders, the member IS the birthday member
                $memberData['birthday_member_name'] = $firstName;
                $memberData['birthday_member_full_name'] = $member['full_name'] ?? 'Member';
                $memberData['birthday_member_email'] = $member['email'] ?? '';
                $memberData['birthday_member_phone'] = $member['phone_number'] ?? '';
                $memberData['birthday_member_birth_date'] = $member['birth_date'] ?? '';

                // CRITICAL FIX: Add explicit flag to indicate this is a birthday reminder
                $memberData['_is_birthday_notification'] = true;
                $memberData['email'] = $member['email'];
                $memberData['recipient_email'] = $member['email'];

                // FIXED: Add the birthday member photo URL (which is the same as the recipient's photo)
                if (!empty($member['image_path'])) {
                    $imagePath = $member['image_path'];

                    if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                        // Already an absolute URL
                        $photoUrl = $imagePath;
                    } else {
                        // Generate the full URL for the photo
                        $siteUrl = defined('SITE_URL') ? SITE_URL :
                            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
                        $photoUrl = $siteUrl . '/' . ltrim($imagePath, '/');
                    }

                    $memberData['birthday_member_photo_url'] = $photoUrl;
                    $memberData['birthday_member_image'] = $photoUrl;
                    $memberData['birthday_member_image_url'] = $photoUrl;
                    $memberData['member_image_url'] = $photoUrl;
                    $memberData['member_image'] = $photoUrl;

                    // Set the original image path for embedding
                    $memberData['_original_image_path'] = $member['image_path'];
                    $memberData['_birthday_member_original_image_path'] = $member['image_path'];

                    error_log("Birthday reminder: Set birthday member image path for embedding: " . $member['image_path']);
                } else {
                    // Use default avatar
                    $siteUrl = defined('SITE_URL') ? SITE_URL :
                        ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
                    $photoUrl = $siteUrl . '/assets/img/default-avatar.png';

                    $memberData['birthday_member_photo_url'] = $photoUrl;
                    $memberData['birthday_member_image'] = $photoUrl;
                    $memberData['birthday_member_image_url'] = $photoUrl;
                    $memberData['member_image_url'] = $photoUrl;
                    $memberData['member_image'] = $photoUrl;

                    $memberData['_original_image_path'] = 'assets/img/default-avatar.png';
                    $memberData['_birthday_member_original_image_path'] = 'assets/img/default-avatar.png';

                    error_log("Birthday reminder: Using default avatar for birthday member image embedding");
                }

                // Calculate birthday member age if not already provided
                if (!empty($member['birth_date'])) {
                    $memberData['birthday_member_age'] = $this->calculateAge($member['birth_date']);
                }

                // Ensure member_name is set correctly
                $memberData['member_name'] = $firstName;
            }
            else {
                // For regular emails, ensure member_name is set
                $memberData['member_name'] = $firstName;

                // Set original image path for regular emails (always set recipient's image path)
                if (!empty($member['image_path'])) {
                    $memberData['_original_image_path'] = $member['image_path'];
                    error_log("Regular email: Set recipient image path for embedding: " . $member['image_path']);
                }
            }
            
            // Set the days text for context
            $memberData['days_text'] = $this->getDaysText($daysUntilBirthday);

            // CRITICAL FIX: Add birthday notification flag for proper email processing
            if ($emailType == 'birthday_notification' || $emailType == 'b_notification' || $emailType == 'birthday_reminder' || $emailType == 'birthday') {
                $memberData['_is_birthday_notification'] = true;
                $memberData['_skip_attachments'] = true; // Prevent unwanted attachments like receipt images
                error_log("Setting _is_birthday_notification and _skip_attachments flags for email type: $emailType");
            }
            
            // Final check on subject line to ensure no concatenated names
            if (($emailType == 'birthday_notification' || $emailType == 'b_notification') && $birthdayMember) {
                $birthdayFirstName = isset($birthdayMember['full_name']) ? 
                    explode(' ', $birthdayMember['full_name'])[0] : 'Member';
                    
                // Check for common patterns that might indicate name concatenation
                if (preg_match('/\b([A-Z][a-z]+)\s+([A-Z][a-z]+)\b/i', $subject)) {
                    $subject = preg_replace('/\b([A-Z][a-z]+)\s+([A-Z][a-z]+)\b/i', $birthdayFirstName, $subject);
                }
                
                // Specific fix for the "Name us!" pattern seen in the user's example
                $recipientFirstName = isset($member['full_name']) ? explode(' ', $member['full_name'])[0] : '';
                $patterns = [
                    '/\b(' . preg_quote($birthdayFirstName, '/') . ')\s+us\b/i',
                    '/\b(' . preg_quote($recipientFirstName, '/') . ')\s+us\b/i'
                ];
                $replacements = ['$1', '$1'];
                $subject = preg_replace($patterns, $replacements, $subject);
                
                // Also fix other common patterns
                $subject = str_replace(
                    [
                        $birthdayFirstName . "'s " . $birthdayFirstName,
                        $birthdayFirstName . "'s" . $birthdayFirstName,
                        $birthdayFirstName . '!' . $birthdayFirstName,
                        $birthdayFirstName . ' ' . $birthdayFirstName
                    ],
                    [
                        $birthdayFirstName . "'s",
                        $birthdayFirstName . "'s",
                        $birthdayFirstName . '!',
                        $birthdayFirstName
                    ],
                    $subject
                );
                
                // If we have a complete mess, just use a clean standard format
                if (substr_count(strtolower($subject), strtolower($birthdayFirstName)) > 1) {
                    $subject = "Birthday Celebration! {$birthdayFirstName}";
                }
                
                // Remove any problematic emoji characters that might cause encoding issues
                $subject = preg_replace('/[\x{10000}-\x{10FFFF}]/u', '', $subject);
                $subject = preg_replace('/[\xF0-\xFF][\x80-\xBF]{3}/', '', $subject);
                $subject = preg_replace('/ðŸŽ‰|ðŸŽ‚|ðŸŽ|ðŸ‚/', '', $subject);
                
                // Log the final processed subject
                error_log("Final processed notification subject: '$subject'");
                
                // REMOVED: Duplicate image processing - let global sendEmail() handle image embedding properly
                
                // Check for broken image tags showing as text
                if (preg_match('/' . preg_quote($birthdayFirstName, '/') . '"(\s+alt="[^"]*")?\s+class="[^"]*"/', $content)) {
                    error_log("Found broken image tag in final content, fixing it");
                    
                    // Get the profile image URL
                    $photoUrl = '';
                    if (!empty($birthdayMember['image_path'])) {
                        $siteUrl = defined('SITE_URL') ? SITE_URL : 
                            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
                        $photoUrl = $siteUrl . '/' . ltrim($birthdayMember['image_path'], '/');
                    } else {
                        $siteUrl = defined('SITE_URL') ? SITE_URL : 
                            ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
                        $photoUrl = $siteUrl . '/assets/img/default-avatar.png';
                    }
                    
                    // Create embedded image HTML
                    // CRITICAL FIX: Use birthday member's name directly, not from memberData which contains recipient data
                    $birthdayMemberName = $birthdayMember['full_name'] ?? 'Birthday Member';
                    $imageHtml = '<img src="' . $photoUrl . '" alt="' .
                        htmlspecialchars($birthdayMemberName) .
                        '" style="display:block; width:200px; height:200px; border-radius:50%; margin:15px auto; object-fit:cover; border:4px solid #fff; box-shadow:0 4px 12px rgba(0,0,0,0.1);">';
                    
                    // Replace the broken tag with proper HTML
                    $content = preg_replace(
                        '/' . preg_quote($birthdayFirstName, '/') . '"(\s+alt="[^"]*")?\s+class="[^"]*"/', 
                        $imageHtml,
                        $content
                    );
                }
                
                // Final check for any other image-related issues
                if (preg_match('/Member"(\s+alt="[^"]*")?\s+class="[^"]*"/', $content)) {
                    error_log("Found generic Member broken image tag in final content, fixing it");
                    
                    // Get default image URL
                    $siteUrl = defined('SITE_URL') ? SITE_URL : 
                        ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
                    $photoUrl = $siteUrl . '/assets/img/default-avatar.png';
                    
                    // Create embedded image HTML
                    // CRITICAL FIX: Use birthday member's name for alt text
                    $birthdayMemberName = $birthdayMember['full_name'] ?? 'Birthday Member';
                    $imageHtml = '<img src="' . $photoUrl . '" alt="' . htmlspecialchars($birthdayMemberName) . '" ' .
                        'style="display:block; width:200px; height:200px; border-radius:50%; margin:15px auto; object-fit:cover; border:4px solid #fff; box-shadow:0 4px 12px rgba(0,0,0,0.1);">';
                    
                    // Replace the broken tag with proper HTML
                    $content = preg_replace(
                        '/Member"(\s+alt="[^"]*")?\s+class="[^"]*"/', 
                        $imageHtml,
                        $content
                    );
                }
            }
            
            // Send the email using the global sendEmail function from config.php
            // Add birthday notification flag to memberData for proper image handling
            $memberDataWithFlag = $memberData;
            $memberDataWithFlag['is_birthday_notification'] = ($emailType === 'birthday_notification' || $emailType === 'b_notification');

            $result = sendEmail(
                $member['email'],
                $memberName,
                $subject,
                $content,
                true, // HTML format
                $memberDataWithFlag
            );
            
            if ($result) {
                // Log successful email in database
                try {
                    // Get a fresh database connection for logging
                    $conn = null;
                    if (function_exists('getDbConnection')) {
                        $conn = getDbConnection();
                    }
                    
                    if ($conn instanceof PDO) {
                        // Debug output
                        error_log("DEBUG: About to log email in database with values:");
                        error_log("DEBUG: Member ID: $memberId");
                        error_log("DEBUG: Template ID: " . (is_array($template) ? ($template['id'] ?? 0) : 0));
                        error_log("DEBUG: Email Type: " . ($emailType === 'notification' ? 'birthday_notification' : 'birthday'));
                        error_log("DEBUG: Subject: '$subject'");
                        
                        // Prepare the statement
                        $stmt = $conn->prepare("
                            INSERT INTO email_logs 
                            (member_id, template_id, email_type, subject, status) 
                            VALUES (?, ?, ?, ?, ?)
                        ");
                        
                        $emailTypeValue = $emailType === 'notification' ? 'birthday_notification' : 'birthday';
                        $templateId = is_array($template) ? ($template['id'] ?? 0) : 0;
                        
                        // Execute with values
                        $logResult = $stmt->execute([
                            $memberId,
                            $templateId,
                            $emailTypeValue,
                            $subject,
                            'success'
                        ]);
                        
                        // Get the insert ID to confirm
                        if ($logResult) {
                            $insertId = $conn->lastInsertId();
                            error_log("Email logged in database: member_id=$memberId, subject='$subject', type='$emailTypeValue', log_id=$insertId");
                        } else {
                            error_log("Failed to insert email log: " . print_r($stmt->errorInfo(), true));
                        }
                    } else {
                        error_log("Could not log email to database - no valid connection available");
                        
                        // Try using our internal logging method as a fallback
                        $this->logEmailSent($memberId, is_array($template) ? ($template['id'] ?? 0) : 0, 'success', null, $emailType);
                    }
                } catch (Exception $e) {
                    error_log("Failed to log email in database: " . $e->getMessage());
                    
                    // Try using our internal logging method as a fallback
                    $this->logEmailSent($memberId, is_array($template) ? ($template['id'] ?? 0) : 0, 'success', null, $emailType);
                }
                
                $this->sentEmails[] = [
                    'member_id' => $memberId,
                    'email' => $member['email'],
                    'name' => $memberName,
                    'template_id' => is_array($template) ? ($template['id'] ?? 0) : 0,
                    'type' => $emailType
                ];
                error_log("Successfully sent $emailType email to: {$member['email']}");
            } else {
                global $last_email_error;
                
                $this->failedEmails[] = [
                    'member_id' => $memberId,
                    'email' => $member['email'],
                    'name' => $memberName,
                    'template_id' => is_array($template) ? ($template['id'] ?? 0) : 0,
                    'type' => $emailType,
                    'error' => $last_email_error ?? 'Unknown error'
                ];
                
                error_log("Failed to send $emailType email to {$member['email']}: " . ($last_email_error ?? 'Unknown error'));
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Exception sending $emailType email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Set the email type to use when logging emails
     * @param string $type Email type to use (max 20 characters)
     */
    public function setEmailType($type) {
        // Ensure the type is not too long for the database column
        $this->emailType = substr($type, 0, 20);
    }

    private function logEmailSent($memberId, $templateId, $status, $errorMessage = null, $emailType = null) {
        try {
            // Use the instance email type if none provided
            if ($emailType === null) {
                $emailType = $this->emailType;
            }
            
            // Log email sending attempt
            error_log("Logging email sent to member ID $memberId with status $status, email_type: $emailType");
            
            // Ensure we have a valid database connection
            if (!($this->pdo instanceof PDO)) {
                error_log("WARNING: Invalid PDO connection in logEmailSent. Attempting to get a new connection.");
                if (function_exists('getDbConnection')) {
                    $this->pdo = getDbConnection();
                    error_log("Successfully established database connection in logEmailSent.");
                } else {
                    error_log("ERROR: getDbConnection function does not exist. Cannot log email.");
                    return false;
                }
            }
            
            // Prepare and execute the query
            $stmt = $this->pdo->prepare("INSERT INTO email_logs (member_id, template_id, email_type, status, sent_at, error_message) 
                                      VALUES (?, ?, ?, ?, NOW(), ?)");
            $result = $stmt->execute([$memberId, $templateId, $emailType, $status, $errorMessage]);
            
            // Verify the insert worked
            if ($result) {
                $logId = $this->pdo->lastInsertId();
                error_log("Email successfully logged with ID $logId for member $memberId");
                return true;
            } else {
                error_log("Failed to insert log record, error info: " . print_r($stmt->errorInfo(), true));
                return false;
            }
        } catch (Exception $e) {
            error_log("Error logging email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send notification to admin about sent and failed emails
     * @return bool Whether the notification was sent successfully
     */
    private function sendAdminNotification() {
        if (empty($this->adminEmail)) {
            error_log("Admin email not set, skipping notification");
            return false;
        }
        
        $totalSent = count($this->sentEmails);
        $totalFailed = count($this->failedEmails);
        
        $subject = "Birthday Email Report: $totalSent sent, $totalFailed failed";
        
        // Create HTML email body
        $body = '<!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                h2, h3 { color: #333; margin-top: 20px; }
                .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .success { color: #28a745; }
                .error { color: #dc3545; }
            </style>
        </head>
        <body>
            <h2>Birthday Email Report</h2>
            <div class="summary">
                <p>This is an automated report of birthday emails sent by the system.</p>
                <p><strong>Total Sent:</strong> <span class="success">' . $totalSent . '</span></p>
                <p><strong>Total Failed:</strong> <span class="error">' . $totalFailed . '</span></p>
                <p><strong>Date/Time:</strong> ' . date('Y-m-d H:i:s') . '</p>
            </div>';
        
        // Show sent emails
        $body .= '<h3>Sent Emails: ' . $totalSent . '</h3>';
        if (!empty($this->sentEmails)) {
            $body .= '<table border="1" cellpadding="5" style="border-collapse: collapse;">';
            $body .= '<tr><th>Member</th><th>Email</th><th>Template</th><th>Type</th></tr>';
            
            foreach ($this->sentEmails as $email) {
                // Ensure all keys exist with default values
                $memberName = isset($email['member']) ? htmlspecialchars($email['member']) : 'Unknown';
                $memberEmail = isset($email['email']) ? htmlspecialchars($email['email']) : 'No email';
                $templateName = isset($email['template_name']) ? htmlspecialchars($email['template_name']) : 'Unknown Template';
                $emailType = isset($email['type']) ? htmlspecialchars($email['type']) : 'Unknown';
                
                $body .= '<tr>';
                $body .= '<td>' . $memberName . '</td>';
                $body .= '<td>' . $memberEmail . '</td>';
                $body .= '<td>' . $templateName . '</td>';
                $body .= '<td>' . $emailType . '</td>';
                $body .= '</tr>';
            }
            
            $body .= '</table>';
        } else {
            $body .= '<p>No emails were sent successfully.</p>';
        }
        
        // Show failed emails
        $body .= '<h3>Failed Emails: ' . $totalFailed . '</h3>';
        if (!empty($this->failedEmails)) {
            $body .= '<table border="1" cellpadding="5" style="border-collapse: collapse;">';
            $body .= '<tr><th>Member</th><th>Email</th><th>Template</th><th>Type</th><th>Error</th></tr>';
            
            foreach ($this->failedEmails as $email) {
                // Ensure all keys exist with default values
                $memberName = isset($email['member']) ? htmlspecialchars($email['member']) : 'Unknown';
                $memberEmail = isset($email['email']) ? htmlspecialchars($email['email']) : 'No email';
                $templateName = isset($email['template_name']) ? htmlspecialchars($email['template_name']) : 'Unknown Template';
                $emailType = isset($email['type']) ? htmlspecialchars($email['type']) : 'Unknown';
                $errorMsg = isset($email['error']) ? htmlspecialchars($email['error']) : 'Unknown error';
                
                $body .= '<tr>';
                $body .= '<td>' . $memberName . '</td>';
                $body .= '<td>' . $memberEmail . '</td>';
                $body .= '<td>' . $templateName . '</td>';
                $body .= '<td>' . $emailType . '</td>';
                $body .= '<td>' . $errorMsg . '</td>';
                $body .= '</tr>';
            }
            
            $body .= '</table>';
        } else {
            $body .= '<p>No emails failed to send.</p>';
        }
        
        $body .= '</body></html>';
        
        // Send the notification email
        try {
            $result = sendEmail(
                $this->adminEmail,
                'Admin',
                $subject,
                $body,
                true
            );
            
            if ($result) {
                error_log("Admin notification email sent successfully to {$this->adminEmail}");
            } else {
                error_log("Failed to send admin notification email to {$this->adminEmail}");
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error sending admin notification: " . $e->getMessage());
            return false;
        }
    }

    // Getter methods for sent and failed emails
    public function getSentEmails() {
        return $this->sentEmails;
    }
    
    public function getFailedEmails() {
        return $this->failedEmails;
    }

    /**
     * Get a summary of upcoming birthdays for the next X days
     * Returns counts and details for each day
     * 
     * @param int $daysToCheck Number of days to check for upcoming birthdays
     * @return array Summary of birthdays by day
     */
    public function getBirthdaySummary($daysToCheck = 14) {
        try {
            $summary = [
                'total_count' => 0,
                'days' => []
            ];
            
            // Check each day
            for ($day = 0; $day <= $daysToCheck; $day++) {
                // Get members with birthdays on this day
                $members = $this->getUpcomingBirthdays($day);
                $count = count($members);
                
                // Format the date for display
                $date = date('Y-m-d', strtotime("+$day days"));
                $dateFormatted = date('l, F j, Y', strtotime("+$day days"));
                
                // Create day summary
                $daySummary = [
                    'date' => $date,
                    'date_formatted' => $dateFormatted,
                    'days_ahead' => $day,
                    'count' => $count,
                    'members' => []
                ];
                
                // Include basic member information
                foreach ($members as $member) {
                    $daySummary['members'][] = [
                        'id' => $member['id'],
                        'name' => $member['full_name'] ?? 'Unknown',
                        'email' => $member['email'] ?? 'No email',
                        'birth_date' => $member['birth_date'] ?? 'Unknown'
                    ];
                }
                
                // Add to summary
                $summary['days'][$day] = $daySummary;
                $summary['total_count'] += $count;
            }
            
            return $summary;
        } catch (Exception $e) {
            error_log("Error in getBirthdaySummary: " . $e->getMessage());
        return [
                'error' => $e->getMessage(),
                'total_count' => 0,
                'days' => []
            ];
        }
    }

    /**
     * Test function to check if emails would be sent correctly
     * Returns details about members who would receive emails without actually sending
     * 
     * @param int $daysRange Range of days to check for today's birthdays
     * @param int $reminderDays Days ahead to check for reminder emails
     * @return array Results of the email check
     */
    public function testBirthdayEmails($daysRange = 0, $reminderDays = 3) {
        try {
            $results = [
                'birthday_emails' => [],
                'reminder_emails' => [],
                'birthday_count' => 0,
                'reminder_count' => 0,
                'total_count' => 0
            ];
            
            // Get birthday members (today through specified range)
            $processedIds = [];
            for ($day = 0; $day <= $daysRange; $day++) {
                $members = $this->getUpcomingBirthdays($day);
                foreach ($members as $member) {
                    if (empty($member['email'])) continue;
                    
                    $results['birthday_emails'][] = [
                        'id' => $member['id'],
                        'name' => $member['full_name'] ?? 'Unknown',
                        'email' => $member['email'],
                        'days_ahead' => $day,
                        'type' => 'birthday',
                        'date' => date('Y-m-d', strtotime("+$day days"))
                    ];
                    
                    $processedIds[] = $member['id'];
                    $results['birthday_count']++;
                }
            }
            
            // Get reminder members (for specified days ahead)
            $upcomingMembers = $this->getUpcomingBirthdays($reminderDays);
            foreach ($upcomingMembers as $member) {
                // Skip if already getting a birthday email
                if (in_array($member['id'], $processedIds)) continue;
                
                // Skip if no email
                if (empty($member['email'])) continue;
                
                $results['reminder_emails'][] = [
                    'id' => $member['id'],
                    'name' => $member['full_name'] ?? 'Unknown',
                    'email' => $member['email'],
                    'days_ahead' => $reminderDays,
                    'type' => 'birthday_reminder',
                    'date' => date('Y-m-d', strtotime("+$reminderDays days"))
                ];
                
                $results['reminder_count']++;
            }
            
            $results['total_count'] = $results['birthday_count'] + $results['reminder_count'];
            
            return $results;
        } catch (Exception $e) {
            error_log("Error in testBirthdayEmails: " . $e->getMessage());
            return [
                'error' => $e->getMessage(),
                'birthday_count' => 0,
                'reminder_count' => 0,
                'total_count' => 0
            ];
        }
    }

    // New method to send birthday notifications to all members about another member's birthday
    public function sendMemberBirthdayNotifications($birthdayMemberId, $templateId = null, $daysUntilBirthday = 0) {
        // Debug log start of notification process
        error_log("Starting birthday notifications for member ID $birthdayMemberId, days until birthday: $daysUntilBirthday");
        
        // Track results
        $success = 0;
        $failed = 0;
        $skipped = 0;
        
        try {
            // 1. First, get the birthday member's details
            $stmt = $this->pdo->prepare("SELECT * FROM members WHERE id = ? LIMIT 1");
            $stmt->execute([$birthdayMemberId]);
            $birthdayMember = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$birthdayMember) {
                error_log("Error: Birthday member with ID $birthdayMemberId not found");
                return ['error' => 'Birthday member not found', 'success' => 0, 'failed' => 0, 'skipped' => 1];
            }
            
            // Log the birthday member's details for debugging
            error_log("Birthday member: " . $birthdayMember['full_name'] . " (ID: {$birthdayMember['id']}, Email: " . 
                     ($birthdayMember['email'] ?? 'none') . ")");
            
            // Verify this person has a birthday on the specified day
            $today = new DateTime();
            $today->modify("+$daysUntilBirthday days");
            $today_md = $today->format('m-d');
            
            $birth_date = new DateTime($birthdayMember['birth_date']);
            $birth_md = $birth_date->format('m-d');
            
            if ($birth_md != $today_md) {
                error_log("Warning: Member ID $birthdayMemberId does not have a birthday on the target date ($today_md vs $birth_md)");
            }
            
            // Get gender if not already set
            if (empty($birthdayMember['gender'])) {
                $birthdayMember['gender'] = $this->detectGender($birthdayMember['full_name']);
                error_log("Detected gender for {$birthdayMember['full_name']}: {$birthdayMember['gender']}");
            }
            
            // Set age if not already calculated - use birthday age for notifications
            if (empty($birthdayMember['age']) && !empty($birthdayMember['birth_date'])) {
                $birthdayMember['age'] = $this->calculateBirthdayAge($birthdayMember['birth_date']);
                error_log("Calculated birthday age for {$birthdayMember['full_name']}: {$birthdayMember['age']} (age they will be on their birthday)");
            }
            
            // 2. Get email template
            if ($templateId) {
                $template = $this->getTemplateById($templateId);
                if (!$template) {
                    error_log("Error: Notification template with ID $templateId not found");
                    return ['error' => 'Template not found', 'success' => 0, 'failed' => 0, 'skipped' => 1];
                }
            } else {
                // Get a random template suitable for notifications
                $stmt = $this->pdo->prepare("SELECT * FROM email_templates WHERE is_birthday_template = 0 AND template_name LIKE '%Notification%' ORDER BY RAND() LIMIT 1");
                $stmt->execute();
                $template = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$template) {
                    error_log("Error: No notification templates found");
                    return ['error' => 'No notification templates available', 'success' => 0, 'failed' => 0, 'skipped' => 0];
                }
            }
            
            error_log("Using template: {$template['template_name']} (ID: {$template['id']})");
            
            // 3. Explicitly filter out the birthday member at database level
            $birthdayEmail = strtolower($birthdayMember['email'] ?? '');
            $hasBirthdayEmail = !empty($birthdayEmail);
            
            // Create a more precise query that filters out the birthday member
            $query = "SELECT * FROM members WHERE status = 'active' AND email IS NOT NULL AND email != ''";
            $params = [];
            
            // Always filter by ID
            $query .= " AND id != ?";
            $params[] = $birthdayMemberId;
            
            // Also filter by email if available
            if ($hasBirthdayEmail) {
                $query .= " AND LOWER(email) != ?";
                $params[] = $birthdayEmail;
            }
            
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            $allMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            error_log("Found " . count($allMembers) . " potential recipients after initial filtering");
            
            // 4. Secondary filtering to make absolutely sure we're not including the birthday member
            $filteredMembers = [];
            foreach ($allMembers as $member) {
                // Never send to the birthday member themselves
                if ($member['id'] == $birthdayMember['id']) {
                    error_log("Filtering out birthday member with ID {$birthdayMember['id']} from recipients");
                    $skipped++;
                    continue;
                }
                
                // Double-check email comparison to catch any case sensitivity issues
                if ($hasBirthdayEmail && !empty($member['email']) && 
                    strtolower($member['email']) === $birthdayEmail) {
                    error_log("Filtering out member with ID {$member['id']} who has same email as birthday member");
                    $skipped++;
                    continue;
                }
                
                // Additional name-based check for maximum safety
                if (!empty($birthdayMember['full_name']) && !empty($member['full_name']) && 
                    strtolower($member['full_name']) === strtolower($birthdayMember['full_name'])) {
                    error_log("Filtering out member with ID {$member['id']} who has same name as birthday member");
                    $skipped++;
                    continue;
                }
                
                // Include this member as a filtered recipient
                $filteredMembers[] = $member;
            }
            
            error_log("After additional filtering: " . count($filteredMembers) . " final recipients (skipped: $skipped)");
            
            if (empty($filteredMembers)) {
                error_log("No members to send notifications to after filtering");
                return ['success' => 0, 'failed' => 0, 'skipped' => $skipped, 'message' => 'No eligible recipients found'];
            }
            
            // 5. Send emails to filtered members in batches using throttling settings
            $batchSize = $this->getEmailBatchSize();
            $batches = array_chunk($filteredMembers, $batchSize);
            error_log("Processing birthday notifications in " . count($batches) . " batches of size $batchSize");
            
            foreach ($batches as $batchIndex => $batch) {
                error_log("Processing birthday notification batch " . ($batchIndex + 1) . " of " . count($batches) . " (batch size: " . count($batch) . ")");

                foreach ($batch as $member) {
                    try {
                        error_log("Sending notification to {$member['full_name']} (ID: {$member['id']}) about {$birthdayMember['full_name']}'s birthday");
                        
                        // Process the template specifically for this context
                        $result = $this->sendEmail($member, $template, $daysUntilBirthday, 'birthday_notification', $birthdayMember);
                        
                        if ($result) {
                            $success++;
                        } else {
                            $failed++;
                            error_log("Failed to send birthday notification to {$member['full_name']} (ID: {$member['id']})");
                        }
                    } catch (Exception $e) {
                        $failed++;
                        error_log("Exception sending notification to {$member['full_name']}: " . $e->getMessage());
                    }
                    
                    // Add throttling delay between emails (except for the last email in the last batch)
                    if (!($member === end($batch) && $batch === end($batches))) {
                        $this->addThrottlingDelay();
                    }
                }

                // Add a longer pause between batches if there are more batches to process
                if ($batchIndex < count($batches) - 1) {
                    error_log("Completed notification batch " . ($batchIndex + 1) . ", pausing before next batch...");
                    sleep(2); // 2 second pause between batches
                }
            }
            
            // Final log
            error_log("Birthday notifications complete for {$birthdayMember['full_name']} - Success: $success, Failed: $failed, Skipped: $skipped");
            
            // Send admin notification
            $this->sendAdminNotification();
            
            return [
                'success' => $success,
                'failed' => $failed,
                'skipped' => $skipped,
                'birthday_member' => $birthdayMember['full_name'],
                'template' => $template['template_name']
            ];
            
        } catch (Exception $e) {
            error_log("Fatal error in sendMemberBirthdayNotifications: " . $e->getMessage());
            return ['error' => $e->getMessage(), 'success' => $success, 'failed' => $failed, 'skipped' => $skipped];
        }
    }
    
    /**
     * Attempt to detect gender from name
     * @param string $name Full name
     * @return string 'male', 'female', or 'unknown'
     */
    private function detectGender($name) {
        // Extract first name
        $firstName = explode(' ', $name)[0];
        
        // Common female names ending patterns
        $femaleEndings = ['a', 'ie', 'i', 'ey', 'y', 'en', 'ah', 'ra', 'ette'];
        
        // Known female names that don't follow patterns
        $femaleNames = [
            'abigail', 'ada', 'alice', 'alisha', 'allison', 'amanda', 'amber', 'amy', 'angela', 'ann', 'anna', 'anne',
            'ashley', 'barbara', 'beatrice', 'bertha', 'beth', 'betty', 'beverly', 'bonnie', 'brenda', 'brittany',
            'carol', 'caroline', 'catherine', 'charlotte', 'cheryl', 'christine', 'claire', 'debbie', 'deborah',
            'diana', 'donna', 'doris', 'dorothy', 'elizabeth', 'emily', 'emma', 'esther', 'eve', 'evelyn', 'faith',
            'frances', 'gail', 'grace', 'hannah', 'heather', 'helen', 'hope', 'irene', 'jacqueline', 'jane', 'janet',
            'jean', 'jennifer', 'jessica', 'joan', 'joyce', 'judith', 'judy', 'julia', 'julie', 'karen', 'katherine',
            'kathleen', 'kathryn', 'kathy', 'kelly', 'kim', 'kimberly', 'laura', 'lauren', 'lillian', 'linda', 'lisa',
            'lois', 'louise', 'lucy', 'margaret', 'maria', 'marie', 'marilyn', 'martha', 'mary', 'megan', 'melissa',
            'michelle', 'mildred', 'nancy', 'nicole', 'olivia', 'pamela', 'patricia', 'paula', 'phyllis', 'rachel',
            'rebecca', 'rhonda', 'rita', 'robin', 'rose', 'ruth', 'sandra', 'sara', 'sarah', 'sharon', 'shirley',
            'sophia', 'stephanie', 'susan', 'tammy', 'teresa', 'theresa', 'tiffany', 'tracy', 'victoria', 'virginia',
            'wanda', 'wendy', 'yvonne', 'zoe'
        ];
        
        // Known male names
        $maleNames = [
            'aaron', 'adam', 'alan', 'albert', 'alex', 'alexander', 'alfred', 'andrew', 'anthony', 'arthur',
            'barry', 'benjamin', 'bernard', 'bill', 'billy', 'bob', 'bobby', 'brad', 'bradley', 'brandon',
            'brian', 'bruce', 'bryan', 'carl', 'carlos', 'charles', 'charlie', 'chris', 'christopher',
            'clarence', 'daniel', 'danny', 'darren', 'dave', 'david', 'dennis', 'derek', 'donald', 'douglas',
            'edward', 'eric', 'eugene', 'francis', 'frank', 'fred', 'frederick', 'gary', 'george', 'gerald',
            'gregory', 'harold', 'harry', 'henry', 'herbert', 'howard', 'isaac', 'jack', 'jacob', 'james',
            'jason', 'jay', 'jeff', 'jeffrey', 'jeremy', 'jerry', 'jesse', 'jim', 'jimmy', 'joe', 'joel', 'john',
            'jonathan', 'joseph', 'joshua', 'justin', 'keith', 'kenneth', 'kevin', 'larry', 'lawrence', 'lee',
            'leonard', 'lewis', 'louis', 'mark', 'martin', 'matthew', 'michael', 'mike', 'nathan', 'neil',
            'nicholas', 'norman', 'patrick', 'paul', 'peter', 'philip', 'phillip', 'ralph', 'randy', 'raymond',
            'richard', 'robert', 'roger', 'ronald', 'roy', 'ryan', 'samuel', 'scott', 'sean', 'shane', 'stanley',
            'stephen', 'steve', 'steven', 'terry', 'theodore', 'thomas', 'timothy', 'todd', 'tom', 'travis',
            'tyler', 'victor', 'vincent', 'walter', 'wayne', 'william', 'zachary', 'jude'
        ];
        
        // Convert to lowercase for case-insensitive comparison
        $firstName = strtolower($firstName);
        
        // Check against known female names list
        if (in_array($firstName, $femaleNames)) {
            return 'female';
        }
        
        // Check against known male names list
        if (in_array($firstName, $maleNames)) {
            return 'male';
        }
        
        // Check for common female name endings
        foreach ($femaleEndings as $ending) {
            if (substr($firstName, -strlen($ending)) === $ending) {
                return 'female';
            }
        }
        
        // If no matches, default to male (more common for unrecognized names)
        return 'male';
    }

    // Helper method to process templates for birthday member notifications
    public function processBirthdayMemberTemplate($content, $member, $birthdayMember, $daysUntilBirthday = 0) {
        // Log for debugging
        error_log("Processing template for celebrant: " . ($birthdayMember['full_name'] ?? 'Unknown') . 
                 ", recipient: " . ($member['full_name'] ?? 'Unknown'));
        
        // Validate input data
        if (!is_array($member) || !is_array($birthdayMember)) {
            error_log("Invalid member data in processBirthdayMemberTemplate");
            return $content;
        }
        
        // Extract names
        $birthdayMemberName = explode(' ', $birthdayMember['full_name'])[0] ?? 'Member';
        $birthdayMemberFullName = $birthdayMember['full_name'] ?? 'Member';
        $recipientName = explode(' ', $member['full_name'])[0] ?? 'Member';
        $recipientFullName = $member['full_name'] ?? 'Member';
        
        // Special handling for subject lines - detect if content appears to be a subject
        $isSubject = strlen($content) < 200 && !strpos($content, '<');
        if ($isSubject) {
            error_log("Processing birthday notification subject: $content");
            
            // For subject lines, ensure proper placeholders are replaced first
            $basicReplacements = [
                '{birthday_member_name}' => $birthdayMemberName,
                '{birthday_member_full_name}' => $birthdayMemberFullName,
                '{recipient_name}' => $recipientName,
                '{recipient_full_name}' => $recipientFullName,
                '{days_text}' => $this->getDaysText($daysUntilBirthday),
                '{member_name}' => $recipientName  // Legacy placeholder
            ];
            
            // Apply basic replacements
            $content = str_replace(array_keys($basicReplacements), array_values($basicReplacements), $content);
            
            // Additional processing for subject lines to avoid duplicated names
            if (strpos($content, $birthdayMemberName . ' ' . $birthdayMemberName) !== false ||
                strpos($content, $birthdayMemberName . "'s " . $birthdayMemberName) !== false ||
                strpos($content, $birthdayMemberName . " us") !== false) {
                
                // Use a consistent format for all birthday notification subjects
                $content = "🎉 Birthday Celebration! {$birthdayMemberName}'s birthday is {$this->getDaysText($daysUntilBirthday)}!";
            }
            
            // Log the final processed subject
            error_log("Final processed subject: $content");
            
            return $content;
        }
        
        // Process member image URL for the birthday member
        $birthdayMemberPhotoUrl = '';
        
        // FIXED: Ensure image URLs are publicly accessible for email delivery
        if (!empty($birthdayMember['image_path'])) {
            $imagePath = $birthdayMember['image_path'];
            if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
                // Already an absolute URL
                $birthdayMemberPhotoUrl = $imagePath;
            } else {
                // Construct public URL - CRITICAL: Must be publicly accessible
                $siteUrl = defined('SITE_URL') ? SITE_URL : ('https://' . $_SERVER['HTTP_HOST']);
                
                // Ensure SITE_URL doesn't use localhost for production
                if (strpos($siteUrl, 'localhost') !== false || strpos($siteUrl, '127.0.0.1') !== false) {
                    error_log("WARNING: SITE_URL uses localhost - images will not be accessible in delivered emails!");
                }
                
                $birthdayMemberPhotoUrl = $siteUrl . '/' . ltrim($imagePath, '/');
            }
        } else {
            // Use publicly accessible default avatar
            $siteUrl = defined('SITE_URL') ? SITE_URL : ('https://' . $_SERVER['HTTP_HOST']);

            // CRITICAL: Check for localhost usage that will break email delivery
            if (strpos($siteUrl, 'localhost') !== false || strpos($siteUrl, '127.0.0.1') !== false) {
                error_log("CRITICAL WARNING: SITE_URL uses localhost ($siteUrl) - default avatar will NOT be accessible in delivered emails! Update SITE_URL to your public domain.");
            }

            $birthdayMemberPhotoUrl = $siteUrl . '/assets/img/default-avatar.png';
        }
        
        // Log the final URL for debugging
        error_log("Birthday member image URL: " . $birthdayMemberPhotoUrl);
        
        // Create both URL and HTML versions for different placeholder usage patterns
        $birthdayMemberImageUrl = $birthdayMemberPhotoUrl;
        $birthdayMemberImageHtml = '<img src="' . $birthdayMemberPhotoUrl . '" alt="' .
            htmlspecialchars($birthdayMemberFullName) .
            '" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 4px solid #ddd; display: block; margin: 0 auto;">';

        // Store the image URL for backward compatibility
        $memberImageUrl = $birthdayMemberPhotoUrl;
        
        // Get preferred birth date and calculate values
        $preferredBirthDate = $this->getPreferredBirthDate($birthdayMember);
        $calculatedAge = $this->calculateBirthdayAge($preferredBirthDate); // Use birthday age, not current age
        $upcomingBirthdayFormatted = $this->getCorrectBirthdayDate($birthdayMember, $daysUntilBirthday);
        $daysText = $this->getDaysText($daysUntilBirthday);

        // Log the calculated values for debugging
        error_log("Birthday notification template processing:");
        error_log("- Birthday member: " . $birthdayMemberFullName);
        error_log("- Preferred birth date: " . $preferredBirthDate);
        error_log("- Calculated birthday age: " . $calculatedAge . " (age they will be on their birthday)");
        error_log("- Days until birthday: " . $daysUntilBirthday);
        error_log("- Days text: " . $daysText);
        error_log("- Upcoming birthday formatted: " . $upcomingBirthdayFormatted);

        // CRITICAL FIX: DO NOT merge with $member data to avoid birth_date conflicts
        // Instead, create a clean data array with only the necessary fields from both members
        $placeholderData = [
            // Recipient data (person receiving the email)
            'first_name' => $recipientName,
            'full_name' => $recipientFullName,
            'email' => $member['email'] ?? '',
            'phone_number' => $member['phone_number'] ?? '',
            'phone' => $member['phone_number'] ?? '',
            'home_address' => $member['home_address'] ?? '',
            'address' => $member['home_address'] ?? $member['address'] ?? '',
            'city' => $member['city'] ?? '',
            'state' => $member['state'] ?? '',
            'zip' => $member['zip'] ?? '',
            'country' => $member['country'] ?? '',
            'member_name' => $recipientName,
            'member_full_name' => $recipientFullName,
            'recipient_first_name' => $recipientName,
            'recipient_full_name' => $recipientFullName,
            'recipient_email' => $member['email'] ?? '',
            'recipient_phone' => $member['phone_number'] ?? '',

            // Birthday member data (person having the birthday)
            'birthday_member_first_name' => $birthdayMemberName,
            'birthday_member_full_name' => $birthdayMemberFullName,
            'birthday_member_email' => $birthdayMember['email'] ?? '',
            'birthday_member_phone' => $birthdayMember['phone_number'] ?? '',
            'birthday_member_birth_date' => date('F j', strtotime($preferredBirthDate)),
            'birthday_member_age' => $calculatedAge,
            'birthday_member_photo_url' => $birthdayMemberPhotoUrl,
            'birthday_member_image' => $birthdayMemberImageHtml,
            'birthday_member_image_url' => $birthdayMemberImageUrl,
            'birthday_member_name' => $birthdayMemberName,

            // CRITICAL: Add birth_date field explicitly for the birthday member
            // This ensures age calculations use the birthday member's birth date, not the recipient's
            'birth_date' => $preferredBirthDate,
            'age' => $calculatedAge,

            // Image placeholders - FIXED: Use URLs and let replaceTemplatePlaceholders handle HTML generation
            'member_image_url' => $birthdayMemberImageUrl,  // For src attribute usage (expects URL)
            'image_path' => $birthdayMemberImageUrl,
            'profile_photo' => $birthdayMemberImageUrl,

            // CRITICAL: Add original image path for proper email embedding
            '_original_image_path' => $birthdayMember['image_path'] ?? null,
            '_birthday_member_original_image_path' => $birthdayMember['image_path'] ?? null,

            // Date and time placeholders
            'current_date' => date('F j, Y'),
            'current_time' => date('g:i A'),
            'upcoming_birthday_formatted' => $upcomingBirthdayFormatted,
            'days_text' => $daysText,
            'birthday_date' => date('F j', strtotime($preferredBirthDate)),
            'birthday_year' => date('Y', strtotime($preferredBirthDate)),
            'current_year' => date('Y'),

            // Flag to indicate this is a birthday notification
            '_is_birthday_notification' => true
        ];
        
        // Ensure all inline styles for email clients have text-align: center
        // This helps fix alignment issues in various email clients
        if (strpos($content, '<div class="container"') !== false) {
            // Add !important to center alignment in content
            $content = str_replace('<div class="container"', '<div class="container" style="text-align: center !important; margin: 0 auto !important;"', $content);
        }
        
        // UPCOMING BIRTHDAY NOTIFICATION FIX: Use the standardized placeholder replacement function for consistency (INCLUDE member image for birthday context)
        $processed = replaceTemplatePlaceholders($content, $placeholderData, false);

        // REMOVED: Secondary replacement loop that was causing image tag duplication
        // The replaceTemplatePlaceholders function already handles all placeholders correctly
        
        // REMOVED: Broken image pattern fixes that were causing corruption
        // These are no longer needed since replaceTemplatePlaceholders() now works correctly

        // UPCOMING BIRTHDAY NOTIFICATION FIX: Final safety check to ensure any remaining {member_image} placeholder gets proper HTML
        if (strpos($processed, '{member_image}') !== false) {
            error_log("UPCOMING BIRTHDAY NOTIFICATION FIX: Found {member_image} placeholder after initial replacements, replacing with HTML");

            // Create proper HTML img tag for the birthday member
            $birthdayMemberName = $birthdayMember['full_name'] ?? 'Birthday Member';
            $safetyImageHtml = '<img src="' . $birthdayMemberImageUrl . '" alt="' . htmlspecialchars($birthdayMemberName) . '" ' .
                'style="width: 140px; height: 140px; border-radius: 50%; object-fit: cover; ' .
                'border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1); display: block; margin: 0 auto;">';

            $processed = str_replace('{member_image}', $safetyImageHtml, $processed);
        }

        // Safety check for URL placeholders that might have been missed
        if (strpos($processed, '{member_image_url}') !== false) {
            error_log("Found {member_image_url} placeholder after initial replacements, replacing with URL");
            $processed = str_replace('{member_image_url}', $birthdayMemberImageUrl, $processed);
        }
        
        // Log any remaining unreplaced placeholders
        if (preg_match_all('/{([^}]+)}/', $processed, $matches)) {
            error_log("Warning: Found unreplaced placeholders: " . implode(', ', $matches[0]));
        }
        
        return $processed;
    }
    
    // Helper function to format days text consistently
    private function getDaysText($daysUntilBirthday) {
        if ($daysUntilBirthday === 0) {
            return 'today';
        } elseif ($daysUntilBirthday === 1) {
            return 'tomorrow';
        } else {
            return "in $daysUntilBirthday days";
        }
    }
    
    // Helper method to get the preferred birth date (prioritizes birth_date over date_of_birth)
    private function getPreferredBirthDate($member) {
        // Prioritize birth_date field, fallback to date_of_birth
        if (!empty($member['birth_date'])) {
            return $member['birth_date'];
        } elseif (!empty($member['date_of_birth'])) {
            return $member['date_of_birth'];
        } else {
            return null;
        }
    }

    // Helper method to calculate current age
    private function calculateAge($birthDate) {
        if (!$birthDate || $birthDate == 'now') {
            return 'Unknown';
        }

        try {
            $birth = new DateTime($birthDate);
            $today = new DateTime();

            // Calculate age as of today
            $age = $today->diff($birth)->y;

            error_log("Calculating current age: birth_date=$birthDate, today=" . $today->format('Y-m-d') . ", age=$age");
            return $age;
        } catch (Exception $e) {
            error_log("Error calculating age for birth date: $birthDate - " . $e->getMessage());
            return 'Unknown';
        }
    }

    // Helper method to calculate age on upcoming birthday
    private function calculateBirthdayAge($birthDate) {
        if (!$birthDate || $birthDate == 'now') {
            return 'Unknown';
        }

        try {
            $birth = new DateTime($birthDate);
            $today = new DateTime();

            // Get birth month and day
            $birthMonth = $birth->format('m');
            $birthDay = $birth->format('d');
            $currentYear = $today->format('Y');

            // Create this year's birthday date
            $thisYearBirthday = new DateTime("$currentYear-$birthMonth-$birthDay");

            // If birthday has already passed this year, use next year's birthday
            if ($thisYearBirthday < $today) {
                $thisYearBirthday->modify('+1 year');
            }

            // Calculate age they will be on their upcoming birthday
            $birthdayAge = $birth->diff($thisYearBirthday)->y;

            error_log("Calculating birthday age: birth_date=$birthDate, upcoming_birthday=" .
                     $thisYearBirthday->format('Y-m-d') . ", birthday_age=$birthdayAge");

            return $birthdayAge;
        } catch (Exception $e) {
            error_log("Error calculating birthday age for birth date: $birthDate - " . $e->getMessage());
            return 'Unknown';
        }
    }

    // Helper method to convert number to ordinal (1st, 2nd, 3rd, etc.)
    private function getOrdinal($number) {
        if (!is_numeric($number)) {
            return $number;
        }

        $number = (int)$number;
        $suffix = 'th';

        if ($number % 100 >= 11 && $number % 100 <= 13) {
            $suffix = 'th';
        } else {
            switch ($number % 10) {
                case 1:
                    $suffix = 'st';
                    break;
                case 2:
                    $suffix = 'nd';
                    break;
                case 3:
                    $suffix = 'rd';
                    break;
                default:
                    $suffix = 'th';
                    break;
            }
        }

        return $number . $suffix;
    }

    // Helper method to get the correct birthday date for the current year
    private function getCorrectBirthdayDate($birthdayMember, $daysUntilBirthday) {
        $preferredBirthDate = $this->getPreferredBirthDate($birthdayMember);

        if (!$preferredBirthDate) {
            // Fallback to adding days to current date
            return date('l, F j, Y', strtotime("+" . $daysUntilBirthday . " days"));
        }

        try {
            $birthDate = new DateTime($preferredBirthDate);
            $currentYear = date('Y');

            // Create birthday for current year
            $thisYearBirthday = new DateTime($currentYear . '-' . $birthDate->format('m-d'));

            // If daysUntilBirthday is 0, it means today is the birthday
            if ($daysUntilBirthday === 0) {
                // Return today's date with the birthday month/day
                $today = new DateTime();
                $todayFormatted = $today->format('l, F j, Y');
                error_log("Birthday is today, returning: " . $todayFormatted);
                return $todayFormatted;
            }

            // If birthday has passed this year (but not today), use next year
            $today = new DateTime();
            $today->setTime(0, 0, 0); // Reset time to start of day for accurate comparison
            $thisYearBirthday->setTime(0, 0, 0); // Reset time to start of day

            if ($thisYearBirthday < $today) {
                $thisYearBirthday->modify('+1 year');
            }

            return $thisYearBirthday->format('l, F j, Y');
        } catch (Exception $e) {
            error_log("Error calculating birthday date: " . $e->getMessage());
            // Fallback to adding days to current date
            return date('l, F j, Y', strtotime("+" . $daysUntilBirthday . " days"));
        }
    }

    // Helper method to get correct birthday date from birth date string
    private function getCorrectBirthdayDateFromBirthDate($birthDate) {
        if (!$birthDate) {
            return date('Y-m-d');
        }

        try {
            $birth = new DateTime($birthDate);
            $currentYear = date('Y');

            // Create birthday for current year
            $thisYearBirthday = new DateTime($currentYear . '-' . $birth->format('m-d'));

            // If birthday has passed this year (but not today), use next year
            $today = new DateTime();
            $today->setTime(0, 0, 0); // Reset time to start of day for accurate comparison
            $thisYearBirthday->setTime(0, 0, 0); // Reset time to start of day

            if ($thisYearBirthday < $today) {
                $thisYearBirthday->modify('+1 year');
            }

            return $thisYearBirthday->format('Y-m-d');
        } catch (Exception $e) {
            error_log("Error calculating birthday date from birth date: " . $e->getMessage());
            return date('Y-m-d');
        }
    }

    // Helper method to calculate actual days until birthday
    private function calculateActualDaysUntilBirthday($birthDate) {
        if (!$birthDate) {
            return 365; // Default fallback
        }

        try {
            $birth = new DateTime($birthDate);
            $today = new DateTime();
            $today->setTime(0, 0, 0);

            // Get this year's birthday
            $thisYearBirthday = new DateTime(date('Y') . '-' . $birth->format('m-d'));
            $thisYearBirthday->setTime(0, 0, 0);

            // If today is the birthday, return 0
            if ($thisYearBirthday == $today) {
                error_log("Today is the birthday, returning 0 days");
                return 0;
            }

            // If birthday has already passed this year, get next year's birthday
            if ($thisYearBirthday < $today) {
                $thisYearBirthday->modify('+1 year');
            }

            // Calculate days difference
            $diff = $today->diff($thisYearBirthday);
            return $diff->days;

        } catch (Exception $e) {
            error_log("Error calculating days until birthday: " . $e->getMessage());
            return 365;
        }
    }

    private function sanitizeSubjectLine($subject, $member, $birthdayMember = null) {
        // If subject is too long, it's probably not a subject line
        if (strlen($subject) > 200) {
            return $subject;
        }
        
        // Log the original subject
        error_log("Sanitizing subject: " . $subject);
        
        $firstName = isset($member['first_name']) ? $member['first_name'] : '';
        $lastName = isset($member['last_name']) ? $member['last_name'] : '';
        $fullName = isset($member['full_name']) ? $member['full_name'] : '';
        
        // If we don't have first/last name but have full name, extract them
        if (empty($firstName) && !empty($fullName)) {
            $nameParts = explode(' ', $fullName, 2);
            $firstName = $nameParts[0];
            $lastName = isset($nameParts[1]) ? $nameParts[1] : '';
        }
        
        // Check if this is a notification email about another member's birthday
        $isBirthdayNotification = $birthdayMember !== null;
        if ($isBirthdayNotification) {
            $birthdayFirstName = isset($birthdayMember['first_name']) ? $birthdayMember['first_name'] : '';
            $birthdayLastName = isset($birthdayMember['last_name']) ? $birthdayMember['last_name'] : '';
            $birthdayFullName = isset($birthdayMember['full_name']) ? $birthdayMember['full_name'] : '';
            
            // Extract names from full name if necessary
            if (empty($birthdayFirstName) && !empty($birthdayFullName)) {
                $birthdayNameParts = explode(' ', $birthdayFullName, 2);
                $birthdayFirstName = $birthdayNameParts[0];
                $birthdayLastName = isset($birthdayNameParts[1]) ? $birthdayNameParts[1] : '';
            }
            
            error_log("Birthday notification - Recipient: '$fullName', Birthday Person: '$birthdayFullName'");
        }
        
        // No name info available, return original
        if (empty($firstName) && empty($fullName)) {
            return $subject;
        }
        
        // Log the name components we're working with
        error_log("Name components - First: '$firstName', Last: '$lastName', Full: '$fullName'");
        
        // Store original for comparison
        $originalSubject = $subject;
        
        // FIRST: Explicitly handle common placeholder replacements
        $placeholders = [
            '{full_name}' => $isBirthdayNotification ? $birthdayFullName : $fullName,
            '{first_name}' => $isBirthdayNotification ? $birthdayFirstName : $firstName,
            '{last_name}' => $isBirthdayNotification ? $birthdayLastName : $lastName,
            '{member_name}' => $firstName, // Always recipient for email formatting
            '{member_full_name}' => $fullName, // Always recipient for email formatting
            '{name}' => $isBirthdayNotification ? $birthdayFirstName : $firstName,
            '{recipient_name}' => $firstName,
            '{recipient_first_name}' => $firstName,
            '{recipient_full_name}' => $fullName,
            '{birthday_member_name}' => $isBirthdayNotification ? $birthdayFirstName : $firstName,
            '{birthday_member_first_name}' => $isBirthdayNotification ? $birthdayFirstName : $firstName,
            '{birthday_member_full_name}' => $isBirthdayNotification ? $birthdayFullName : $fullName,

            // Organization placeholders
            '{organization_name}' => get_site_setting('organization_name', get_site_setting('site_title', 'Organization')),
            '{organization_type}' => get_site_setting('organization_type', 'organization'),
            '{church_name}' => get_site_setting('organization_name', get_site_setting('site_title', 'Organization')), // Legacy support
        ];
        
        // Apply placeholder replacements
        $subject = str_replace(array_keys($placeholders), array_values($placeholders), $subject);
        
        // For birthday notifications, ensure subject uses birthday member's name
        if ($isBirthdayNotification) {
            // For notification subjects, use a simple, clean format without emoji to avoid encoding issues
            $birthdayMemberName = $birthdayFirstName; // Use first name only
            $subject = "Birthday Celebration! {$birthdayMemberName}";
            
            error_log("Using clean notification subject format: $subject");
        } else {
            // SECOND: Fix common patterns (only if not a notification)
            $subject = str_replace(
                [
                    $firstName . $firstName, 
                    $firstName . ' ' . $lastName . $firstName,
                    $fullName . $firstName,
                    $firstName . $fullName,
                    $fullName . $fullName,
                    $firstName . "'s " . $firstName . "'s",
                    $firstName . "'s" . $firstName . "'s",
                    $firstName . "Ohoh",  // Special case for the example
                    "Ohoh" . $firstName,  // Special case for the example
                ],
                [
                    $firstName,
                    $firstName . ' ' . $lastName,
                    $fullName,
                    $fullName,
                    $fullName,
                    $firstName . "'s",
                    $firstName . "'s",
                    $firstName,
                    $firstName,
                ],
                $subject
            );
            
            // Detect and fix patterns like "OhohOhoh Jude"
            if (preg_match('/([A-Za-z]+)\\1\\s+([A-Za-z]+)/i', $subject, $matches)) {
                $repeatedFirstName = $matches[1];
                $lastName = $matches[2];
                $subject = str_replace($repeatedFirstName . $repeatedFirstName . ' ' . $lastName, 
                                       $repeatedFirstName . ' ' . $lastName, $subject);
            }
            
            // Use a systematic regex replacement for duplicate names
            $subject = preg_replace('/\b([A-Za-z]+)(\s+[A-Za-z]+)?\1\b/i', '$1$2', $subject);
            
            // Fix specific birthday reminder format
            if (strpos($subject, "Birthday Reminder") !== false) {
                $subject = "Birthday Reminder for {$firstName}";
            }
            
            // For birthday announcements, use standardized format
            if (strpos($subject, "birthday is coming") !== false || 
                strpos($subject, "Celebrate with us") !== false) {
                // Get days text
                preg_match('/in\s+(\d+)\s+days/', $subject, $matches);
                $daysText = isset($matches[1]) ? "in {$matches[1]} days" : 
                           (strpos($subject, "today") !== false ? "today" : 
                           (strpos($subject, "tomorrow") !== false ? "tomorrow" : "soon"));
                
                $subject = "Celebrate with us! {$firstName}'s birthday is coming up {$daysText}!";
            }
        }
        
        // FINAL: Check for any remaining placeholders and replace them
        if (preg_match_all('/{([^}]+)}/', $subject, $matches)) {
            error_log("Warning: Found unreplaced placeholders in subject: " . implode(', ', $matches[0]));
            
            // Try to replace each one with equivalent member data if available
            foreach ($matches[0] as $placeholder) {
                $key = trim($placeholder, '{}');
                if (isset($member[$key])) {
                    $subject = str_replace($placeholder, $member[$key], $subject);
                    error_log("Replaced placeholder $placeholder with '" . $member[$key] . "'");
                } else if ($isBirthdayNotification && isset($birthdayMember[$key])) {
                    // Check if it's a birthday member field
                    $subject = str_replace($placeholder, $birthdayMember[$key], $subject);
                    error_log("Replaced placeholder $placeholder with birthday member's '" . $birthdayMember[$key] . "'");
                } else {
                    // Remove the placeholder if no data available
                    $subject = str_replace($placeholder, '', $subject);
                    error_log("Removed placeholder $placeholder (no data available)");
                }
            }
        }
        
        // Remove any stray or incorrectly encoded emojis (common UTF-8 encoding issues)
        $subject = preg_replace('/[\x{10000}-\x{10FFFF}]/u', '', $subject);
        $subject = preg_replace('/[\xF0-\xFF][\x80-\xBF]{3}/', '', $subject);
        $subject = preg_replace('/ðŸŽ‰|ðŸŽ‚|ðŸŽ|ðŸ‚/', '', $subject);
        
        // Log if changes were made
        if ($originalSubject !== $subject) {
            error_log("Subject was sanitized from: '$originalSubject' to: '$subject'");
        } else {
            error_log("No changes needed to subject: '$subject'");
        }
        
        return $subject;
    }

    /**
     * Synchronize the date_of_birth and birth_date fields in the members table
     * This ensures both fields contain the same values for better compatibility
     * @return array Result statistics
     */
    public function syncBirthDateFields() {
        try {
            // Ensure we have a valid database connection
            if (!($this->pdo instanceof PDO)) {
                error_log("WARNING: Invalid PDO connection in syncBirthDateFields. Attempting to get a new connection.");
                if (function_exists('getDbConnection')) {
                    $this->pdo = getDbConnection();
                    error_log("Successfully established database connection in syncBirthDateFields.");
                } else {
                    error_log("ERROR: getDbConnection function does not exist. Cannot sync birth date fields.");
                    return ['status' => 'error', 'message' => 'Invalid database connection'];
                }
            }
            
            $result = [
                'status' => 'success',
                'updated_count' => 0,
                'details' => []
            ];
            
            // 1. First, update birth_date from date_of_birth where birth_date is NULL
            $stmt = $this->pdo->prepare("
                UPDATE members 
                SET birth_date = date_of_birth, updated_at = NOW() 
                WHERE date_of_birth IS NOT NULL 
                AND (birth_date IS NULL OR birth_date != date_of_birth)
            ");
            $stmt->execute();
            $updateCount1 = $stmt->rowCount();
            $result['details'][] = "Updated $updateCount1 records: Copied date_of_birth to birth_date";
            
            // 2. Then, update date_of_birth from birth_date where date_of_birth is NULL
            $stmt = $this->pdo->prepare("
                UPDATE members 
                SET date_of_birth = birth_date, updated_at = NOW() 
                WHERE birth_date IS NOT NULL 
                AND (date_of_birth IS NULL OR date_of_birth != birth_date)
            ");
            $stmt->execute();
            $updateCount2 = $stmt->rowCount();
            $result['details'][] = "Updated $updateCount2 records: Copied birth_date to date_of_birth";
            
            // Update total count
            $result['updated_count'] = $updateCount1 + $updateCount2;
            
            // Log the results
            error_log("Synchronized birth date fields: $updateCount1 + $updateCount2 = {$result['updated_count']} records updated");
            
            return $result;
        } catch (Exception $e) {
            error_log("Error synchronizing birth date fields: " . $e->getMessage());
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    /**
     * Perform a test of the birthday email system
     * This function can be called via cron to regularly verify the system is working
     * 
     * @param string $mode Testing mode (all, birthday, reminder)
     * @param int $daysAhead Days ahead for reminders
     * @param int $testLimit Maximum emails to send during testing
     * @return array Test results
     */
    public function runAutomatedTest($mode = 'all', $daysAhead = 3, $testLimit = 2) {
        try {
            error_log("Starting automated test of birthday email system in mode: $mode");
            
            // Ensure database connection is valid
            if (!($this->pdo instanceof PDO)) {
                error_log("WARNING: Invalid PDO connection in runAutomatedTest. Attempting to get a new connection.");
                if (function_exists('getDbConnection')) {
                    $this->pdo = getDbConnection();
                    error_log("Successfully established database connection in runAutomatedTest.");
                } else {
                    error_log("ERROR: getDbConnection function does not exist. Cannot run automated test.");
                    return ['status' => 'error', 'message' => 'Invalid database connection'];
                }
            }
            
            $results = [
                'status' => 'success',
                'timestamp' => date('Y-m-d H:i:s'),
                'mode' => $mode,
                'days_ahead' => $daysAhead,
                'sync_results' => null,
                'emails_sent' => 0,
                'emails_failed' => 0,
                'db_logs_created' => 0,
                'details' => []
            ];
            
            // 1. Start by syncing birth date fields
            $syncResults = $this->syncBirthDateFields();
            $results['sync_results'] = $syncResults;
            $results['details'][] = "Birth date field synchronization completed: {$syncResults['updated_count']} records updated";
            
            // 2. Log current birthday status
            $birthdaySummary = $this->getBirthdaySummary($daysAhead);
            $totalBirthdays = $birthdaySummary['total_count'] ?? 0;
            $results['details'][] = "Found $totalBirthdays total birthdays in next $daysAhead days";
            
            // 3. Set a test limit to avoid sending too many emails during testing
            $this->testLimit = $testLimit;
            $results['details'][] = "Test limit set to $testLimit emails";
            
            // 4. Run the email sending based on mode
            switch ($mode) {
                case 'birthday':
                    // Only test birthday emails
                    $testResults = $this->sendBirthdayEmails();
                    break;
                    
                case 'reminder':
                    // Only test reminder emails
                    $testResults = $this->sendBirthdayReminders(null, $daysAhead);
                    break;
                    
                case 'all':
                default:
                    // Test both types
                    $testResults = $this->sendReminders(null, null, $daysAhead);
                    break;
            }
            
            // 5. Record the results
            if ($testResults) {
                $results['emails_sent'] = $testResults['total_sent'] ?? 0;
                $results['emails_failed'] = $testResults['total_failed'] ?? 0;
                $results['details'][] = "Email test completed: {$results['emails_sent']} sent, {$results['emails_failed']} failed";
                
                // 6. Verify database logging by counting new entries
                try {
                    $stmt = $this->pdo->prepare("
                        SELECT COUNT(*) FROM email_logs 
                        WHERE sent_at >= ? 
                        AND email_type LIKE ?
                    ");
                    $timeLimit = date('Y-m-d H:i:s', strtotime('-5 minutes'));
                    $stmt->execute([$timeLimit, $mode === 'all' ? '%birth%' : "%$mode%"]);
                    $logCount = (int)$stmt->fetchColumn();
                    
                    $results['db_logs_created'] = $logCount;
                    $results['details'][] = "Found $logCount recent email logs in database";
                    
                    // 7. Check if logging appears to be working correctly
                    if ($results['emails_sent'] > 0 && $logCount == 0) {
                        $results['status'] = 'warning';
                        $results['message'] = 'Emails were sent but no database logs were created';
                        $results['details'][] = "WARNING: Email logging may not be working properly";
                    }
                } catch (Exception $e) {
                    $results['details'][] = "ERROR checking database logs: " . $e->getMessage();
                }
            } else {
                $results['status'] = 'warning';
                $results['message'] = 'No test results returned';
                $results['details'][] = "WARNING: No test results returned from email sender";
            }
            
            // 8. Log the test completion
            $status = $results['status'];
            $emailsSent = $results['emails_sent'];
            $emailsFailed = $results['emails_failed'];
            $dbLogs = $results['db_logs_created'];
            
            error_log("Automated test completed with status: $status. Emails: $emailsSent sent, $emailsFailed failed. Database logs: $dbLogs");
            
            return $results;
        } catch (Exception $e) {
            error_log("Error in automated testing: " . $e->getMessage());
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
    }
}

// Only execute when this file is run directly, not when included
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
try {
    // Create a new PDO connection
    $conn = null;
    if (function_exists('getDbConnection')) {
        $conn = getDbConnection();
        error_log("Successfully created database connection for BirthdayReminder script");
    } else {
        // Try to include config if getDbConnection doesn't exist
        require_once 'config.php';
        $conn = $pdo; // Use the global PDO from config
        error_log("Using global PDO connection from config.php");
    }
    
    // Initialize the reminder system
    $reminder = new BirthdayReminder($conn);
    error_log("Initialized BirthdayReminder system");
    
    // Sync birth date fields to ensure data consistency
    $syncResult = $reminder->syncBirthDateFields();
    error_log("Birth date field synchronization result: " . ($syncResult['status'] === 'success' ? 
        "SUCCESS - Updated {$syncResult['updated_count']} records" : 
        "ERROR - {$syncResult['message']}"));
        
    // Get command line arguments if any
    $options = getopt("m:b:r:d:t:", ["mode:", "birthday-template:", "reminder-template:", "days:", "test"]);
    
    // Check if test mode is requested
    $testMode = isset($options['t']) || isset($options['test']);
    
    // Default mode is to send all emails
    $mode = isset($options['m']) ? $options['m'] : (isset($options['mode']) ? $options['mode'] : 'all');
    
    // Default days ahead for reminders is 3
    $daysAhead = isset($options['d']) ? (int)$options['d'] : (isset($options['days']) ? (int)$options['days'] : 3);
    
    // Default templates are null (random)
    $birthdayTemplateId = isset($options['b']) ? (int)$options['b'] : (isset($options['birthday-template']) ? (int)$options['birthday-template'] : null);
    $reminderTemplateId = isset($options['r']) ? (int)$options['r'] : (isset($options['reminder-template']) ? (int)$options['reminder-template'] : null);
    
    // Log what we're doing
    if ($testMode) {
        error_log("Running birthday email system in TEST mode: $mode");
        
        // Run the automated test
        $testResults = $reminder->runAutomatedTest($mode, $daysAhead);
        
        // Output results
        echo "Birthday System Test Results\n";
        echo "Status: " . $testResults['status'] . "\n";
        if (isset($testResults['message'])) {
            echo "Message: " . $testResults['message'] . "\n";
        }
        echo "Emails Sent: " . $testResults['emails_sent'] . "\n";
        echo "Emails Failed: " . $testResults['emails_failed'] . "\n";
        echo "Database Logs: " . $testResults['db_logs_created'] . "\n";
        
        // Show detailed logs if there are any
        if (!empty($testResults['details'])) {
            echo "\nDetails:\n";
            foreach ($testResults['details'] as $detail) {
                echo "- " . $detail . "\n";
            }
        }
    } else {
        error_log("Running birthday email system in mode: $mode");
}
    
    $results = null;
    
    // Execute based on mode
    switch ($mode) {
        case 'birthday':
            // Only send birthday emails
            error_log("Sending birthday emails only");
            $results = $reminder->sendBirthdayEmails($birthdayTemplateId);
            break;
            
        case 'reminder':
            // Only send reminder emails
            error_log("Sending reminder emails only for $daysAhead days ahead");
            $results = $reminder->sendBirthdayReminders($reminderTemplateId, $daysAhead);
            break;
            
        case 'all':
        default:
            // Send both birthday and reminder emails
            error_log("Sending all emails (birthdays and reminders for $daysAhead days ahead)");
            $results = $reminder->sendReminders($birthdayTemplateId, $reminderTemplateId, $daysAhead);
            break;
    }
    
    // Output results
    if ($results) {
        $sentCount = $results['total_sent'];
        $failedCount = $results['total_failed'];
        
        error_log("Birthday email system complete: $sentCount sent, $failedCount failed");
        
        if ($sentCount > 0) {
            echo "Successfully sent $sentCount email(s)\n";
        }
        
        if ($failedCount > 0) {
            echo "Failed to send $failedCount email(s)\n";
        }
        
        if ($sentCount == 0 && $failedCount == 0) {
            echo "No emails to send\n";
        }
    } else {
        error_log("Birthday email system complete: No emails sent");
        echo "No emails to send\n";
    }
    
} catch (Exception $e) {
    error_log('Birthday reminder error: ' . $e->getMessage());
        echo "Error: " . $e->getMessage() . "\n";
    }
}