<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Simulating Admin Birthday Notification Workflow\n";
echo "===================================================\n\n";

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

// Simulate what the admin interface does when you click "Send Birthday Notifications"
echo "🎯 Step 1: Checking for upcoming birthdays (admin interface logic)\n";
echo "==================================================================\n";

try {
    // This is the exact logic from the admin interface
    $stmt = $pdo->prepare("
        SELECT id, full_name, first_name, last_name, email, birth_date, image_path
        FROM members 
        WHERE birth_date IS NOT NULL 
        AND email IS NOT NULL 
        AND email != ''
        AND status = 'active'
    ");
    $stmt->execute();
    $allMembers = $stmt->fetchAll();
    
    echo "Total active members with email and birth_date: " . count($allMembers) . "\n\n";
    
    // Calculate upcoming birthdays (next 7 days)
    $today = new DateTime();
    $upcomingBirthdays = [];
    
    foreach ($allMembers as $member) {
        $birthDate = new DateTime($member['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        // If birthday already passed this year, check next year
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntilBirthday = $today->diff($thisYearBirthday)->days;
        
        if ($daysUntilBirthday <= 7) {
            $upcomingBirthdays[] = [
                'member' => $member,
                'days_until' => $daysUntilBirthday,
                'birthday_date' => $thisYearBirthday->format('Y-m-d')
            ];
            
            echo "✅ Found: " . $member['full_name'] . " (ID: " . $member['id'] . ") in " . $daysUntilBirthday . " days\n";
            echo "   Email: " . $member['email'] . "\n";
            echo "   Birthday: " . $member['birth_date'] . " -> " . $thisYearBirthday->format('Y-m-d') . "\n";
            echo "   Status: " . ($member['status'] ?? 'unknown') . "\n\n";
        }
    }
    
    if (empty($upcomingBirthdays)) {
        echo "❌ NO UPCOMING BIRTHDAYS FOUND!\n";
        echo "This would cause the 'no recipient within 7 days' error.\n\n";
        
        echo "🔍 Debugging why no birthdays found:\n";
        echo "====================================\n";
        
        // Check each member individually
        foreach ($allMembers as $member) {
            if (strpos($member['full_name'], 'Sandra') !== false) {
                echo "🔍 Checking Sandra Stern specifically:\n";
                echo "   Full name: " . $member['full_name'] . "\n";
                echo "   Email: " . $member['email'] . "\n";
                echo "   Birth date: " . $member['birth_date'] . "\n";
                echo "   Status: " . ($member['status'] ?? 'not set') . "\n";
                
                $birthDate = new DateTime($member['birth_date']);
                $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
                
                if ($thisYearBirthday < $today) {
                    $thisYearBirthday->add(new DateInterval('P1Y'));
                    echo "   Birthday already passed this year, checking next year\n";
                }
                
                $daysUntilBirthday = $today->diff($thisYearBirthday)->days;
                echo "   This year birthday: " . $thisYearBirthday->format('Y-m-d') . "\n";
                echo "   Days until birthday: " . $daysUntilBirthday . "\n";
                echo "   Within 7 days: " . ($daysUntilBirthday <= 7 ? 'YES' : 'NO') . "\n";
                
                if ($daysUntilBirthday > 7) {
                    echo "   ❌ This is why Sandra is not included!\n";
                } else {
                    echo "   ✅ Sandra should be included\n";
                }
                echo "\n";
            }
        }
        
    } else {
        echo "✅ Found " . count($upcomingBirthdays) . " upcoming birthdays\n\n";
        
        echo "🎯 Step 2: Testing notification sending for each birthday member\n";
        echo "================================================================\n";
        
        $birthdayReminder = new BirthdayReminder($pdo);
        
        foreach ($upcomingBirthdays as $birthday) {
            $member = $birthday['member'];
            $daysUntil = $birthday['days_until'];
            
            echo "🔄 Testing notifications for " . $member['full_name'] . " (in " . $daysUntil . " days):\n";
            
            try {
                // This is what the admin interface would do
                $result = $birthdayReminder->sendMemberBirthdayNotifications($member['id'], null, $daysUntil);
                
                if ($result) {
                    echo "   ✅ Notifications sent successfully\n";
                } else {
                    echo "   ❌ Notifications failed\n";
                }
                
            } catch (Exception $e) {
                echo "   ❌ Error: " . $e->getMessage() . "\n";
            }
            
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error in admin workflow simulation: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 Step 3: Check member status specifically\n";
echo "===========================================\n";

// Check Sandra's status specifically
$stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
$stmt->execute();
$sandra = $stmt->fetch();

if ($sandra) {
    echo "Sandra Stern details:\n";
    echo "  ID: " . $sandra['id'] . "\n";
    echo "  Full name: " . $sandra['full_name'] . "\n";
    echo "  Email: " . $sandra['email'] . "\n";
    echo "  Birth date: " . $sandra['birth_date'] . "\n";
    echo "  Status: " . ($sandra['status'] ?? 'not set') . "\n";
    echo "  Created: " . ($sandra['created_at'] ?? 'not set') . "\n";
    
    // Check if status is the issue
    if ($sandra['status'] !== 'active') {
        echo "  ❌ ISSUE FOUND: Sandra's status is '" . $sandra['status'] . "' instead of 'active'\n";
        echo "  This would exclude her from birthday notifications!\n";
        
        echo "\n🔧 Fixing Sandra's status...\n";
        $stmt = $pdo->prepare("UPDATE members SET status = 'active' WHERE id = ?");
        $result = $stmt->execute([$sandra['id']]);
        
        if ($result) {
            echo "  ✅ Sandra's status updated to 'active'\n";
            echo "  Please try the birthday notifications again!\n";
        } else {
            echo "  ❌ Failed to update Sandra's status\n";
        }
    } else {
        echo "  ✅ Sandra's status is 'active' - this is not the issue\n";
    }
} else {
    echo "❌ Sandra not found in database\n";
}

echo "\n🎯 Summary:\n";
echo "===========\n";
echo "If you're still getting 'no recipient within 7 days' error:\n";
echo "1. Check if the member's status is 'active'\n";
echo "2. Check if the member has a valid email address\n";
echo "3. Check if the birth_date is correctly formatted\n";
echo "4. Verify the current date calculation is correct\n";
echo "\nThe most likely issue is that the member status is not 'active'.\n";
?>
