<?php
require_once 'config.php';

echo "🔍 Detailed {member_image} Replacement Debug\n";
echo "===========================================\n\n";

// Get a test member with an image
$stmt = $pdo->query("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$birthdayMember = $stmt->fetch();

// Get another member to receive the notification
$stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? LIMIT 1");
$stmt->execute([$birthdayMember['id']]);
$recipientMember = $stmt->fetch();

// Create member data
$memberData = [
    'id' => $recipientMember['id'],
    'full_name' => $recipientMember['full_name'],
    'first_name' => $recipientMember['first_name'],
    'email' => $recipientMember['email'],
    
    // Birthday member data
    'birthday_member_id' => $birthdayMember['id'],
    'birthday_member_full_name' => $birthdayMember['full_name'],
    'birthday_member_first_name' => $birthdayMember['first_name'],
    'birthday_member_email' => $birthdayMember['email'],
    'birthday_member_age' => 25,
    
    // Image data
    'image_path' => $birthdayMember['image_path'],
    'member_image_url' => 'http://localhost/campaign/church/' . $birthdayMember['image_path'],
    'birthday_member_image_url' => 'http://localhost/campaign/church/' . $birthdayMember['image_path'],
    
    // Birthday notification flag
    '_is_birthday_notification' => true,
    '_birthday_member_original_image_path' => $birthdayMember['image_path'],
    
    // Other placeholders
    'recipient_full_name' => $recipientMember['full_name'],
    'days_text' => 'in 3 days',
    'upcoming_birthday_formatted' => 'July 22, 2025',
    'organization_name' => 'Freedom Assembly',
];

echo "🎯 Testing the replaceTemplatePlaceholders function directly...\n\n";

// Simple test content with just the placeholder
$testContent = '<div class="member-section">{member_image}<div class="member-name">Test</div></div>';

echo "📝 Test Content: " . $testContent . "\n\n";

// Call the function and see what happens
$result = replaceTemplatePlaceholders($testContent, $memberData);

echo "📤 Result: " . $result . "\n\n";

echo "🔍 Analysis:\n";
echo "============\n";
echo "Original contains {member_image}: " . (strpos($testContent, '{member_image}') !== false ? 'YES' : 'NO') . "\n";
echo "Result contains {member_image}: " . (strpos($result, '{member_image}') !== false ? 'YES - NOT REPLACED' : 'NO - REPLACED') . "\n";
echo "Result contains <img: " . (strpos($result, '<img') !== false ? 'YES' : 'NO') . "\n";

if (strpos($result, '<img') !== false) {
    preg_match('/<img[^>]*>/', $result, $matches);
    echo "IMG tag found: " . $matches[0] . "\n";
}

// Let's also test the replacement array directly
echo "\n🧪 Testing replacement array directly...\n";
echo "========================================\n";

// Manually check what the replacement array would contain
$isBirthdayNotification = !empty($memberData['_is_birthday_notification']);
echo "Is birthday notification: " . ($isBirthdayNotification ? 'YES' : 'NO') . "\n";

if ($isBirthdayNotification) {
    $memberImageUrl = $memberData['member_image_url'] ?? '';
    $altName = $memberData['birthday_member_full_name'] ?? 'Birthday Member';
    $memberImageHtml = '<img src="' . $memberImageUrl . '" alt="' . htmlspecialchars($altName) . '" style="display:block; width:160px; height:160px; border-radius:50%; margin:15px auto; object-fit:cover; border:6px solid #fff; box-shadow:0 4px 8px rgba(0,0,0,0.1);">';
    
    echo "Member image URL: " . $memberImageUrl . "\n";
    echo "Alt name: " . $altName . "\n";
    echo "Member image HTML: " . $memberImageHtml . "\n";
    
    // Test manual replacement
    $manualResult = str_replace('{member_image}', $memberImageHtml, $testContent);
    echo "\nManual replacement result: " . $manualResult . "\n";
    echo "Manual replacement worked: " . (strpos($manualResult, '{member_image}') === false ? 'YES' : 'NO') . "\n";
}
?>
