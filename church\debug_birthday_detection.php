<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Debugging Birthday Detection System\n";
echo "======================================\n\n";

echo "📅 Current Date Information:\n";
echo "============================\n";
$currentDate = new DateTime();
echo "Current Date: " . $currentDate->format('Y-m-d H:i:s') . "\n";
echo "Current Day/Month: " . $currentDate->format('m-d') . "\n";
echo "Timezone: " . $currentDate->getTimezone()->getName() . "\n\n";

echo "🎂 Checking Members with Birthdays on July 21st:\n";
echo "=================================================\n";

// Check for members with birthday on July 21st
$stmt = $pdo->prepare("SELECT id, full_name, first_name, birth_date, email FROM members WHERE birth_date IS NOT NULL");
$stmt->execute();
$allMembers = $stmt->fetchAll();

$july21Members = [];
foreach ($allMembers as $member) {
    if ($member['birth_date']) {
        $birthDate = new DateTime($member['birth_date']);
        $birthMonthDay = $birthDate->format('m-d');
        
        if ($birthMonthDay === '07-21') {
            $july21Members[] = $member;
            echo "✅ Found: " . $member['full_name'] . " (ID: " . $member['id'] . ") - " . $member['birth_date'] . "\n";
            echo "   Email: " . ($member['email'] ?? 'none') . "\n";
        }
    }
}

if (empty($july21Members)) {
    echo "❌ No members found with birthday on July 21st\n";
} else {
    echo "📊 Total members with July 21st birthday: " . count($july21Members) . "\n";
}

echo "\n🔍 Testing Birthday Detection Logic:\n";
echo "====================================\n";

// Test the birthday detection for the next 7 days
for ($i = 0; $i <= 7; $i++) {
    $testDate = clone $currentDate;
    $testDate->add(new DateInterval("P{$i}D"));
    $testMonthDay = $testDate->format('m-d');
    
    echo "Day +$i (" . $testDate->format('Y-m-d') . ", " . $testMonthDay . "): ";
    
    $count = 0;
    foreach ($allMembers as $member) {
        if ($member['birth_date']) {
            $birthDate = new DateTime($member['birth_date']);
            $birthMonthDay = $birthDate->format('m-d');
            
            if ($birthMonthDay === $testMonthDay) {
                $count++;
            }
        }
    }
    
    echo "$count members\n";
    
    if ($count > 0 && $testMonthDay === '07-21') {
        echo "   ✅ July 21st members found on day +$i\n";
    }
}

echo "\n🔍 Testing BirthdayReminder Class Logic:\n";
echo "=======================================\n";

$birthdayReminder = new BirthdayReminder($pdo);

// Use reflection to access private methods
$reflection = new ReflectionClass($birthdayReminder);

// Check if there's a method for getting upcoming birthdays
$methods = $reflection->getMethods();
echo "Available methods in BirthdayReminder:\n";
foreach ($methods as $method) {
    if (strpos(strtolower($method->getName()), 'birthday') !== false || 
        strpos(strtolower($method->getName()), 'upcoming') !== false ||
        strpos(strtolower($method->getName()), 'notification') !== false) {
        echo "  - " . $method->getName() . " (" . ($method->isPublic() ? 'public' : 'private') . ")\n";
    }
}

echo "\n🔍 Testing Notification System:\n";
echo "===============================\n";

// Try to run the notification system and see what happens
try {
    echo "Attempting to send member birthday notifications...\n";
    
    // Check if there's a public method to send notifications
    if (method_exists($birthdayReminder, 'sendMemberBirthdayNotifications')) {
        $result = $birthdayReminder->sendMemberBirthdayNotifications();
        echo "Result: " . ($result ? 'Success' : 'Failed') . "\n";
    } else {
        echo "sendMemberBirthdayNotifications method not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🔍 Checking Database Query for Upcoming Birthdays:\n";
echo "==================================================\n";

// Test the actual SQL query that should find upcoming birthdays
$sql = "SELECT id, full_name, first_name, birth_date, email 
        FROM members 
        WHERE birth_date IS NOT NULL 
        AND email IS NOT NULL 
        AND email != ''";

$stmt = $pdo->prepare($sql);
$stmt->execute();
$membersWithEmail = $stmt->fetchAll();

echo "Members with email and birth_date: " . count($membersWithEmail) . "\n";

// Check upcoming birthdays manually
$upcomingBirthdays = [];
$today = new DateTime();

foreach ($membersWithEmail as $member) {
    $birthDate = new DateTime($member['birth_date']);
    $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
    
    // If birthday already passed this year, check next year
    if ($thisYearBirthday < $today) {
        $thisYearBirthday->add(new DateInterval('P1Y'));
    }
    
    $daysUntilBirthday = $today->diff($thisYearBirthday)->days;
    
    if ($daysUntilBirthday <= 7) {
        $upcomingBirthdays[] = [
            'member' => $member,
            'days' => $daysUntilBirthday,
            'birthday_date' => $thisYearBirthday->format('Y-m-d')
        ];
    }
}

echo "Upcoming birthdays in next 7 days: " . count($upcomingBirthdays) . "\n";

foreach ($upcomingBirthdays as $upcoming) {
    echo "  - " . $upcoming['member']['full_name'] . " in " . $upcoming['days'] . " days (" . $upcoming['birthday_date'] . ")\n";
}

if (empty($upcomingBirthdays)) {
    echo "❌ No upcoming birthdays found - this explains the error message!\n";
    echo "\n🔍 Debugging why no birthdays are found:\n";
    echo "========================================\n";
    
    // Check if there are any members with July 21st birthday and email
    foreach ($july21Members as $member) {
        echo "July 21st member: " . $member['full_name'] . "\n";
        echo "  Has email: " . (!empty($member['email']) ? 'YES (' . $member['email'] . ')' : 'NO') . "\n";
        
        if (!empty($member['email'])) {
            $birthDate = new DateTime($member['birth_date']);
            $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
            
            if ($thisYearBirthday < $today) {
                $thisYearBirthday->add(new DateInterval('P1Y'));
            }
            
            $daysUntilBirthday = $today->diff($thisYearBirthday)->days;
            echo "  Days until birthday: " . $daysUntilBirthday . "\n";
            echo "  This year birthday: " . $thisYearBirthday->format('Y-m-d') . "\n";
            echo "  Should be included: " . ($daysUntilBirthday <= 7 ? 'YES' : 'NO') . "\n";
        }
        echo "\n";
    }
}
?>
