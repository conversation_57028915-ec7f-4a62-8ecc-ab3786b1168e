<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

// Test the upcoming birthday notification templates specifically
echo "Testing Upcoming Birthday Notification Templates...\n";

// Get a test member
$stmt = $pdo->query("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$testMember = $stmt->fetch();

if (!$testMember) {
    echo "No test member found with photo\n";
    exit;
}

echo "Using test member: " . $testMember['full_name'] . "\n";

// Get the upcoming birthday notification templates
$templates = [];
for ($i = 1; $i <= 3; $i++) {
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name = ?");
    $stmt->execute(["Member Upcoming Birthday Notification $i"]);
    $template = $stmt->fetch();
    if ($template) {
        $templates[] = $template;
        echo "Found template: " . $template['template_name'] . "\n";
    }
}

if (empty($templates)) {
    echo "No upcoming birthday notification templates found\n";
    exit;
}

// Create birthday reminder instance
$birthdayReminder = new BirthdayReminder($pdo);

// Test each template
foreach ($templates as $template) {
    echo "\n--- Testing template: " . $template['template_name'] . " ---\n";
    
    // Create test member data for birthday notification
    $memberData = [
        'id' => $testMember['id'],
        'full_name' => $testMember['full_name'],
        'first_name' => $testMember['first_name'],
        'last_name' => $testMember['last_name'],
        'email' => $testMember['email'],
        'birthday_member_name' => $testMember['full_name'],
        'birthday_member_full_name' => $testMember['full_name'],
        'birthday_member_photo_url' => get_base_url() . '/' . ltrim($testMember['image_path'], '/'),
        'birthday_member_image_url' => get_base_url() . '/' . ltrim($testMember['image_path'], '/'),
        'birthday_member_age' => '30',
        'days_until_birthday' => '3',
        'birthday_date' => date('F j', strtotime('+3 days')),
        'is_birthday_notification' => true  // NEW FLAG
    ];
    
    // Test the placeholder replacement directly
    $content = $template['content'];
    $subject = $template['subject'];

    echo "Original content contains {member_image}: " . (strpos($content, '{member_image}') !== false ? 'YES' : 'NO') . "\n";

    // Process the template content with birthday notification flag
    $processedContent = replaceTemplatePlaceholders($content, $memberData);
    $processedSubject = replaceTemplatePlaceholders($subject, $memberData);

    echo "Processed content contains {member_image}: " . (strpos($processedContent, '{member_image}') !== false ? 'YES' : 'NO') . "\n";
    echo "Processed content contains <img: " . (strpos($processedContent, '<img') !== false ? 'YES' : 'NO') . "\n";
    echo "Processed content contains cid:: " . (strpos($processedContent, 'cid:') !== false ? 'YES' : 'NO') . "\n";

    // Test sending the email
    $result = sendEmail(
        $testMember['email'],
        $testMember['full_name'],
        $processedSubject,
        $processedContent,
        true,
        $memberData
    );

    echo "Email sent: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
}

echo "\nTest completed. Check email_debug.log for details.\n";
?>
