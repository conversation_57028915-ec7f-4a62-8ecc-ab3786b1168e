<?php
/**
 * Email Attachment Diagnostic Script
 * This script will help us understand exactly what's happening with birthday email attachments
 */

require_once 'config.php';
require_once 'includes/email_functions.php';

// Test configuration
$TEST_EMAIL = '<EMAIL>'; // CHANGE THIS
$TEST_NAME = 'Test User';

function logDiagnostic($message) {
    $logFile = __DIR__ . '/logs/email_attachment_diagnostic.log';
    if (!file_exists(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND);
    echo "[$timestamp] $message\n";
}

function createTestBirthdayData() {
    return [
        'id' => 999, // Test member ID
        'member_id' => 999, // Required for sendScheduledEmail
        'full_name' => '<PERSON>',
        'first_name' => '<PERSON>',
        'last_name' => '<PERSON>',
        'email' => '<EMAIL>',
        'birth_date' => '1990-05-15',
        'image_path' => 'uploads/members/test_birthday.jpg',
        'member_image_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
        'birthday_member_name' => 'Jane',
        'birthday_member_full_name' => 'Jane Birthday Smith',
        'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
        'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
        'birthday_member_age' => 34,
        '_is_birthday_notification' => true,
        '_original_image_path' => 'uploads/members/test_birthday.jpg',
        '_birthday_member_original_image_path' => 'uploads/members/test_birthday.jpg'
    ];
}

function createTestImage() {
    $uploadsDir = __DIR__ . '/uploads/members';
    if (!file_exists($uploadsDir)) {
        mkdir($uploadsDir, 0755, true);
    }
    
    $imagePath = $uploadsDir . '/test_birthday.jpg';
    if (!file_exists($imagePath)) {
        // Create a simple test image
        $image = imagecreatetruecolor(200, 200);
        $bgColor = imagecolorallocate($image, 255, 192, 203); // Pink background
        $textColor = imagecolorallocate($image, 0, 0, 0);
        imagefill($image, 0, 0, $bgColor);
        imagestring($image, 5, 50, 90, 'BIRTHDAY', $textColor);
        imagestring($image, 5, 70, 110, 'TEST', $textColor);
        imagejpeg($image, $imagePath, 90);
        imagedestroy($image);
        logDiagnostic("Created test birthday image: $imagePath");
    }
    return $imagePath;
}

function testSendEmail() {
    global $TEST_EMAIL, $TEST_NAME;
    
    logDiagnostic("=== TESTING sendEmail() function (config.php) ===");
    
    $memberData = createTestBirthdayData();
    $emailBody = '
    <html>
    <body>
        <h2>🎉 Birthday Test - sendEmail() Function</h2>
        <p>Dear {full_name},</p>
        <p>Testing birthday email with {birthday_member_full_name}\'s image:</p>
        <div style="text-align: center;">
            <img src="{birthday_member_photo_url}" alt="Birthday Member" style="width:150px;height:150px;border-radius:50%;">
        </div>
        <p>This email was sent using the sendEmail() function from config.php</p>
    </body>
    </html>';
    
    // Process template placeholders
    $processedBody = replaceTemplatePlaceholders($emailBody, $memberData);
    
    logDiagnostic("Calling sendEmail() with birthday notification data...");
    $result = sendEmail($TEST_EMAIL, $TEST_NAME, 'TEST: sendEmail() Birthday Function', $processedBody, true, $memberData);
    
    logDiagnostic("sendEmail() result: " . ($result ? "SUCCESS" : "FAILED"));
    return $result;
}

function testSendScheduledEmail() {
    global $TEST_EMAIL, $TEST_NAME;
    
    logDiagnostic("=== TESTING sendScheduledEmail() function (email_functions.php) ===");
    
    $memberData = createTestBirthdayData();
    $emailBody = '
    <html>
    <body>
        <h2>🎂 Birthday Test - sendScheduledEmail() Function</h2>
        <p>Dear {full_name},</p>
        <p>Testing birthday email with {birthday_member_full_name}\'s image:</p>
        <div style="text-align: center;">
            <img src="{birthday_member_photo_url}" alt="Birthday Member" style="width:150px;height:150px;border-radius:50%;">
        </div>
        <p>This email was sent using the sendScheduledEmail() function from email_functions.php</p>
    </body>
    </html>';
    
    logDiagnostic("Calling sendScheduledEmail() with birthday notification data...");
    $result = sendScheduledEmail(
        $TEST_EMAIL,
        $TEST_NAME,
        'TEST: sendScheduledEmail() Birthday Function',
        $emailBody,
        ['track_opens' => 1, 'member_id' => 999], // Include member_id for database tracking
        $memberData
    );
    
    logDiagnostic("sendScheduledEmail() result: " . ($result ? "SUCCESS" : "FAILED"));
    return $result;
}

function testSendEmailWithPHPMailer() {
    global $TEST_EMAIL, $TEST_NAME;
    
    logDiagnostic("=== TESTING sendEmailWithPHPMailer() function (email_functions.php) ===");
    
    $emailBody = '
    <html>
    <body>
        <h2>📧 Birthday Test - sendEmailWithPHPMailer() Function</h2>
        <p>Dear Test User,</p>
        <p>Testing birthday email with image URL:</p>
        <div style="text-align: center;">
            <img src="http://localhost/campaign/church/uploads/members/test_birthday.jpg" alt="Birthday Member" style="width:150px;height:150px;border-radius:50%;">
        </div>
        <p>This email was sent using the sendEmailWithPHPMailer() function from email_functions.php</p>
    </body>
    </html>';
    
    logDiagnostic("Calling sendEmailWithPHPMailer() with image URL...");
    $result = sendEmailWithPHPMailer(
        $TEST_EMAIL,
        'TEST: sendEmailWithPHPMailer() Birthday Function',
        $emailBody,
        'Church System',
        '<EMAIL>',
        true
    );
    
    logDiagnostic("sendEmailWithPHPMailer() result: " . ($result['success'] ? "SUCCESS" : "FAILED - " . $result['message']));
    return $result['success'];
}

// Main diagnostic execution
function runDiagnostics() {
    logDiagnostic("🔍 Starting Email Attachment Diagnostic");
    logDiagnostic("This will help us understand which function is causing attachment issues");
    
    // Create test image
    createTestImage();
    
    $results = [];
    
    // Test all three email functions
    $results['sendEmail'] = testSendEmail();
    sleep(2);
    
    $results['sendScheduledEmail'] = testSendScheduledEmail();
    sleep(2);
    
    $results['sendEmailWithPHPMailer'] = testSendEmailWithPHPMailer();
    
    // Summary
    logDiagnostic("\n=== DIAGNOSTIC SUMMARY ===");
    foreach ($results as $function => $success) {
        $status = $success ? "✅ SUCCESS" : "❌ FAILED";
        logDiagnostic("$function: $status");
    }
    
    logDiagnostic("\n📋 NEXT STEPS:");
    logDiagnostic("1. Check your email inbox for 3 test emails");
    logDiagnostic("2. For each email, note:");
    logDiagnostic("   - Does the image appear inline in the email body?");
    logDiagnostic("   - Does the email have file attachments?");
    logDiagnostic("   - If yes, what are the attachment names/types?");
    logDiagnostic("3. Check debug logs:");
    logDiagnostic("   - /logs/email_debug.log");
    logDiagnostic("   - /logs/scheduled_email_debug.log");
    logDiagnostic("   - /logs/email_attachment_diagnostic.log");
    
    logDiagnostic("\n🎯 EXPECTED RESULTS (AFTER REAL FIX):");
    logDiagnostic("- sendEmail(): Should have inline image only (NO attachments) ✅");
    logDiagnostic("- sendScheduledEmail(): Should have inline image only (NO attachments) ✅");
    logDiagnostic("- sendEmailWithPHPMailer(): May still have issues (not fixed) ⚠️");

    logDiagnostic("\n🔍 REAL FIX VERIFICATION:");
    logDiagnostic("- Check email debug logs for Content-Disposition headers");
    logDiagnostic("- Should show 'Content-Disposition: inline' WITHOUT filename");
    logDiagnostic("- Should show 'Content-Type: image/jpeg; name=' WITHOUT filename");

    logDiagnostic("\n⚠️  IF ATTACHMENTS STILL APPEAR:");
    logDiagnostic("- The real fix didn't work completely");
    logDiagnostic("- Check Content-Disposition headers in debug logs");
    logDiagnostic("- Look for any filename parameters that shouldn't be there");
    
    return $results;
}

// Run diagnostics
if (php_sapi_name() === 'cli') {
    // Command line
    runDiagnostics();
} else {
    // Web browser
    echo "<pre>";
    runDiagnostics();
    echo "</pre>";
}
?>
