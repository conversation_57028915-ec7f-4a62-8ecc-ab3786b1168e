<?php
require_once 'config.php';

echo "🔧 Fixing Remaining Placeholder Issues\n";
echo "=======================================\n\n";

// Check which template is actually being used
$stmt = $pdo->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'notification'");
$stmt->execute();
$settings = $stmt->fetch();

if ($settings && !empty($settings['template_ids'])) {
    $templateIds = explode(',', $settings['template_ids']);
    echo "📋 Templates configured for notifications: " . implode(', ', $templateIds) . "\n\n";
    
    foreach ($templateIds as $templateId) {
        $templateId = trim($templateId);
        if (empty($templateId)) continue;
        
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch();
        
        if ($template) {
            echo "📧 Checking template ID $templateId: " . $template['template_name'] . "\n";
            
            $content = $template['content'];
            $subject = $template['subject'];
            $updated = false;
            
            // Check for the specific issues you mentioned
            if (strpos($content, '{days_until_birthday_plural}') !== false) {
                echo "  ❌ Found {days_until_birthday_plural} placeholder\n";
                
                // Fix the days plural issue properly
                $content = preg_replace(
                    '/(\d+)\s+day\{days_until_birthday_plural\}/',
                    '$1 {days_text}',
                    $content
                );
                
                // Also handle the case without numbers
                $content = str_replace(
                    'day{days_until_birthday_plural}',
                    '{days_text}',
                    $content
                );
                
                $updated = true;
                echo "  ✅ Fixed days plural issue\n";
            }
            
            if (strpos($content, '{birthday_date_formatted}') !== false) {
                echo "  ❌ Found {birthday_date_formatted} placeholder\n";
                $content = str_replace('{birthday_date_formatted}', '{birthday_date}', $content);
                $updated = true;
                echo "  ✅ Fixed birthday date placeholder\n";
            }
            
            // Fix hardcoded Sandra references
            if (strpos($content, 'Sandra') !== false && strpos($content, '{birthday_member') === false) {
                echo "  ❌ Found hardcoded 'Sandra' references\n";
                $content = str_replace('Sandra\'s birthday', '{birthday_member_full_name}\'s birthday', $content);
                $content = str_replace('Sandra Stern', '{birthday_member_full_name}', $content);
                $content = str_replace('🎂 Sandra 🎂', '🎂 {birthday_member_name} 🎂', $content);
                $content = str_replace('celebrate Sandra\'s', 'celebrate {birthday_member_full_name}\'s', $content);
                $updated = true;
                echo "  ✅ Fixed hardcoded Sandra references\n";
            }
            
            // Fix hardcoded age
            if (strpos($content, '43 Years') !== false) {
                echo "  ❌ Found hardcoded age '43 Years'\n";
                $content = str_replace('43 Years', '{birthday_member_age} Years', $content);
                $updated = true;
                echo "  ✅ Fixed hardcoded age\n";
            }
            
            // Fix hardcoded email
            if (strpos($content, '<EMAIL>') !== false) {
                echo "  ❌ Found hardcoded email address\n";
                $content = str_replace('<EMAIL>', '{birthday_member_email}', $content);
                $content = str_replace('Happy Birthday Sandra!', 'Happy Birthday {birthday_member_name}!', $content);
                $updated = true;
                echo "  ✅ Fixed hardcoded email address\n";
            }
            
            // Fix recipient name
            if (strpos($content, 'Dear {recipient_name}') !== false) {
                echo "  ❌ Found {recipient_name} placeholder\n";
                $content = str_replace('Dear {recipient_name}', 'Dear {first_name}', $content);
                $updated = true;
                echo "  ✅ Fixed recipient name placeholder\n";
            }
            
            if ($updated) {
                // Update the template
                $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
                $result = $stmt->execute([$content, $templateId]);
                
                if ($result) {
                    echo "  ✅ Template updated successfully!\n";
                } else {
                    echo "  ❌ Failed to update template\n";
                }
            } else {
                echo "  ✅ Template looks good\n";
            }
            
            echo "\n";
        }
    }
} else {
    echo "❌ No templates configured for notifications\n";
    echo "Let me check all birthday notification templates...\n\n";
    
    // Check all birthday notification templates
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Birthday%' OR template_name LIKE '%Member%'");
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    foreach ($templates as $template) {
        echo "📧 Checking template: " . $template['template_name'] . " (ID: " . $template['id'] . ")\n";
        
        $content = $template['content'];
        
        // Show a sample of the content to identify issues
        $preview = substr(strip_tags($content), 0, 200) . '...';
        echo "  Preview: $preview\n";
        
        // Check for problematic placeholders
        if (strpos($content, '{days_until_birthday_plural}') !== false) {
            echo "  ❌ Has {days_until_birthday_plural} issue\n";
        }
        if (strpos($content, '{birthday_date_formatted}') !== false) {
            echo "  ❌ Has {birthday_date_formatted} issue\n";
        }
        if (strpos($content, 'Sandra') !== false && strpos($content, '{birthday_member') === false) {
            echo "  ❌ Has hardcoded Sandra references\n";
        }
        
        echo "\n";
    }
}

echo "🧪 Testing with a fresh notification:\n";
echo "====================================\n";

// Test again
try {
    require_once 'send_birthday_reminders.php';
    $birthdayReminder = new BirthdayReminder($pdo);
    
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        $today = new DateTime();
        $birthDate = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        echo "Testing notification for Sandra (days until: $daysUntil)...\n";
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Notifications sent successfully!\n";
            echo "Template used: " . ($result['template'] ?? 'unknown') . "\n";
            echo "Sent to " . $result['success'] . " recipients\n";
        } else {
            echo "❌ Notification failed\n";
            var_dump($result);
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎯 NEXT STEPS:\n";
echo "==============\n";
echo "1. Check your email again - the placeholders should now be properly replaced\n";
echo "2. The email should show actual values instead of {placeholder} text\n";
echo "3. If you still see placeholder text, let me know which specific template is being used\n";
?>
