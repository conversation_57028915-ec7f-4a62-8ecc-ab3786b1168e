<?php
require_once 'config.php';

echo "🔍 Checking Admin Accounts\n";
echo "=========================\n\n";

try {
    // Check if admins table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admins'");
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo "❌ Admins table does not exist\n";
        exit;
    }
    
    // Get all admin accounts
    $stmt = $pdo->query("SELECT id, username, full_name, email, status, created_at, last_login_at FROM admins");
    $admins = $stmt->fetchAll();
    
    if (empty($admins)) {
        echo "❌ No admin accounts found\n";
        echo "\n🔧 Creating default admin account...\n";
        
        // Create a default admin account
        $defaultUsername = 'admin';
        $defaultPassword = 'admin123';
        $defaultEmail = '<EMAIL>';
        $defaultName = 'System Administrator';
        
        // Hash the password
        $hashedPassword = password_hash($defaultPassword, PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO admins (username, password, full_name, email, status, created_at) VALUES (?, ?, ?, ?, 'active', NOW())");
        $result = $stmt->execute([$defaultUsername, $hashedPassword, $defaultName, $defaultEmail]);
        
        if ($result) {
            echo "✅ Default admin account created successfully!\n";
            echo "   Username: $defaultUsername\n";
            echo "   Password: $defaultPassword\n";
            echo "   Email: $defaultEmail\n";
            echo "\n⚠️  IMPORTANT: Please change this password after logging in!\n";
        } else {
            echo "❌ Failed to create default admin account\n";
        }
        
    } else {
        echo "✅ Found " . count($admins) . " admin account(s):\n\n";
        
        foreach ($admins as $admin) {
            echo "ID: " . $admin['id'] . "\n";
            echo "Username: " . $admin['username'] . "\n";
            echo "Full Name: " . $admin['full_name'] . "\n";
            echo "Email: " . $admin['email'] . "\n";
            echo "Status: " . $admin['status'] . "\n";
            echo "Created: " . $admin['created_at'] . "\n";
            echo "Last Login: " . ($admin['last_login_at'] ?? 'Never') . "\n";
            echo "---\n";
        }
        
        echo "\n🔑 You can use any of these accounts to log in to the admin interface.\n";
        echo "If you don't know the password, you can reset it through the forgot password feature.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🌐 Admin Interface URL: http://localhost/campaign/church/admin/login.php\n";
echo "🎂 Birthday Notifications URL: http://localhost/campaign/church/admin/send_birthday_notification.php\n";
?>
