<?php
/**
 * Test Real Birthday Celebration Email
 * This will send an actual birthday celebration email to verify our fixes work
 */

require_once 'config.php';
require_once 'classes/BirthdayReminder.php';

echo "<h1>🎂 Real Birthday Celebration Email Test</h1>";

// Test configuration
$testEmail = '<EMAIL>';
$testName = 'Test User';

echo "<p><strong>Purpose:</strong> Send a real birthday celebration email to verify systematic fix works</p>";
echo "<p><strong>Test Email:</strong> $testEmail</p>";

// Clear previous debug logs for clean testing
$debugLogFile = __DIR__ . '/logs/email_debug.log';
if (file_exists($debugLogFile)) {
    // Keep last 50 lines and add separator
    $lines = file($debugLogFile);
    $keepLines = array_slice($lines, -50);
    $keepLines[] = "\n" . str_repeat("=", 80) . "\n";
    $keepLines[] = "[" . date('Y-m-d H:i:s') . "] REAL BIRTHDAY EMAIL TEST: Starting test\n";
    $keepLines[] = str_repeat("=", 80) . "\n\n";
    file_put_contents($debugLogFile, implode('', $keepLines));
}

echo "<h2>🧪 Sending Real Birthday Celebration Email</h2>";

echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 20px; border-radius: 10px;'>";
echo "<h3>📧 Test: Birthday Celebration Email via BirthdayReminder Class</h3>";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Create test member data that simulates a real birthday member
    $testMember = [
        'id' => 999,
        'full_name' => 'Test Birthday Celebration',
        'first_name' => 'Test',
        'last_name' => 'Birthday',
        'email' => $testEmail,
        'birth_date' => '1985-07-19', // Today's date for testing
        'image_path' => 'uploads/members/test_birthday.jpg',
        'phone_number' => '555-0123'
    ];
    
    // Prepare member data using the class method
    $memberData = $birthdayReminder->prepareMemberDataWithImage($testMember);
    
    // Manually add the birthday notification flag (this is normally done in sendBirthdayEmails)
    $memberData['_is_birthday_notification'] = true;
    $memberData['_birthday_member_original_image_path'] = $testMember['image_path'];
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>Member Data for Email:</h4>";
    echo "<ul>";
    echo "<li><strong>_is_birthday_notification:</strong> " . (isset($memberData['_is_birthday_notification']) ? 'SET ✅' : 'NOT SET ❌') . "</li>";
    echo "<li><strong>_original_image_path:</strong> " . (isset($memberData['_original_image_path']) ? $memberData['_original_image_path'] . ' ✅' : 'NOT SET ❌') . "</li>";
    echo "<li><strong>_birthday_member_original_image_path:</strong> " . (isset($memberData['_birthday_member_original_image_path']) ? $memberData['_birthday_member_original_image_path'] . ' ✅' : 'NOT SET ❌') . "</li>";
    echo "<li><strong>birthday_member_image_url:</strong> " . (isset($memberData['birthday_member_image_url']) ? $memberData['birthday_member_image_url'] . ' ✅' : 'NOT SET ❌') . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Create a simple birthday email template
    $subject = "🎉 Happy Birthday Test Birthday! 🎂";
    
    $body = '
    <html>
    <head>
        <title>Birthday Celebration</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .member-photo { text-align: center; margin: 20px 0; }
            .birthday-details { background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎉 Happy Birthday Celebration! 🎉</h1>
                <p>Let\'s celebrate a cherished member of our church family! 💒</p>
            </div>
            
            <div class="content">
                <div class="greeting">
                    <p>Dear ' . htmlspecialchars($testName) . ',</p>
                    <p>🎂 We are thrilled to celebrate Test Birthday\'s birthday today! 🎉</p>
                </div>
                
                <div class="member-photo">
                    <img src="' . $memberData['birthday_member_image_url'] . '" alt="Test Birthday" style="display:block; width:160px; height:160px; border-radius:50%; margin:15px auto; object-fit:cover; border:6px solid #fff; box-shadow:0 4px 8px rgba(0,0,0,0.1);">
                </div>
                
                <div class="birthday-details">
                    <div class="birthday-date">🎈 Birthday: Saturday, July 19, 2025</div>
                    <div class="birthday-age">🎂 Turning 40 years</div>
                </div>
                
                <div class="suggestions">
                    <h3>🎁 Ways to Bless Test Birthday:</h3>
                    <ul>
                        <li>💌 Send a heartfelt birthday message</li>
                        <li>🙏 Pray for their growth and blessings</li>
                        <li>📖 Share a scripture that uplifts them</li>
                        <li>🎁 Gift them something meaningful</li>
                    </ul>
                </div>
                
                <div class="footer">
                    <p>🎂 With blessings from <strong>Freedom Assembly Church</strong> 💒</p>
                </div>
            </div>
        </div>
    </body>
    </html>';
    
    echo "<div style='background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>📧 Sending Birthday Celebration Email...</h4>";
    echo "<p><strong>Subject:</strong> " . htmlspecialchars($subject) . "</p>";
    echo "<p><strong>Image URL in HTML:</strong> " . htmlspecialchars($memberData['birthday_member_image_url']) . "</p>";
    echo "</div>";
    
    // Send the email using our systematic fix
    $result = sendEmail($testEmail, $testName, $subject, $body, true, $memberData);
    
    if ($result) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ SUCCESS: Birthday celebration email sent!</h4>";
        echo "<p><strong>Expected Results:</strong></p>";
        echo "<ul>";
        echo "<li>✅ Email should be detected as birthday notification</li>";
        echo "<li>✅ Image should be embedded inline with space character filename</li>";
        echo "<li>✅ Content-Disposition header should be 'inline' without filename</li>";
        echo "<li>✅ NO file attachments should appear in email client</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>❌ FAILED: Could not send birthday celebration email</h4>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ ERROR: " . htmlspecialchars($e->getMessage()) . "</h4>";
    echo "</div>";
}

echo "</div>";

// Wait a moment for logs to be written
sleep(1);

echo "<h2>📋 Debug Log Analysis</h2>";

// Analyze the debug logs
if (file_exists($debugLogFile)) {
    $logContent = file_get_contents($debugLogFile);
    $testLogs = explode("REAL BIRTHDAY EMAIL TEST: Starting test", $logContent);
    $currentTestLogs = end($testLogs);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🔍 Key Systematic Fix Indicators:</h3>";
    
    // Check for systematic fix messages
    $systematicFixCount = substr_count($currentTestLogs, 'SYSTEMATIC FIX:');
    $birthdayDetectedCount = substr_count($currentTestLogs, 'Birthday notification detected');
    $filenameValidationCount = substr_count($currentTestLogs, "filename=' ' (length=1)");
    $successfulEmbeddingCount = substr_count($currentTestLogs, 'Successfully embedded');
    $urlReplacementCount = substr_count($currentTestLogs, 'URL replacement');
    
    echo "<ul>";
    echo "<li><strong>Systematic Fix Messages:</strong> $systematicFixCount</li>";
    echo "<li><strong>Birthday Notification Detected:</strong> $birthdayDetectedCount</li>";
    echo "<li><strong>Filename Validations:</strong> $filenameValidationCount</li>";
    echo "<li><strong>Successful Embeddings:</strong> $successfulEmbeddingCount</li>";
    echo "<li><strong>URL Replacements:</strong> $urlReplacementCount</li>";
    echo "</ul>";
    
    // Check for Content-Disposition headers
    $inlineDispositionCount = substr_count($currentTestLogs, 'Content-Disposition: inline');
    $inlineWithFilenameCount = substr_count($currentTestLogs, 'Content-Disposition: inline; filename=');
    
    echo "<h4>📧 Email Header Analysis:</h4>";
    echo "<ul>";
    echo "<li><strong>Clean 'Content-Disposition: inline':</strong> $inlineDispositionCount</li>";
    echo "<li><strong>With filename (BAD):</strong> $inlineWithFilenameCount</li>";
    echo "</ul>";
    
    if ($inlineWithFilenameCount > 0) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ WARNING: Some Content-Disposition headers still have filenames!</h4>";
        echo "</div>";
    } else if ($inlineDispositionCount > 0) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ SUCCESS: All Content-Disposition headers are clean (no filenames)</h4>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // Show recent log entries
    echo "<h3>📄 Recent Debug Log Entries:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;'>";
    echo "<pre>" . htmlspecialchars(substr($currentTestLogs, -2000)) . "</pre>";
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Debug log file not found</h3>";
    echo "</div>";
}

echo "<h2>🎯 Final Verification</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h3>✅ If All Fixes Work Correctly:</h3>";
echo "<ul>";
echo "<li><strong>Debug Logs:</strong> Should show 'Birthday notification detected' and systematic fix messages</li>";
echo "<li><strong>Email Headers:</strong> Should show 'Content-Disposition: inline' without filename</li>";
echo "<li><strong>Email Client:</strong> Should display image inline, NO attachments</li>";
echo "<li><strong>Image Embedding:</strong> Should use space character for filename parameter</li>";
echo "</ul>";
echo "</div>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<strong>🚀 This test verifies that birthday celebration emails now work with our systematic image embedding fix!</strong>";
echo "</p>";
?>
