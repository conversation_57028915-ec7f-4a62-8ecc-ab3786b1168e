<?php
/**
 * Test script to verify that the upcoming birthday notification image embedding fix is working
 */

require_once 'config.php';

// Test member data for upcoming birthday notification
$testMemberData = [
    'full_name' => 'Jane Test Birthday',
    'first_name' => '<PERSON>',
    'last_name' => 'Test Birthday',
    'email' => '<EMAIL>',
    'birthday_member_name' => 'Jane Test Birthday',
    'birthday_member_full_name' => 'Jane Test Birthday',
    'birthday_member_email' => '<EMAIL>',
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    'birthday_member_image' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    'image_path' => 'uploads/members/test_birthday.jpg',
    '_original_image_path' => 'uploads/members/test_birthday.jpg',
    '_birthday_member_original_image_path' => 'uploads/members/test_birthday.jpg',
    '_is_birthday_notification' => true,
    'upcoming_birthday_date' => 'July 19, 2025',
    'days_text' => 'today',
    'age' => '25'
];

// Test template content with {birthday_member_image_url} placeholder
$testTemplateContent = '
<div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f4f7fc; padding: 25px;">
    <div style="text-align: center; background-color: #ffffff; padding: 25px; border-radius: 12px; box-shadow: 0 5px 10px rgba(0,0,0,0.15);">
        <h1 style="color: #e67e22; font-size: 32px; margin-bottom: 15px;">🎉🎂 Upcoming Birthday! 🎂🎉</h1>
        
        <img src="{birthday_member_image_url}" alt="{birthday_member_full_name}" style="width: 160px; height: 160px; border-radius: 50%; margin: 15px auto; display: block; object-fit: cover; border: 6px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />
        
        <div style="background-color: #ecf3fc; padding: 20px; border-radius: 12px; margin: 20px 0; border-left: 5px solid #3498db;">
            <p style="font-size: 18px; line-height: 1.6; font-weight: bold; color: #2c3e50;">Dear {first_name},</p>
            <p style="font-size: 16px; line-height: 1.6; color: #444;">
                {birthday_member_full_name} has a birthday {days_text}! 🎈🎁  
                Please join us in celebrating this special day. 🙏✨
            </p>
        </div>
    </div>
</div>';

echo "<h1>Testing Upcoming Birthday Notification Image Embedding Fix</h1>\n";
echo "<h2>1. Testing replaceTemplatePlaceholders function</h2>\n";

// Test the replaceTemplatePlaceholders function
$processedContent = replaceTemplatePlaceholders($testTemplateContent, $testMemberData);

echo "<h3>Original template content:</h3>\n";
echo "<pre>" . htmlspecialchars($testTemplateContent) . "</pre>\n";

echo "<h3>Processed content after replaceTemplatePlaceholders:</h3>\n";
echo "<pre>" . htmlspecialchars($processedContent) . "</pre>\n";

// Check if the placeholder was replaced
if (strpos($processedContent, '{birthday_member_image_url}') !== false) {
    echo "<div style='color: red; font-weight: bold;'>❌ FAILED: {birthday_member_image_url} placeholder was NOT replaced!</div>\n";
} else {
    echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: {birthday_member_image_url} placeholder was replaced!</div>\n";
}

// Check if the URL is correct
if (strpos($processedContent, 'http://localhost/campaign/church/uploads/members/test_birthday.jpg') !== false) {
    echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Correct image URL found in processed content!</div>\n";
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ FAILED: Correct image URL NOT found in processed content!</div>\n";
}

echo "<h2>2. Testing sendEmail function with upcoming birthday notification</h2>\n";

// Test the sendEmail function
$subject = "Test: {birthday_member_full_name}'s Birthday {days_text}!";
$processedSubject = replaceTemplatePlaceholders($subject, $testMemberData);

echo "<h3>Sending test email...</h3>\n";
echo "<p><strong>To:</strong> <EMAIL></p>\n";
echo "<p><strong>Subject:</strong> " . htmlspecialchars($processedSubject) . "</p>\n";

// Send the test email
$emailResult = sendEmail(
    '<EMAIL>',
    'Test User',
    $processedSubject,
    $processedContent,
    true, // HTML format
    $testMemberData // Pass member data for image embedding
);

if ($emailResult) {
    echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Test email sent successfully!</div>\n";
    echo "<p>Check the email debug logs and the <NAME_EMAIL> to verify that:</p>\n";
    echo "<ul>\n";
    echo "<li>The image appears inline (not as an attachment)</li>\n";
    echo "<li>The Content-Disposition header shows 'inline' without filename</li>\n";
    echo "<li>The {birthday_member_image_url} placeholder was replaced with the actual URL</li>\n";
    echo "</ul>\n";
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ FAILED: Test email could not be sent!</div>\n";
}

echo "<h2>3. Check the latest email debug logs</h2>\n";
echo "<p>Check the file: church/logs/email_debug.log for the latest entries to verify the fix is working.</p>\n";

?>
