<?php
/**
 * Detailed Analysis of Upcoming Birthday Notification Templates
 * This will show the full content and image handling for each template
 */

require_once 'config.php';

echo "<h1>🔍 Detailed Notification Template Analysis</h1>";

// Get the specific notification templates
$templateIds = [37, 46, 47];

foreach ($templateIds as $templateId) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>❌ Template ID $templateId Not Found</h3>";
            echo "</div>";
            continue;
        }
        
        echo "<div style='border: 2px solid #007bff; margin: 30px 0; padding: 20px; border-radius: 10px;'>";
        echo "<h2>📧 " . htmlspecialchars($template['template_name']) . "</h2>";
        echo "<p><strong>ID:</strong> " . $template['id'] . "</p>";
        echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
        
        // Analyze image placeholders
        $content = $template['content'];
        echo "<h3>🖼️ Image Placeholder Analysis:</h3>";
        
        $imagePatterns = [
            'birthday_member_image_url' => 'Birthday member image URL',
            'birthday_member_photo_url' => 'Birthday member photo URL', 
            'member_image_url' => 'Member image URL',
            'member_image' => 'Member image (generic)',
            'birthday_member_image' => 'Birthday member image'
        ];
        
        $foundPatterns = [];
        foreach ($imagePatterns as $pattern => $description) {
            if (strpos($content, '{' . $pattern . '}') !== false) {
                $foundPatterns[] = $pattern;
                echo "<span style='color: green; font-weight: bold;'>✅ {" . $pattern . "} - " . $description . "</span><br>";
            }
        }
        
        if (empty($foundPatterns)) {
            echo "<span style='color: red;'>❌ No image placeholders found</span><br>";
        }
        
        // Check for img tags
        echo "<h3>🏷️ Image Tags Analysis:</h3>";
        if (preg_match_all('/<img[^>]*>/i', $content, $matches)) {
            foreach ($matches[0] as $imgTag) {
                echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px;'>";
                echo "<code>" . htmlspecialchars($imgTag) . "</code>";
                echo "</div>";
            }
        } else {
            echo "<span style='color: orange;'>⚠️ No img tags found</span><br>";
        }
        
        // Show full content
        echo "<h3>📄 Full Template Content:</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd;'>";
        echo "<pre style='white-space: pre-wrap; word-wrap: break-word;'>" . htmlspecialchars($content) . "</pre>";
        echo "</div>";
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>❌ Error analyzing template $templateId</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

echo "<h2>🔍 Current Image Embedding Status</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h3>✅ What We Know:</h3>";
echo "<ul>";
echo "<li>Templates 37 & 46 use <code>{member_image}</code> placeholder</li>";
echo "<li>Template 47 uses <code>{birthday_member_image_url}</code> placeholder</li>";
echo "<li>These templates are used for notifying members about OTHER members' upcoming birthdays</li>";
echo "<li>The system uses the <code>sendEmail()</code> function which we've already fixed</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
echo "<h3>🎯 Expected Behavior:</h3>";
echo "<ul>";
echo "<li>When Member A has an upcoming birthday, other members should receive notification emails</li>";
echo "<li>These emails should show Member A's profile image inline (not as attachment)</li>";
echo "<li>The image should be embedded using our ONE-TIME FIX approach</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
echo "<h3>❓ Questions to Investigate:</h3>";
echo "<ul>";
echo "<li>Are the notification emails being sent using the fixed <code>sendEmail()</code> function?</li>";
echo "<li>Are member images being processed correctly in the notification context?</li>";
echo "<li>Do we need to apply additional fixes for the notification-specific image handling?</li>";
echo "</ul>";
echo "</div>";
?>
