<?php
require_once 'config.php';

echo "🔧 Fixing Email Template Display Issues\n";
echo "=======================================\n\n";

echo "📧 1. Checking Current Notification Templates:\n";
echo "==============================================\n";

// Get the current notification templates
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Birthday Notification%' OR template_name LIKE '%Member%Birthday%'");
$stmt->execute();
$templates = $stmt->fetchAll();

if (empty($templates)) {
    echo "❌ No notification templates found\n";
    exit;
}

foreach ($templates as $template) {
    echo "Template ID: " . $template['id'] . " - " . $template['template_name'] . "\n";
    echo "Content length: " . strlen($template['content']) . " characters\n";
    
    // Check if it has inline CSS (which causes display issues)
    if (strpos($template['content'], '<style>') !== false) {
        echo "⚠️ Contains <style> tags - this causes email display issues\n";
    }
    echo "\n";
}

echo "🔧 2. Creating Email-Friendly Templates:\n";
echo "========================================\n";

// Create a clean, email-friendly birthday notification template
$cleanTemplate = [
    'name' => 'Clean Birthday Notification',
    'subject' => '🎂 Birthday Celebration - {birthday_member_name}',
    'content' => '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday Celebration</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f0f8ff;">
    <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f0f8ff;">
        <tr>
            <td align="center" style="padding: 20px;">
                <table width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border-radius: 15px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td style="background: linear-gradient(135deg, #ff7eb3, #6a0dad); color: white; text-align: center; padding: 40px 20px; border-radius: 15px 15px 0 0;">
                            <h1 style="margin: 0; font-size: 32px; font-weight: bold;">🎉 Birthday Celebration! 🎉</h1>
                            <p style="margin: 10px 0 0 0; font-size: 18px;">Let\'s Make This Day Special! ✨</p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 30px; text-align: center;">
                            <p style="font-size: 18px; color: #333; margin-bottom: 20px;">
                                Dear {member_name},
                            </p>
                            
                            <p style="font-size: 18px; color: #333; margin-bottom: 30px;">
                                We are excited to celebrate <strong>{birthday_member_name}\'s</strong> birthday {days_until_birthday_text}! 🎂🎉
                            </p>
                            
                            <!-- Birthday Member Photo -->
                            <div style="margin: 30px 0;">
                                {member_image}
                                <h2 style="color: #6a0dad; margin: 15px 0 5px 0; font-size: 24px;">🎂 {birthday_member_name} 🎂</h2>
                                <p style="color: #666; font-size: 16px; margin: 5px 0;">
                                    <strong>🎈 Birthday:</strong> {birthday_date}<br>
                                    <strong>🎂 Age:</strong> {birthday_member_age} Years
                                </p>
                            </div>
                            
                            <!-- Celebration Ideas -->
                            <div style="background-color: #f9f9f9; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: left;">
                                <h3 style="color: #6a0dad; margin-top: 0; text-align: center;">🎁 How You Can Celebrate:</h3>
                                <ul style="list-style: none; padding: 0; font-size: 16px;">
                                    <li style="margin-bottom: 8px;">💌 Send a heartfelt message</li>
                                    <li style="margin-bottom: 8px;">🙏 Say a special prayer</li>
                                    <li style="margin-bottom: 8px;">🎁 Gift something meaningful</li>
                                    <li style="margin-bottom: 8px;">📖 Share a verse of encouragement</li>
                                </ul>
                            </div>
                            
                            <!-- Scripture -->
                            <div style="background-color: #f0e6ff; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <p style="font-style: italic; color: #6a0dad; font-size: 18px; margin: 0; text-align: center;">
                                    "The LORD has done great things for us, and we are filled with joy."<br>
                                    <strong>- Psalm 126:3</strong>
                                </p>
                            </div>
                            
                            <!-- Call to Action -->
                            <div style="margin: 30px 0;">
                                <a href="mailto:{birthday_member_email}?subject=Happy Birthday {birthday_member_name}!" 
                                   style="display: inline-block; background-color: #ff4081; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; font-size: 16px;">
                                    🎉 Send Birthday Wishes
                                </a>
                            </div>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="text-align: center; padding: 20px; color: #777; font-size: 14px;">
                            <p style="margin: 0;">With love from,</p>
                            <p style="margin: 5px 0 0 0; font-weight: bold; color: #6a0dad;">💒 Freedom Assembly Church</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>'
];

// Update or create the clean template
$stmt = $pdo->prepare("SELECT id FROM email_templates WHERE template_name = ?");
$stmt->execute([$cleanTemplate['name']]);
$existingTemplate = $stmt->fetch();

if ($existingTemplate) {
    echo "Updating existing template: " . $cleanTemplate['name'] . "\n";
    $stmt = $pdo->prepare("UPDATE email_templates SET subject = ?, content = ? WHERE id = ?");
    $result = $stmt->execute([$cleanTemplate['subject'], $cleanTemplate['content'], $existingTemplate['id']]);
    $templateId = $existingTemplate['id'];
} else {
    echo "Creating new template: " . $cleanTemplate['name'] . "\n";
    $stmt = $pdo->prepare("INSERT INTO email_templates (template_name, subject, content, is_birthday_template, created_at) VALUES (?, ?, ?, 0, NOW())");
    $result = $stmt->execute([$cleanTemplate['name'], $cleanTemplate['subject'], $cleanTemplate['content']]);
    $templateId = $pdo->lastInsertId();
}

if ($result) {
    echo "✅ Template created/updated successfully (ID: $templateId)\n";
} else {
    echo "❌ Failed to create/update template\n";
}

echo "\n🔧 3. Updating Notification Settings:\n";
echo "=====================================\n";

// Update the automated email settings to use the new clean template
$stmt = $pdo->prepare("UPDATE automated_emails_settings SET template_ids = ? WHERE email_type = 'notification'");
$result = $stmt->execute([$templateId]);

if ($result) {
    echo "✅ Updated notification settings to use clean template\n";
} else {
    echo "❌ Failed to update notification settings\n";
}

echo "\n🧪 4. Testing Clean Email Template:\n";
echo "===================================\n";

try {
    require_once 'send_birthday_reminders.php';
    
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get Sandra's details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        echo "Testing clean template with Sandra's birthday...\n";
        
        // Calculate days until birthday
        $today = new DateTime();
        $birthDate = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        // Send notifications using the clean template
        $result = $birthdayReminder->sendMemberBirthdayNotifications(
            $sandra['id'],
            $templateId,  // Use the clean template specifically
            $daysUntil
        );
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Clean template test: " . $result['success'] . " emails sent successfully!\n";
            echo "The emails should now display properly without CSS issues.\n";
        } else {
            echo "❌ Clean template test failed\n";
            var_dump($result);
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error testing clean template: " . $e->getMessage() . "\n";
}

echo "\n🎯 SOLUTION SUMMARY:\n";
echo "====================\n";
echo "✅ Created email-friendly template without problematic <style> tags\n";
echo "✅ Used inline CSS styles for better email client compatibility\n";
echo "✅ Updated notification settings to use the clean template\n";
echo "✅ Tested the new template successfully\n\n";

echo "📧 The birthday notification emails should now display beautifully!\n";
echo "Try sending notifications again through the admin interface.\n";
echo "The emails will now show properly formatted content instead of raw HTML/CSS.\n";
?>
