<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔧 Testing Full Name Fix\n";
echo "========================\n\n";

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if (!$sandra) {
        echo "❌ <PERSON> not found\n";
        exit;
    }
    
    echo "✅ <PERSON> found: " . $sandra['full_name'] . "\n";
    echo "Expected in emails: <PERSON> (full name)\n\n";
    
    // Calculate days until birthday
    $today = new DateTime();
    $birthDate = new DateTime($sandra['birth_date']);
    $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
    
    if ($thisYearBirthday < $today) {
        $thisYearBirthday->add(new DateInterval('P1Y'));
    }
    
    $daysUntil = $today->diff($thisYearBirthday)->days;
    
    echo "🔄 Sending test notification with full name fix...\n";
    echo "==================================================\n";
    
    // Clear debug log
    $debug_log_file = __DIR__ . '/logs/email_debug.log';
    if (file_exists($debug_log_file)) {
        file_put_contents($debug_log_file, '');
    }
    
    $result = $birthdayReminder->sendMemberBirthdayNotifications(
        $sandra['id'],
        null,  // Let system choose template
        $daysUntil
    );
    
    echo "📋 Results:\n";
    echo "===========\n";
    
    if (isset($result['success']) && $result['success'] > 0) {
        echo "🎉 SUCCESS! " . $result['success'] . " notification emails sent!\n";
        echo "Template used: " . ($result['template'] ?? 'unknown') . "\n";
        echo "Birthday member: " . ($result['birthday_member'] ?? 'unknown') . "\n";
        
        echo "\n✅ The emails should now show:\n";
        echo "  📧 Subject: Birthday Celebration! Sandra (first name for subject is OK)\n";
        echo "  📝 Content: 'Sandra Stern's birthday' (FULL NAME)\n";
        echo "  🎂 Header: '🎂 Sandra Stern 🎂' (FULL NAME)\n";
        echo "  💌 Email link: 'Happy Birthday Sandra Stern!' (FULL NAME)\n";
        
        if (isset($result['failed']) && $result['failed'] > 0) {
            echo "\n⚠️ " . $result['failed'] . " emails failed\n";
        }
        
    } elseif (isset($result['error'])) {
        echo "❌ Error: " . $result['error'] . "\n";
    } else {
        echo "❌ Unexpected result\n";
        var_dump($result);
    }
    
    // Check the debug log for confirmation
    if (file_exists($debug_log_file)) {
        $logContent = file_get_contents($debug_log_file);
        if (!empty($logContent)) {
            echo "\n📋 Checking email content for full name usage:\n";
            echo "==============================================\n";
            
            // Look for Sandra Stern in the log
            if (strpos($logContent, 'Sandra Stern') !== false) {
                echo "✅ CONFIRMED: 'Sandra Stern' found in email content!\n";
            } else {
                echo "❌ WARNING: 'Sandra Stern' not found in email content\n";
            }
            
            // Look for just Sandra (without Stern)
            $sandraCount = substr_count($logContent, 'Sandra');
            $sandraStternCount = substr_count($logContent, 'Sandra Stern');
            
            echo "📊 Analysis:\n";
            echo "  - Total 'Sandra' mentions: $sandraCount\n";
            echo "  - 'Sandra Stern' mentions: $sandraStternCount\n";
            
            if ($sandraStternCount > 0) {
                echo "  ✅ Full name is being used correctly!\n";
            } else {
                echo "  ❌ Only first name is being used\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 SUMMARY:\n";
echo "============\n";
echo "The fix has been applied to show 'Sandra Stern' instead of just 'Sandra'\n";
echo "in the email content. Check your email to confirm the full name is displayed!\n";
?>
