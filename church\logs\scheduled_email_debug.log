[2025-07-19 06:49:09] Sending scheduled email to Test User <<EMAIL>>
[2025-07-19 06:49:09] Added tracking pixel with ID: scheduled_687b23c59c65a9.89882752
[2025-07-19 06:49:22] Sending scheduled email to Test User <<EMAIL>>
[2025-07-19 06:49:22] Added tracking pixel with ID: scheduled_687b23d2698d51.27006096
[2025-07-19 06:50:20] Sending scheduled email to Test User <<EMAIL>>
[2025-07-19 06:50:20] Added tracking pixel with ID: scheduled_687b240c539e80.30530372
[2025-07-19 06:52:19] Sending scheduled email to Test User <<EMAIL>>
[2025-07-19 06:52:19] SCHEDULED: Birthday notification detected
[2025-07-19 06:52:19] SCHEDULED: Processing HTML email with images
[2025-07-19 06:52:19] SCHEDULED: Embedded birthday image: C:\xampp\htdocs\campaign\church\includes/../uploads/members/test_birthday.jpg
[2025-07-19 06:52:19] SCHEDULED: Processed 1 images for embedding
[2025-07-19 06:52:19] Added tracking pixel with ID: scheduled_687b24837c4fd9.23057093
[2025-07-19 06:53:15] Sending scheduled email to Test User <<EMAIL>>
[2025-07-19 06:53:15] SCHEDULED: Birthday notification detected
[2025-07-19 06:53:15] SCHEDULED: Processing HTML email with images
[2025-07-19 06:53:15] SCHEDULED: Embedded birthday image: C:\xampp\htdocs\campaign\church\includes/../uploads/members/test_birthday.jpg
[2025-07-19 06:53:15] SCHEDULED: Processed 1 images for embedding
[2025-07-19 06:53:15] Added tracking pixel with ID: scheduled_687b24bb15ce75.82482513
[2025-07-19 06:53:15] Skipping member_id tracking for testing
[2025-07-19 06:53:17] Email sent <NAME_EMAIL>
[2025-07-19 07:08:59] Sending scheduled email to Test User <<EMAIL>>
[2025-07-19 07:08:59] SCHEDULED: Birthday notification detected
[2025-07-19 07:08:59] SCHEDULED: ONE-TIME FIX - Processing HTML email with single image embedding
[2025-07-19 07:08:59] SCHEDULED: Found 1 images referenced in HTML
[2025-07-19 07:08:59] SCHEDULED: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 07:08:59] SCHEDULED: ONE-TIME FIX - Embedded birthday image: C:/xampp/htdocs/campaign/church/includes/../uploads/members/test_birthday.jpg as CID: birthday_image_466a444faa9d119182ffa2e89845af68
[2025-07-19 07:08:59] SCHEDULED: ONE-TIME FIX - Skipping already processed image: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 07:08:59] SCHEDULED: ONE-TIME FIX - Embedded 1 images
[2025-07-19 07:08:59] SCHEDULED: Embedded images: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 07:08:59] Added tracking pixel with ID: scheduled_687b286bf30e56.25219168
[2025-07-19 07:09:00] Member ID 999 not found, skipping tracking
[2025-07-19 07:09:02] Email sent <NAME_EMAIL>
[2025-07-19 07:13:01] Sending scheduled email to Test User <<EMAIL>>
[2025-07-19 07:13:01] SCHEDULED: Birthday notification detected
[2025-07-19 07:13:01] SCHEDULED: ONE-TIME FIX - Processing HTML email with single image embedding
[2025-07-19 07:13:01] SCHEDULED: Found 1 images referenced in HTML
[2025-07-19 07:13:01] SCHEDULED: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 07:13:01] SCHEDULED: ONE-TIME FIX - Embedded birthday image: C:/xampp/htdocs/campaign/church/includes/../uploads/members/test_birthday.jpg as CID: birthday_image_3de45d6c77924ea730c3c5406765b3b0
[2025-07-19 07:13:01] SCHEDULED: ONE-TIME FIX - Skipping already processed image: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 07:13:01] SCHEDULED: ONE-TIME FIX - Embedded 1 images
[2025-07-19 07:13:01] SCHEDULED: Embedded images: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 07:13:01] Added tracking pixel with ID: scheduled_687b295d399fd8.29228599
[2025-07-19 07:13:01] Member ID 999 not found, skipping tracking
[2025-07-19 07:13:03] Email sent <NAME_EMAIL>
