<?php
/**
 * Test Upcoming Birthday Notification Image Embedding
 * This will test the fix for member images in upcoming birthday notification emails
 */

require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h1>🔍 Test Upcoming Birthday Notification Image Embedding</h1>";

// Test configuration
$testRecipientEmail = '<EMAIL>';
$testRecipientName = 'Test Recipient';

echo "<p><strong>Purpose:</strong> Test that member images appear inline in upcoming birthday notification emails</p>";
echo "<p><strong>Test Recipient:</strong> $testRecipientEmail</p>";

try {
    // Get the notification templates
    $stmt = $pdo->prepare("
        SELECT id, template_name, subject, content 
        FROM email_templates 
        WHERE id IN (37, 46, 47)
        ORDER BY id
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($templates)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No notification templates found</h3>";
        echo "<p>Templates 37, 46, 47 are missing from the database.</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>📧 Found " . count($templates) . " Notification Templates</h2>";
    
    // Create test birthday member data
    $testBirthdayMember = [
        'id' => 999,
        'full_name' => 'Jane Test Birthday',
        'first_name' => 'Jane',
        'email' => '<EMAIL>',
        'image_path' => 'uploads/members/test_birthday.jpg',
        'birth_date' => date('Y-m-d', strtotime('+2 days')), // Birthday in 2 days
        'phone_number' => '555-0123'
    ];
    
    // Create test recipient data
    $testRecipient = [
        'id' => 998,
        'full_name' => $testRecipientName,
        'first_name' => 'Test',
        'email' => $testRecipientEmail,
        'image_path' => null,
        'phone_number' => '555-0124'
    ];
    
    echo "<h2>🎂 Test Birthday Member: " . htmlspecialchars($testBirthdayMember['full_name']) . "</h2>";
    echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testBirthdayMember['image_path']) . "</p>";
    echo "<p><strong>Birthday:</strong> " . date('F j, Y', strtotime($testBirthdayMember['birth_date'])) . " (in 2 days)</p>";
    
    // Test each template
    foreach ($templates as $template) {
        echo "<div style='border: 2px solid #007bff; margin: 30px 0; padding: 20px; border-radius: 10px;'>";
        echo "<h3>📧 Testing Template: " . htmlspecialchars($template['template_name']) . "</h3>";
        echo "<p><strong>Template ID:</strong> " . $template['id'] . "</p>";
        
        // Check what image placeholder this template uses
        $content = $template['content'];
        $usesGenericMemberImage = strpos($content, '{member_image}') !== false;
        $usesBirthdayMemberImageUrl = strpos($content, '{birthday_member_image_url}') !== false;
        
        echo "<h4>🖼️ Image Placeholder Analysis:</h4>";
        if ($usesGenericMemberImage) {
            echo "<span style='color: green; font-weight: bold;'>✅ Uses {member_image} - Will be fixed by our update</span><br>";
        }
        if ($usesBirthdayMemberImageUrl) {
            echo "<span style='color: blue; font-weight: bold;'>ℹ️ Uses {birthday_member_image_url} - Already has img tag</span><br>";
        }
        
        try {
            // Create BirthdayReminder instance
            $reminderSystem = new BirthdayReminder($pdo);
            
            // Use the public method to process template content
            $processedContent = $reminderSystem->processBirthdayMemberTemplate(
                $template['content'],
                $testRecipient,
                $testBirthdayMember,
                2 // 2 days until birthday
            );
            
            echo "<h4>✅ Template Processing Successful</h4>";
            
            // Check if images are properly embedded
            $hasImgTags = preg_match_all('/<img[^>]*>/i', $processedContent, $imgMatches);
            
            if ($hasImgTags) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h5>🎉 SUCCESS: Found " . count($imgMatches[0]) . " image tag(s)</h5>";
                foreach ($imgMatches[0] as $imgTag) {
                    echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px;'>";
                    echo "<code>" . htmlspecialchars($imgTag) . "</code>";
                    echo "</div>";
                }
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h5>❌ ISSUE: No image tags found in processed content</h5>";
                echo "</div>";
            }
            
            // Check for any remaining unprocessed placeholders
            if (preg_match_all('/{([^}]+)}/', $processedContent, $placeholderMatches)) {
                echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<h5>⚠️ Unprocessed placeholders found:</h5>";
                foreach (array_unique($placeholderMatches[0]) as $placeholder) {
                    echo "<code>" . htmlspecialchars($placeholder) . "</code> ";
                }
                echo "</div>";
            }
            
            // Show a preview of the processed content
            echo "<h4>📄 Processed Content Preview:</h4>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
            echo "<pre style='white-space: pre-wrap; word-wrap: break-word;'>" . htmlspecialchars(substr($processedContent, 0, 800)) . (strlen($processedContent) > 800 ? '...' : '') . "</pre>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Error processing template</h4>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        
        echo "</div>";
    }
    
    echo "<h2>🎯 Summary</h2>";
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Expected Results After Fix:</h3>";
    echo "<ul>";
    echo "<li><strong>Templates 37 & 46:</strong> Should now show proper img tags (fixed {member_image} placeholder)</li>";
    echo "<li><strong>Template 47:</strong> Should continue working (already has img tag)</li>";
    echo "<li><strong>All templates:</strong> Should use sendEmail() function with our ONE-TIME FIX</li>";
    echo "<li><strong>Images:</strong> Should appear inline only, NO attachments</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🚀 Next Step: Send Real Test Email</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
echo "<p>If the template processing looks good, we can send a real test email to verify the complete fix works end-to-end.</p>";
echo "<p><a href='send_test_upcoming_birthday_notification.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Send Test Email</a></p>";
echo "</div>";
?>
