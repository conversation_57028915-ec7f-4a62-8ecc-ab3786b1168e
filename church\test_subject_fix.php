<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔧 Testing Subject Line Fix\n";
echo "===========================\n\n";

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if (!$sandra) {
        echo "❌ Sandra not found\n";
        exit;
    }
    
    echo "✅ <PERSON> found: " . $sandra['full_name'] . "\n";
    
    // Calculate days until birthday
    $today = new DateTime();
    $birthDate = new DateTime($sandra['birth_date']);
    $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
    
    if ($thisYearBirthday < $today) {
        $thisYearBirthday->add(new DateInterval('P1Y'));
    }
    
    $daysUntil = $today->diff($thisYearBirthday)->days;
    
    echo "🔄 Sending test notification with subject fix...\n";
    echo "================================================\n";
    
    // Clear debug log
    $debug_log_file = __DIR__ . '/logs/email_debug.log';
    if (file_exists($debug_log_file)) {
        file_put_contents($debug_log_file, '');
    }
    
    $result = $birthdayReminder->sendMemberBirthdayNotifications(
        $sandra['id'],
        null,  // Let system choose template
        $daysUntil
    );
    
    echo "📋 Results:\n";
    echo "===========\n";
    
    if (isset($result['success']) && $result['success'] > 0) {
        echo "🎉 SUCCESS! " . $result['success'] . " notification emails sent!\n";
        
        // Check the debug log for subject line
        if (file_exists($debug_log_file)) {
            $logContent = file_get_contents($debug_log_file);
            
            echo "\n🔍 Checking subject line in debug log:\n";
            echo "======================================\n";
            
            // Look for subject line entries
            $lines = explode("\n", $logContent);
            foreach ($lines as $line) {
                if (strpos($line, 'Subject:') !== false || strpos($line, 'FINAL SUBJECT PROTECTION') !== false) {
                    echo "📧 " . trim($line) . "\n";
                }
            }
            
            // Check if HTML content is in the log (which would be bad)
            if (strpos($logContent, 'body {') !== false) {
                echo "\n❌ WARNING: HTML content found in email log\n";
            } else {
                echo "\n✅ No HTML content found in email log - good!\n";
            }
            
            // Check for clean subject
            if (strpos($logContent, 'Birthday Celebration! Sandra') !== false && 
                strpos($logContent, 'body {') === false) {
                echo "✅ Subject line appears clean!\n";
            }
        }
        
        echo "\n📧 Expected email structure:\n";
        echo "============================\n";
        echo "📧 Subject: Birthday Celebration! Sandra (CLEAN)\n";
        echo "📝 Content: Beautiful HTML with Sandra Stern under image\n";
        echo "🎂 No HTML/CSS in subject line\n";
        
    } elseif (isset($result['error'])) {
        echo "❌ Error: " . $result['error'] . "\n";
    } else {
        echo "❌ Unexpected result\n";
        var_dump($result);
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n🎯 SUMMARY:\n";
echo "============\n";
echo "✅ Added final subject protection before email sending\n";
echo "✅ Subject is cleaned of any HTML/CSS content\n";
echo "✅ Template content remains unchanged (Sandra Stern under image)\n";
echo "✅ Only the subject line issue has been addressed\n\n";
echo "Check your email - the subject should now be clean:\n";
echo "'Birthday Celebration! Sandra' (without any HTML content)\n";
?>
