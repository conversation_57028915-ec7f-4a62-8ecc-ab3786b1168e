<?php
/**
 * Send Test Upcoming Birthday Notification Email
 * This will send a real upcoming birthday notification email to verify the complete fix
 */

require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "<h1>🎉 Send Test Upcoming Birthday Notification Email</h1>";

// Test configuration
$testRecipientEmail = '<EMAIL>';
$testRecipientName = 'Test Recipient';

echo "<p><strong>Purpose:</strong> Send a real upcoming birthday notification email to verify the complete fix works</p>";
echo "<p><strong>Test Recipient:</strong> $testRecipientEmail</p>";

try {
    // Create test birthday member data (member having the birthday)
    $testBirthdayMember = [
        'id' => 999,
        'full_name' => 'Jane Test Birthday',
        'first_name' => 'Jane',
        'email' => '<EMAIL>',
        'image_path' => 'uploads/members/test_birthday.jpg',
        'birth_date' => date('Y-m-d', strtotime('+2 days')), // Birthday in 2 days
        'phone_number' => '555-0123'
    ];
    
    // Create test recipient data (member receiving the notification)
    $testRecipient = [
        'id' => 998,
        'full_name' => $testRecipientName,
        'first_name' => 'Test',
        'email' => $testRecipientEmail,
        'image_path' => null,
        'phone_number' => '555-0124'
    ];
    
    echo "<h2>🎂 Birthday Member: " . htmlspecialchars($testBirthdayMember['full_name']) . "</h2>";
    echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testBirthdayMember['image_path']) . "</p>";
    echo "<p><strong>Birthday:</strong> " . date('F j, Y', strtotime($testBirthdayMember['birth_date'])) . " (in 2 days)</p>";
    
    echo "<h2>📧 Recipient: " . htmlspecialchars($testRecipient['full_name']) . "</h2>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($testRecipient['email']) . "</p>";
    
    // Get notification template (let's use template 37 which uses {member_image})
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 37");
    $stmt->execute();
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$template) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ Template not found</h3>";
        echo "<p>Template ID 37 (Member Upcoming Birthday Notification 1) not found.</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>📧 Using Template: " . htmlspecialchars($template['template_name']) . "</h2>";
    echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
    
    // Create BirthdayReminder instance
    $reminderSystem = new BirthdayReminder($pdo);
    
    // Process the template content
    $processedSubject = $reminderSystem->processBirthdayMemberTemplate(
        $template['subject'],
        $testRecipient,
        $testBirthdayMember,
        2 // 2 days until birthday
    );
    
    $processedContent = $reminderSystem->processBirthdayMemberTemplate(
        $template['content'],
        $testRecipient,
        $testBirthdayMember,
        2 // 2 days until birthday
    );
    
    echo "<h3>📧 Processed Email Details:</h3>";
    echo "<p><strong>Processed Subject:</strong> " . htmlspecialchars($processedSubject) . "</p>";
    
    // Check if images are properly embedded
    $hasImgTags = preg_match_all('/<img[^>]*>/i', $processedContent, $imgMatches);
    
    if ($hasImgTags) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Image Processing Success: Found " . count($imgMatches[0]) . " image tag(s)</h4>";
        foreach ($imgMatches[0] as $imgTag) {
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px;'>";
            echo "<code>" . htmlspecialchars($imgTag) . "</code>";
            echo "</div>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ No image tags found - fix may not be working</h4>";
        echo "</div>";
    }
    
    // Create member data for sendEmail function
    $memberData = [
        // Recipient data
        'full_name' => $testRecipient['full_name'],
        'first_name' => $testRecipient['first_name'],
        'email' => $testRecipient['email'],
        
        // Birthday member data
        'birthday_member_full_name' => $testBirthdayMember['full_name'],
        'birthday_member_name' => $testBirthdayMember['first_name'],
        'birthday_member_email' => $testBirthdayMember['email'],
        'birthday_member_image_url' => 'http://localhost/campaign/church/' . $testBirthdayMember['image_path'],
        'birthday_member_photo_url' => 'http://localhost/campaign/church/' . $testBirthdayMember['image_path'],
        
        // Critical flags for proper processing
        '_is_birthday_notification' => true,
        '_birthday_member_original_image_path' => $testBirthdayMember['image_path'],
        '_original_image_path' => $testBirthdayMember['image_path']
    ];
    
    echo "<h2>📤 Sending Test Email...</h2>";
    
    // Send the email using the fixed sendEmail function
    $result = sendEmail(
        $testRecipient['email'],
        $testRecipient['full_name'],
        $processedSubject,
        $processedContent,
        true, // HTML format
        $memberData
    );
    
    if ($result) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3>🎉 SUCCESS! Email Sent Successfully</h3>";
        echo "<p><strong>To:</strong> $testRecipientEmail</p>";
        echo "<p><strong>Subject:</strong> " . htmlspecialchars($processedSubject) . "</p>";
        echo "<p><strong>Expected Result:</strong> Birthday member image should appear inline only, NO file attachments</p>";
        echo "</div>";
        
        echo "<h3>🔍 Verification Steps:</h3>";
        echo "<ol>";
        echo "<li>Check your email inbox for the upcoming birthday notification</li>";
        echo "<li>Verify that Jane Test Birthday's image appears inline in the email body</li>";
        echo "<li><strong>CRITICAL:</strong> Confirm there are NO file attachments</li>";
        echo "<li>The email should look professional with proper image embedding</li>";
        echo "</ol>";
        
        echo "<h3>📋 Technical Verification:</h3>";
        echo "<p>Check these logs for technical details:</p>";
        echo "<ul>";
        echo "<li><code>/logs/email_debug.log</code> - Look for Content-Disposition headers</li>";
        echo "<li>Should show: <code>Content-Disposition: inline</code> (without filename)</li>";
        echo "<li>Should show: <code>Content-Type: image/jpeg; name=</code> (empty name)</li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h3>❌ FAILED! Email could not be sent</h3>";
        echo "<p>Check the error logs for details.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🎯 Expected Results</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>✅ If the complete fix works correctly:</h4>";
echo "<ul>";
echo "<li>Birthday member image appears inline in the email body</li>";
echo "<li>NO file attachments are shown in your email client</li>";
echo "<li>Email looks professional and clean</li>";
echo "<li>Content-Disposition headers show 'inline' without filename</li>";
echo "<li>The ONE-TIME FIX prevents duplicate image embedding</li>";
echo "<li>The REAL FIX prevents filename in attachment headers</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>⚠️ If attachments still appear:</h4>";
echo "<ul>";
echo "<li>Check debug logs for Content-Disposition headers</li>";
echo "<li>Look for any filename parameters that shouldn't be there</li>";
echo "<li>Verify the ONE-TIME FIX is working correctly</li>";
echo "</ul>";
echo "</div>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<strong>🚀 This is the final test to confirm the upcoming birthday notification image embedding is completely resolved!</strong>";
echo "</p>";
?>
