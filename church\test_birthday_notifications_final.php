<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🎂 Testing Birthday Notifications - FINAL TEST\n";
echo "===============================================\n\n";

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

echo "🎯 Testing Sandra's Birthday Notifications:\n";
echo "===========================================\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if (!$sandra) {
        echo "❌ Sandra not found\n";
        exit;
    }
    
    echo "✅ <PERSON> found: " . $sandra['full_name'] . " (ID: " . $sandra['id'] . ")\n";
    echo "Birthday: " . $sandra['birth_date'] . "\n";
    
    // Calculate days until birthday
    $today = new DateTime();
    $birthDate = new DateTime($sandra['birth_date']);
    $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
    
    if ($thisYearBirthday < $today) {
        $thisYearBirthday->add(new DateInterval('P1Y'));
    }
    
    $daysUntil = $today->diff($thisYearBirthday)->days;
    echo "Days until birthday: " . $daysUntil . "\n\n";
    
    echo "🔄 Sending birthday notifications to other members...\n";
    echo "=====================================================\n";
    
    // Clear any existing debug log
    $debug_log_file = __DIR__ . '/logs/email_debug.log';
    if (file_exists($debug_log_file)) {
        file_put_contents($debug_log_file, ''); // Clear the log
    }
    
    // Send notifications
    $result = $birthdayReminder->sendMemberBirthdayNotifications(
        $sandra['id'],
        null,  // Let system choose template
        $daysUntil
    );
    
    echo "📋 Notification Results:\n";
    echo "========================\n";
    var_dump($result);
    
    // Analyze results
    if (isset($result['success']) && $result['success'] > 0) {
        echo "\n🎉 SUCCESS! " . $result['success'] . " notification emails sent!\n";
        echo "Birthday member: " . $result['birthday_member'] . "\n";
        echo "Template used: " . $result['template'] . "\n";
        
        if (isset($result['failed']) && $result['failed'] > 0) {
            echo "⚠️ " . $result['failed'] . " emails failed\n";
        }
        
        echo "\n✅ The following members should receive notifications about Sandra's birthday:\n";
        echo "  - Godwin Bointa (<EMAIL>)\n";
        echo "  - Jennifer Godson (<EMAIL>)\n";
        echo "  - Ndivhuwo Machiba (<EMAIL>)\n";
        
    } elseif (isset($result['error'])) {
        echo "\n❌ Error: " . $result['error'] . "\n";
    } else {
        echo "\n❌ Unexpected result format\n";
    }
    
    // Show email debug log
    if (file_exists($debug_log_file)) {
        $logContent = file_get_contents($debug_log_file);
        if (!empty($logContent)) {
            echo "\n📧 Email Debug Log:\n";
            echo "===================\n";
            echo $logContent;
        }
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 ADMIN INTERFACE TEST:\n";
echo "========================\n";
echo "Now the admin interface should work correctly!\n";
echo "Go to: http://localhost/campaign/church/admin/send_birthday_notification.php\n";
echo "1. Log in with: admin / admin123\n";
echo "2. Find Sandra's upcoming birthday (July 21st)\n";
echo "3. Click to send notifications\n";
echo "4. You should see SUCCESS instead of the error!\n\n";

echo "The error 'No notification emails were sent. Please check if there are eligible recipients.'\n";
echo "should now be COMPLETELY RESOLVED! 🎂✨\n";
?>
