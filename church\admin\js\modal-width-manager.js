/**
 * Modal Width Manager for Admin Panel
 * Automatically optimizes modal widths based on content and provides utilities for manual control
 */

class ModalWidthManager {
    constructor() {
        this.init();
    }

    init() {
        // Listen for modal show events to auto-optimize
        document.addEventListener('show.bs.modal', (e) => this.handleModalShow(e));
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => this.optimizeExistingModals());
        
        console.log('Modal Width Manager initialized');
    }

    handleModalShow(event) {
        const modal = event.target;
        const modalDialog = modal.querySelector('.modal-dialog');
        
        if (modalDialog && !modalDialog.classList.contains('modal-width-optimized')) {
            this.optimizeModalWidth(modalDialog);
            modalDialog.classList.add('modal-width-optimized');
        }
    }

    optimizeExistingModals() {
        const modals = document.querySelectorAll('.modal .modal-dialog');
        modals.forEach(modalDialog => {
            if (!modalDialog.classList.contains('modal-width-optimized')) {
                this.optimizeModalWidth(modalDialog);
                modalDialog.classList.add('modal-width-optimized');
            }
        });
    }

    optimizeModalWidth(modalDialog) {
        const modal = modalDialog.closest('.modal');
        const modalContent = modalDialog.querySelector('.modal-content');
        const modalBody = modalDialog.querySelector('.modal-body');
        
        if (!modalContent || !modalBody) return;

        // Remove existing size classes except custom ones
        const sizeClasses = ['modal-lg', 'modal-xl', 'modal-sm'];
        const customClasses = ['modal-narrow', 'modal-medium', 'modal-wide', 'modal-full', 
                              'modal-form', 'modal-confirmation', 'modal-preview', 'modal-table', 'modal-editor'];
        
        // Check if modal already has a custom class
        const hasCustomClass = customClasses.some(cls => modalDialog.classList.contains(cls));
        
        if (hasCustomClass) {
            console.log('Modal already has custom width class, skipping auto-optimization');
            return;
        }

        // Auto-detect optimal width based on content
        const optimalClass = this.detectOptimalWidth(modalDialog, modalBody);
        
        if (optimalClass) {
            // Remove default Bootstrap size classes
            sizeClasses.forEach(cls => modalDialog.classList.remove(cls));
            
            // Add optimal class
            modalDialog.classList.add(optimalClass);
            
            console.log(`Applied optimal width class: ${optimalClass} to modal`);
        }
    }

    detectOptimalWidth(modalDialog, modalBody) {
        // Check for specific content types
        
        // Forms - use modal-form
        if (modalBody.querySelector('form')) {
            const formElements = modalBody.querySelectorAll('input, select, textarea');
            if (formElements.length > 6) {
                return 'modal-form'; // Larger form
            } else {
                return 'modal-medium'; // Smaller form
            }
        }
        
        // Tables - use modal-table
        if (modalBody.querySelector('table')) {
            return 'modal-table';
        }
        
        // Text editors or code blocks - use modal-editor
        if (modalBody.querySelector('textarea[rows]') || 
            modalBody.querySelector('.code-editor') ||
            modalBody.querySelector('pre')) {
            return 'modal-editor';
        }
        
        // Confirmation dialogs - use modal-confirmation
        if (modalBody.textContent.length < 200 && 
            (modalBody.textContent.toLowerCase().includes('delete') ||
             modalBody.textContent.toLowerCase().includes('confirm') ||
             modalBody.textContent.toLowerCase().includes('remove'))) {
            return 'modal-confirmation';
        }
        
        // Preview content - use modal-preview
        if (modalBody.querySelector('img') || 
            modalBody.querySelector('.preview') ||
            modalBody.querySelector('.content-preview')) {
            return 'modal-preview';
        }
        
        // Default based on content length
        const contentLength = modalBody.textContent.length;
        if (contentLength < 300) {
            return 'modal-narrow';
        } else if (contentLength < 800) {
            return 'modal-medium';
        } else {
            return 'modal-wide';
        }
    }

    // Manual width control methods
    setModalWidth(modalId, widthClass) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`Modal with ID ${modalId} not found`);
            return;
        }

        const modalDialog = modal.querySelector('.modal-dialog');
        if (!modalDialog) {
            console.error(`Modal dialog not found in modal ${modalId}`);
            return;
        }

        // Remove all width classes
        const allWidthClasses = ['modal-lg', 'modal-xl', 'modal-sm', 'modal-narrow', 
                                'modal-medium', 'modal-wide', 'modal-full', 'modal-form', 
                                'modal-confirmation', 'modal-preview', 'modal-table', 'modal-editor'];
        
        allWidthClasses.forEach(cls => modalDialog.classList.remove(cls));
        
        // Add new width class
        modalDialog.classList.add(widthClass);
        
        console.log(`Set modal ${modalId} width to ${widthClass}`);
    }

    // Utility methods for common modal types
    makeModalNarrow(modalId) {
        this.setModalWidth(modalId, 'modal-narrow');
    }

    makeModalMedium(modalId) {
        this.setModalWidth(modalId, 'modal-medium');
    }

    makeModalWide(modalId) {
        this.setModalWidth(modalId, 'modal-wide');
    }

    makeModalForm(modalId) {
        this.setModalWidth(modalId, 'modal-form');
    }

    makeModalConfirmation(modalId) {
        this.setModalWidth(modalId, 'modal-confirmation');
    }

    // Get current modal width class
    getModalWidth(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return null;

        const modalDialog = modal.querySelector('.modal-dialog');
        if (!modalDialog) return null;

        const widthClasses = ['modal-lg', 'modal-xl', 'modal-sm', 'modal-narrow', 
                             'modal-medium', 'modal-wide', 'modal-full', 'modal-form', 
                             'modal-confirmation', 'modal-preview', 'modal-table', 'modal-editor'];

        for (const cls of widthClasses) {
            if (modalDialog.classList.contains(cls)) {
                return cls;
            }
        }

        return 'default';
    }

    // Batch optimize all modals on page
    optimizeAllModals() {
        const modals = document.querySelectorAll('.modal .modal-dialog');
        let optimized = 0;

        modals.forEach(modalDialog => {
            modalDialog.classList.remove('modal-width-optimized');
            this.optimizeModalWidth(modalDialog);
            optimized++;
        });

        console.log(`Optimized ${optimized} modals`);
        return optimized;
    }

    // Reset all modals to default Bootstrap sizes
    resetAllModals() {
        const modals = document.querySelectorAll('.modal .modal-dialog');
        const customClasses = ['modal-narrow', 'modal-medium', 'modal-wide', 'modal-full', 
                              'modal-form', 'modal-confirmation', 'modal-preview', 'modal-table', 'modal-editor'];
        
        let reset = 0;
        modals.forEach(modalDialog => {
            customClasses.forEach(cls => modalDialog.classList.remove(cls));
            modalDialog.classList.remove('modal-width-optimized');
            reset++;
        });

        console.log(`Reset ${reset} modals to default sizes`);
        return reset;
    }
}

// Initialize the modal width manager
window.modalWidthManager = new ModalWidthManager();

// Expose utility functions globally for easy use
window.setModalWidth = (modalId, widthClass) => window.modalWidthManager.setModalWidth(modalId, widthClass);
window.makeModalNarrow = (modalId) => window.modalWidthManager.makeModalNarrow(modalId);
window.makeModalMedium = (modalId) => window.modalWidthManager.makeModalMedium(modalId);
window.makeModalWide = (modalId) => window.modalWidthManager.makeModalWide(modalId);
window.makeModalForm = (modalId) => window.modalWidthManager.makeModalForm(modalId);
window.makeModalConfirmation = (modalId) => window.modalWidthManager.makeModalConfirmation(modalId);
