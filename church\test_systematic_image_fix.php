<?php
/**
 * Comprehensive Test for Systematic Image Embedding Fix
 * This will test all aspects of the systematic fix implementation
 */

require_once 'config.php';

echo "<h1>🔧 Comprehensive Systematic Image Embedding Fix Test</h1>";

// Test configuration
$testEmail = '<EMAIL>';
$testName = 'Test User';

echo "<p><strong>Purpose:</strong> Verify all systematic fixes are working correctly</p>";
echo "<p><strong>Test Email:</strong> $testEmail</p>";

// Clear previous debug logs for clean testing
$debugLogFile = __DIR__ . '/logs/email_debug.log';
if (file_exists($debugLogFile)) {
    // Keep last 100 lines and add separator
    $lines = file($debugLogFile);
    $keepLines = array_slice($lines, -100);
    $keepLines[] = "\n" . str_repeat("=", 80) . "\n";
    $keepLines[] = "[" . date('Y-m-d H:i:s') . "] SYSTEMATIC FIX TEST: Starting comprehensive test\n";
    $keepLines[] = str_repeat("=", 80) . "\n\n";
    file_put_contents($debugLogFile, implode('', $keepLines));
}

echo "<h2>🧪 Test Scenarios</h2>";

// Test Scenario 1: Birthday Notification with Single Image
echo "<div style='border: 2px solid #007bff; margin: 20px 0; padding: 20px; border-radius: 10px;'>";
echo "<h3>📧 Test 1: Birthday Notification with Single Image</h3>";

$birthdayMemberData = [
    'birthday_member_name' => 'Jane Test Birthday',
    'birthday_member_full_name' => 'Jane Test Birthday',
    'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    '_is_birthday_notification' => true,
    '_birthday_member_original_image_path' => 'uploads/members/test_birthday.jpg',
    'email' => $testEmail,
    'name' => $testName,
    'full_name' => $testName
];

$singleImageBody = '
<html>
<head><title>Birthday Test</title></head>
<body>
    <h1>Happy Birthday Jane!</h1>
    <div style="text-align: center;">
        <img src="http://localhost/campaign/church/uploads/members/test_birthday.jpg" alt="Jane Test Birthday" style="width: 150px; height: 150px; border-radius: 50%;">
    </div>
    <p>Join us in celebrating Jane\'s special day!</p>
</body>
</html>';

try {
    $result1 = sendEmail($testEmail, $testName, "🎉 Test 1: Single Image Birthday", $singleImageBody, true, $birthdayMemberData);
    
    if ($result1) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Test 1 SUCCESS: Single image birthday email sent</h4>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ Test 1 FAILED: Could not send email</h4>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Test 1 ERROR: " . htmlspecialchars($e->getMessage()) . "</h4>";
    echo "</div>";
}

echo "</div>";

// Test Scenario 2: Multiple Images (should only embed referenced ones)
echo "<div style='border: 2px solid #28a745; margin: 20px 0; padding: 20px; border-radius: 10px;'>";
echo "<h3>📧 Test 2: Multiple Images (Only Referenced Should Be Embedded)</h3>";

$multiImageMemberData = [
    'birthday_member_name' => 'John Multi Test',
    'birthday_member_full_name' => 'John Multi Test',
    'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    'member_image_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    '_is_birthday_notification' => true,
    '_birthday_member_original_image_path' => 'uploads/members/test_birthday.jpg',
    'email' => $testEmail,
    'name' => $testName,
    'full_name' => $testName
];

$multiImageBody = '
<html>
<head><title>Multi Image Test</title></head>
<body>
    <h1>Multi Image Test</h1>
    <p>This email has multiple potential images, but only one is referenced in HTML:</p>
    
    <!-- This image IS referenced and SHOULD be embedded -->
    <div style="text-align: center;">
        <img src="http://localhost/campaign/church/uploads/members/test_birthday.jpg" alt="Referenced Image" style="width: 100px; height: 100px;">
    </div>
    
    <!-- This image is NOT referenced in HTML and should NOT be embedded -->
    <!-- birthday_member_photo_url is available but not used in HTML -->
    
    <p>Only the image above should be embedded, not any others from memberData.</p>
</body>
</html>';

try {
    $result2 = sendEmail($testEmail, $testName, "🔍 Test 2: Multiple Images", $multiImageBody, true, $multiImageMemberData);
    
    if ($result2) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Test 2 SUCCESS: Multi-image email sent</h4>";
        echo "<p><strong>Expected:</strong> Only 1 image should be embedded (the one referenced in HTML)</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ Test 2 FAILED: Could not send email</h4>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Test 2 ERROR: " . htmlspecialchars($e->getMessage()) . "</h4>";
    echo "</div>";
}

echo "</div>";

// Test Scenario 3: Duplicate Image References
echo "<div style='border: 2px solid #ffc107; margin: 20px 0; padding: 20px; border-radius: 10px;'>";
echo "<h3>📧 Test 3: Duplicate Image References (Should Generate Only One CID)</h3>";

$duplicateImageBody = '
<html>
<head><title>Duplicate Image Test</title></head>
<body>
    <h1>Duplicate Image Test</h1>
    <p>This email has the same image referenced multiple times:</p>
    
    <!-- Same image referenced 3 times -->
    <div style="text-align: center;">
        <img src="http://localhost/campaign/church/uploads/members/test_birthday.jpg" alt="Image 1" style="width: 80px; height: 80px;">
        <img src="http://localhost/campaign/church/uploads/members/test_birthday.jpg" alt="Image 2" style="width: 80px; height: 80px;">
        <img src="http://localhost/campaign/church/uploads/members/test_birthday.jpg" alt="Image 3" style="width: 80px; height: 80px;">
    </div>
    
    <p>All three images should use the same CID (only one embedding).</p>
</body>
</html>';

try {
    $result3 = sendEmail($testEmail, $testName, "🔄 Test 3: Duplicate Images", $duplicateImageBody, true, $birthdayMemberData);
    
    if ($result3) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Test 3 SUCCESS: Duplicate image email sent</h4>";
        echo "<p><strong>Expected:</strong> Only 1 CID generated, all 3 img tags should use same CID</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ Test 3 FAILED: Could not send email</h4>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ Test 3 ERROR: " . htmlspecialchars($e->getMessage()) . "</h4>";
    echo "</div>";
}

echo "</div>";

// Wait a moment for logs to be written
sleep(1);

echo "<h2>📋 Debug Log Analysis</h2>";

// Analyze the debug logs
if (file_exists($debugLogFile)) {
    $logContent = file_get_contents($debugLogFile);
    $testLogs = explode("SYSTEMATIC FIX TEST: Starting comprehensive test", $logContent);
    $currentTestLogs = end($testLogs);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🔍 Key Systematic Fix Indicators:</h3>";
    
    // Check for systematic fix messages
    $systematicFixCount = substr_count($currentTestLogs, 'SYSTEMATIC FIX:');
    $filenameValidationCount = substr_count($currentTestLogs, "filename='' (length=1)");
    $successfulEmbeddingCount = substr_count($currentTestLogs, 'Successfully embedded');
    $duplicateWarningCount = substr_count($currentTestLogs, 'already embedded');
    $urlReplacementCount = substr_count($currentTestLogs, 'URL replacement');
    
    echo "<ul>";
    echo "<li><strong>Systematic Fix Messages:</strong> $systematicFixCount</li>";
    echo "<li><strong>Filename Validations:</strong> $filenameValidationCount</li>";
    echo "<li><strong>Successful Embeddings:</strong> $successfulEmbeddingCount</li>";
    echo "<li><strong>Duplicate Warnings:</strong> $duplicateWarningCount</li>";
    echo "<li><strong>URL Replacements:</strong> $urlReplacementCount</li>";
    echo "</ul>";
    
    // Check for Content-Disposition headers
    $inlineDispositionCount = substr_count($currentTestLogs, 'Content-Disposition: inline');
    $inlineWithFilenameCount = substr_count($currentTestLogs, 'Content-Disposition: inline; filename=');
    
    echo "<h4>📧 Email Header Analysis:</h4>";
    echo "<ul>";
    echo "<li><strong>Clean 'Content-Disposition: inline':</strong> $inlineDispositionCount</li>";
    echo "<li><strong>With filename (BAD):</strong> $inlineWithFilenameCount</li>";
    echo "</ul>";
    
    if ($inlineWithFilenameCount > 0) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ WARNING: Some Content-Disposition headers still have filenames!</h4>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ SUCCESS: All Content-Disposition headers are clean (no filenames)</h4>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // Show recent log entries
    echo "<h3>📄 Recent Debug Log Entries:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;'>";
    echo "<pre>" . htmlspecialchars(substr($currentTestLogs, -2000)) . "</pre>";
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Debug log file not found</h3>";
    echo "</div>";
}

echo "<h2>🎯 Expected Results Summary</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<h3>✅ If Systematic Fix Works Correctly:</h3>";
echo "<ul>";
echo "<li><strong>Test 1:</strong> Single image embedded with clean headers</li>";
echo "<li><strong>Test 2:</strong> Only 1 image embedded (the referenced one)</li>";
echo "<li><strong>Test 3:</strong> Only 1 CID generated despite 3 references</li>";
echo "<li><strong>All Tests:</strong> Content-Disposition headers show 'inline' without filename</li>";
echo "<li><strong>All Tests:</strong> No file attachments in email client</li>";
echo "<li><strong>Debug Logs:</strong> Show systematic validation and success messages</li>";
echo "</ul>";
echo "</div>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<strong>🚀 This comprehensive test verifies all aspects of the systematic image embedding fix!</strong>";
echo "</p>";
?>
