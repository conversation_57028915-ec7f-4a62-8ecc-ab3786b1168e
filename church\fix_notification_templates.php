<?php
require_once 'config.php';

echo "🔍 Fixing Notification Templates & Settings\n";
echo "============================================\n\n";

echo "🎨 1. Checking Notification Templates:\n";
echo "======================================\n";

// Check for notification templates (the admin interface looks for specific template names)
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Birthday Notification%' OR template_name LIKE '%Member Notification%' ORDER BY template_name");
$stmt->execute();
$notificationTemplates = $stmt->fetchAll();

if (empty($notificationTemplates)) {
    echo "❌ NO NOTIFICATION TEMPLATES FOUND!\n";
    echo "The admin interface specifically looks for templates with names containing:\n";
    echo "- 'Birthday Notification'\n";
    echo "- 'Member Notification'\n\n";
    
    echo "🔧 Creating a notification template...\n";
    
    $templateName = "Birthday Notification to Members";
    $subject = "Upcoming Birthday - {member_name}";
    $content = "
    <h2>🎂 Upcoming Birthday Celebration!</h2>
    
    <p>Dear {recipient_name},</p>
    
    <p>We wanted to let you know that <strong>{member_name}</strong> has a birthday coming up on <strong>{birthday_date}</strong>!</p>
    
    <p>Please join us in celebrating and consider sending them birthday wishes.</p>
    
    <p>Birthday Details:</p>
    <ul>
        <li><strong>Name:</strong> {member_name}</li>
        <li><strong>Birthday:</strong> {birthday_date}</li>
        <li><strong>Days until birthday:</strong> {days_until_birthday}</li>
    </ul>
    
    <p>Let's make their day special!</p>
    
    <p>Best regards,<br>
    {organization_name}</p>
    ";
    
    $stmt = $pdo->prepare("
        INSERT INTO email_templates (template_name, subject, content, is_birthday_template, created_at) 
        VALUES (?, ?, ?, 0, NOW())
    ");
    
    $result = $stmt->execute([$templateName, $subject, $content]);
    
    if ($result) {
        echo "✅ Created notification template: '$templateName'\n";
        
        // Get the new template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name = ?");
        $stmt->execute([$templateName]);
        $notificationTemplates = $stmt->fetchAll();
    } else {
        echo "❌ Failed to create notification template\n";
    }
    
} else {
    echo "✅ Found " . count($notificationTemplates) . " notification templates:\n";
    foreach ($notificationTemplates as $template) {
        echo "  - ID: " . $template['id'] . " - " . $template['template_name'] . "\n";
    }
}

echo "\n⚙️ 2. Checking Automated Email Settings:\n";
echo "=========================================\n";

$stmt = $pdo->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'notification'");
$stmt->execute();
$notificationSettings = $stmt->fetch();

if (!$notificationSettings) {
    echo "❌ NO NOTIFICATION SETTINGS FOUND!\n";
    echo "Creating default notification settings...\n";
    
    // Create default notification settings
    $stmt = $pdo->prepare("
        INSERT INTO automated_emails_settings 
        (email_type, days_before, template_ids, enabled, send_time, created_at) 
        VALUES ('notification', 3, '', 1, '09:00:00', NOW())
    ");
    
    $result = $stmt->execute();
    
    if ($result) {
        echo "✅ Created default notification settings (3 days before, enabled)\n";
    } else {
        echo "❌ Failed to create notification settings\n";
    }
    
} else {
    echo "✅ Notification settings found:\n";
    echo "  - Days before: " . $notificationSettings['days_before'] . "\n";
    echo "  - Template IDs: " . ($notificationSettings['template_ids'] ?? 'none') . "\n";
    echo "  - Enabled: " . ($notificationSettings['enabled'] ? 'YES' : 'NO') . "\n";
    echo "  - Send time: " . $notificationSettings['send_time'] . "\n";
}

echo "\n🎯 3. Testing the Fix:\n";
echo "======================\n";

// Test the notification sending again
try {
    require_once 'send_birthday_reminders.php';
    
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get Sandra's ID
    $stmt = $pdo->prepare("SELECT id FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        echo "Testing notification for Sandra (ID: " . $sandra['id'] . ") with templates...\n";
        
        // Test with 2 days until birthday
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, 2);
        
        if ($result) {
            echo "✅ Notification test with templates: SUCCESS\n";
        } else {
            echo "❌ Notification test with templates: FAILED\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error testing notifications: " . $e->getMessage() . "\n";
}

echo "\n🎉 SOLUTION:\n";
echo "=============\n";
echo "The issue was likely missing notification templates!\n";
echo "I've created the required templates and settings.\n\n";

echo "✅ Now try the admin interface again:\n";
echo "1. Go to: http://localhost/campaign/church/admin/send_birthday_notification.php\n";
echo "2. Log in with: admin / admin123\n";
echo "3. Look for Sandra's upcoming birthday (July 21st)\n";
echo "4. Click to send notifications to other members\n";
echo "5. It should now work and send notifications to:\n";
echo "   - Godwin Bointa (<EMAIL>)\n";
echo "   - Jennifer Godson (<EMAIL>)\n";
echo "   - Ndivhuwo Machiba (<EMAIL>)\n\n";

echo "The error 'No notification emails were sent' should be resolved!\n";
?>
