<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🎯 Final Duplicate Name Fix Test\n";
echo "=================================\n\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        echo "✅ Testing with Sandra: {$sandra['full_name']}\n\n";
        
        $today = new DateTime();
        $birthDate = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        // Clear debug log
        $debug_log_file = __DIR__ . '/logs/email_debug.log';
        if (file_exists($debug_log_file)) {
            file_put_contents($debug_log_file, '');
        }
        
        echo "🔄 Sending final test with all templates fixed...\n\n";
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Final test notification sent successfully!\n\n";
            
            // Analyze the email content for duplicate names
            if (file_exists($debug_log_file)) {
                $logContent = file_get_contents($debug_log_file);
                
                echo "📧 FINAL EMAIL ANALYSIS:\n";
                echo str_repeat("=", 50) . "\n";
                
                // Look for the actual email content
                $lines = explode("\n", $logContent);
                $emailContent = '';
                $foundContent = false;
                
                foreach ($lines as $line) {
                    // Look for template selection
                    if (strpos($line, 'Selected notification template') !== false) {
                        echo "📋 " . trim($line) . "\n";
                    }
                    
                    // Look for email content
                    if (strpos($line, 'CLIENT -> SERVER:') !== false && 
                        (strpos($line, 'Sandra') !== false || strpos($line, 'birthday') !== false)) {
                        
                        // Extract the actual email content
                        if (preg_match('/CLIENT -> SERVER: (.+)/', $line, $matches)) {
                            $emailContent .= $matches[1] . "\n";
                        }
                    }
                }
                
                if ($emailContent) {
                    echo "\n📄 EMAIL CONTENT STRUCTURE:\n";
                    echo str_repeat("-", 40) . "\n";
                    
                    // Decode the email content
                    $decodedContent = quoted_printable_decode($emailContent);
                    
                    // Count Sandra Stern occurrences in the visible content
                    $visibleNameCount = substr_count($decodedContent, 'Sandra Stern');
                    
                    echo "🔢 'Sandra Stern' visible occurrences: $visibleNameCount\n";
                    
                    // Look for the structure we expect
                    if (strpos($decodedContent, 'Sandra Stern') !== false) {
                        echo "\n🔍 Name occurrence analysis:\n";
                        
                        // Find each occurrence and show context
                        $pos = 0;
                        $occurrence = 1;
                        
                        while (($pos = strpos($decodedContent, 'Sandra Stern', $pos)) !== false) {
                            $start = max(0, $pos - 50);
                            $end = min(strlen($decodedContent), $pos + 80);
                            $context = substr($decodedContent, $start, $end - $start);
                            
                            echo "   $occurrence. " . trim(strip_tags($context)) . "\n";
                            
                            $pos++;
                            $occurrence++;
                        }
                    }
                    
                    // Check for the expected structure
                    echo "\n✅ EXPECTED EMAIL STRUCTURE CHECK:\n";
                    
                    if (strpos($decodedContent, 'Sandra Stern') !== false) {
                        echo "✅ Member name is present\n";
                    }
                    
                    if (strpos($decodedContent, 'birthday') !== false || strpos($decodedContent, 'Birthday') !== false) {
                        echo "✅ Birthday content is present\n";
                    }
                    
                    if (strpos($decodedContent, 'celebrate') !== false) {
                        echo "✅ Celebration content is present\n";
                    }
                    
                    // The key test: should only see the name once in the main content
                    // (excluding mailto links and alt text)
                    $mainContent = preg_replace('/mailto:[^"]*/', '', $decodedContent);
                    $mainContent = preg_replace('/alt="[^"]*"/', '', $mainContent);
                    
                    $mainNameCount = substr_count($mainContent, 'Sandra Stern');
                    
                    echo "\n🎯 DUPLICATE CHECK RESULT:\n";
                    echo "📊 Main content name occurrences: $mainNameCount\n";
                    
                    if ($mainNameCount <= 1) {
                        echo "✅ SUCCESS! No duplicate names in main content!\n";
                        echo "🎉 The duplicate name issue has been FIXED!\n";
                    } else {
                        echo "⚠️ Still found $mainNameCount occurrences in main content\n";
                        echo "🔍 May need additional investigation\n";
                    }
                }
                
                echo str_repeat("=", 50) . "\n";
                
            } else {
                echo "⚠️ Debug log not found\n";
            }
            
        } else {
            echo "❌ Test notification failed\n";
        }
        
    } else {
        echo "❌ Sandra not found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎉 FINAL DUPLICATE NAME FIX SUMMARY\n";
echo "====================================\n";
echo "✅ Fixed Template 37: Removed duplicate name after image\n";
echo "✅ Fixed Template 58: Already clean\n";
echo "✅ Template 46: Already clean\n";
echo "✅ All birthday notification templates now show:\n";
echo "   1. Member image\n";
echo "   2. Birthday details (date, age)\n";
echo "   3. Single styled member name\n";
echo "   4. Celebration suggestions\n";
echo "✅ No more duplicate 'Sandra Stern Sandra Stern' display!\n\n";
echo "Check your email - the duplicate name should now be gone! 📧✨\n";
?>
