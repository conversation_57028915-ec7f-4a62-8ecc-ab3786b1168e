<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🎯 Final Verification - Member Image in Birthday Notifications\n";
echo "=============================================================\n\n";

// Get a test member with an image
$stmt = $pdo->query("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$birthdayMember = $stmt->fetch();

// Get another member to receive the notification
$stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? LIMIT 1");
$stmt->execute([$birthdayMember['id']]);
$recipientMember = $stmt->fetch();

echo "🎂 Birthday Member: " . $birthdayMember['full_name'] . "\n";
echo "📧 Recipient Member: " . $recipientMember['full_name'] . "\n";
echo "📷 Birthday Member Image: " . $birthdayMember['image_path'] . "\n\n";

// Get Member Upcoming Birthday Notification template 1
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name = ?");
$stmt->execute(["Member Upcoming Birthday Notification 1"]);
$template = $stmt->fetch();

// Create BirthdayReminder instance
$birthdayReminder = new BirthdayReminder($pdo);

echo "🧪 Testing the complete flow...\n";
echo "===============================\n";

// Send using the exact same method as the real automated system
$result = $birthdayReminder->sendMemberBirthdayNotifications(
    $birthdayMember['id'],  // Birthday member ID
    $template['id'],        // Template ID
    3                       // 3 days until birthday
);

echo "\n📊 Results:\n";
echo "- Success: " . $result['success'] . "\n";
echo "- Failed: " . $result['failed'] . "\n";
echo "- Skipped: " . $result['skipped'] . "\n";

if ($result['success'] > 0) {
    echo "\n✅ SUCCESS! Member Upcoming Birthday Notification sent!\n";
    echo "\n🔍 Check the email you just received:\n";
    echo "1. ✅ Image should be embedded inline (not as attachment)\n";
    echo "2. ✅ Image should show the birthday member's photo\n";
    echo "3. ✅ Image should be properly sized and styled\n";
    echo "4. ✅ No file attachments should appear in Gmail\n";
    
    echo "\n📧 Email should have been sent to: " . $recipientMember['email'] . "\n";
    echo "🎂 Featuring birthday member: " . $birthdayMember['full_name'] . "\n";
    
    echo "\n🎯 This is the FINAL TEST using the exact same code as production!\n";
} else {
    echo "\n❌ FAILED! No emails were sent successfully\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 MEMBER IMAGE EMBEDDING FIX COMPLETE!\n";
echo str_repeat("=", 60) . "\n";
?>
