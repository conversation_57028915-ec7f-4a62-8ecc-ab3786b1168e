<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🎂 Testing REAL Member Upcoming Birthday Notification Email\n";
echo "==========================================================\n\n";

// Get a test member with an image
$stmt = $pdo->query("SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != '' LIMIT 1");
$birthdayMember = $stmt->fetch();

if (!$birthdayMember) {
    echo "❌ No test member found with image\n";
    exit;
}

// Get another member to receive the notification
$stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? LIMIT 1");
$stmt->execute([$birthdayMember['id']]);
$recipientMember = $stmt->fetch();

if (!$recipientMember) {
    echo "❌ No recipient member found\n";
    exit;
}

echo "🎂 Birthday Member: " . $birthdayMember['full_name'] . "\n";
echo "📧 Recipient Member: " . $recipientMember['full_name'] . "\n";
echo "📷 Birthday Member Image: " . $birthdayMember['image_path'] . "\n\n";

// Get Member Upcoming Birthday Notification template 1
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name = ?");
$stmt->execute(["Member Upcoming Birthday Notification 1"]);
$template = $stmt->fetch();

if (!$template) {
    echo "❌ Template 'Member Upcoming Birthday Notification 1' not found\n";
    exit;
}

echo "📧 Using Template: " . $template['template_name'] . "\n";
echo "📝 Template Subject: " . $template['subject'] . "\n\n";

// Create BirthdayReminder instance
$birthdayReminder = new BirthdayReminder($pdo);

echo "🚀 Sending REAL Member Upcoming Birthday Notification...\n";

// Send the actual notification using the real system method
$result = $birthdayReminder->sendMemberBirthdayNotifications(
    $birthdayMember['id'],  // Birthday member ID
    $template['id'],        // Template ID
    3                       // 3 days until birthday
);

echo "\n📊 Results:\n";
echo "- Success: " . $result['success'] . "\n";
echo "- Failed: " . $result['failed'] . "\n";
echo "- Skipped: " . $result['skipped'] . "\n";

if ($result['success'] > 0) {
    echo "\n✅ SUCCESS! Real Member Upcoming Birthday Notification sent!\n";
    echo "📧 Check the email_debug.log for the latest headers\n";
    echo "🔍 Look for Content-Disposition: inline (should be inline, not attachment)\n";
    echo "🔍 Look for Content-Type: image/...; name= (name should be empty)\n";
} else {
    echo "\n❌ FAILED! No emails were sent successfully\n";
}

echo "\n🎯 This test uses the EXACT same code path as the real system!\n";
echo "📝 Check your email to see if the image is embedded inline or shows as attachment\n";
?>
