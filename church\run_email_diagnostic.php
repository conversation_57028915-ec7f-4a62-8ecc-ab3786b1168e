<?php
/**
 * Web Interface for Email Attachment Diagnostic
 */

// Set your test email here
$TEST_EMAIL = '<EMAIL>'; // Using mailinator for testing

?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Attachment Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 900px; margin: 0 auto; }
        .section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        pre { background: #fff; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
        .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px; display: inline-block; margin: 5px; }
        .button:hover { background: #005a87; }
        .critical { background: #ffebee; border-left: 4px solid #f44336; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Email Attachment Diagnostic Tool</h1>
        
        <div class="critical">
            <h3>🚨 CRITICAL ISSUE INVESTIGATION</h3>
            <p><strong>Problem:</strong> Birthday notification emails show celebrant images as both inline content AND file attachments.</p>
            <p><strong>Goal:</strong> Identify which email function is causing the attachment issue.</p>
        </div>

        <div class="section">
            <h2>📧 Test Configuration</h2>
            <p><strong>Test Email:</strong> <?php echo htmlspecialchars($TEST_EMAIL); ?></p>
            <?php if ($TEST_EMAIL === '<EMAIL>'): ?>
                <p class="error">⚠️ Please update the $TEST_EMAIL variable in this file with your actual email address!</p>
            <?php else: ?>
                <p class="success">✅ Test email configured</p>
            <?php endif; ?>
        </div>

        <div class="section">
            <h2>🧪 What This Diagnostic Does</h2>
            <p>This tool will send 3 test emails using different email functions:</p>
            <ol>
                <li><strong>sendEmail()</strong> - Main function in config.php (has my fixes)</li>
                <li><strong>sendScheduledEmail()</strong> - Function used by birthday cron job</li>
                <li><strong>sendEmailWithPHPMailer()</strong> - Basic PHPMailer function</li>
            </ol>
            <p class="info">Each email will contain the same birthday member image to test attachment behavior.</p>
        </div>

        <div class="section">
            <h2>🎯 Expected Results</h2>
            <table border="1" style="width:100%; border-collapse: collapse;">
                <tr style="background: #e0e0e0;">
                    <th style="padding: 8px;">Function</th>
                    <th style="padding: 8px;">Image Display</th>
                    <th style="padding: 8px;">Attachments</th>
                    <th style="padding: 8px;">Notes</th>
                </tr>
                <tr>
                    <td style="padding: 8px;"><strong>sendEmail()</strong></td>
                    <td style="padding: 8px;">✅ Inline only</td>
                    <td style="padding: 8px;">❌ None</td>
                    <td style="padding: 8px;">Has my fixes</td>
                </tr>
                <tr>
                    <td style="padding: 8px;"><strong>sendScheduledEmail()</strong></td>
                    <td style="padding: 8px;">🔗 URL reference</td>
                    <td style="padding: 8px;">❓ Unknown</td>
                    <td style="padding: 8px;">Used by cron job</td>
                </tr>
                <tr>
                    <td style="padding: 8px;"><strong>sendEmailWithPHPMailer()</strong></td>
                    <td style="padding: 8px;">🔗 URL reference</td>
                    <td style="padding: 8px;">❓ Unknown</td>
                    <td style="padding: 8px;">Basic function</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h2>🚀 Run Diagnostic</h2>
            
            <?php if (isset($_GET['run_diagnostic'])): ?>
                <h3>Diagnostic Results:</h3>
                <pre><?php
                    // Update the test email in the diagnostic file
                    $diagnosticFile = __DIR__ . '/debug_email_attachments.php';
                    $diagnosticContent = file_get_contents($diagnosticFile);
                    $diagnosticContent = str_replace('<EMAIL>', $TEST_EMAIL, $diagnosticContent);
                    file_put_contents($diagnosticFile, $diagnosticContent);
                    
                    // Run the diagnostic
                    ob_start();
                    include 'debug_email_attachments.php';
                    $output = ob_get_clean();
                    echo htmlspecialchars($output);
                ?></pre>
            <?php else: ?>
                <a href="?run_diagnostic=1" class="button">🧪 Run Email Diagnostic</a>
                <p class="info">Click the button above to send test emails and analyze attachment behavior.</p>
            <?php endif; ?>
        </div>

        <div class="section">
            <h2>📊 Manual Verification Steps</h2>
            <ol>
                <li><strong>Check Your Email Inbox:</strong> Look for 3 test emails</li>
                <li><strong>For Each Email, Note:</strong>
                    <ul>
                        <li>Does the birthday image appear inline in the email content?</li>
                        <li>Does the email have any file attachments?</li>
                        <li>If yes, what are the attachment names and file types?</li>
                        <li>Are the attachments the same as the inline images?</li>
                    </ul>
                </li>
                <li><strong>Compare Results:</strong> Which function(s) create unwanted attachments?</li>
            </ol>
        </div>

        <div class="section">
            <h2>📋 Debug Logs</h2>
            <p>Check these log files for detailed information:</p>
            <ul>
                <li><code>/logs/email_debug.log</code> - sendEmail() function logs</li>
                <li><code>/logs/scheduled_email_debug.log</code> - sendScheduledEmail() function logs</li>
                <li><code>/logs/email_attachment_diagnostic.log</code> - This diagnostic's logs</li>
            </ul>
            
            <?php if (file_exists(__DIR__ . '/logs/email_attachment_diagnostic.log')): ?>
                <h4>Latest Diagnostic Log:</h4>
                <pre style="max-height: 300px; overflow-y: auto;"><?php 
                    $logContent = file_get_contents(__DIR__ . '/logs/email_attachment_diagnostic.log');
                    echo htmlspecialchars(substr($logContent, -3000)); // Show last 3000 characters
                ?></pre>
            <?php endif; ?>
        </div>

        <div class="section">
            <h2>🔧 Next Steps Based on Results</h2>
            <div style="background: #fff3cd; padding: 10px; border-radius: 5px;">
                <h4>If sendEmail() creates attachments:</h4>
                <p>My fix didn't work completely - need to investigate PHPMailer configuration</p>
            </div>
            <div style="background: #d1ecf1; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <h4>If sendScheduledEmail() creates attachments:</h4>
                <p>This is the root cause! Need to fix the cron job email function</p>
            </div>
            <div style="background: #d4edda; padding: 10px; border-radius: 5px; margin-top: 10px;">
                <h4>If sendEmailWithPHPMailer() creates attachments:</h4>
                <p>Basic PHPMailer has automatic image processing - need to disable it</p>
            </div>
        </div>

        <div class="section">
            <h2>🎯 Goal</h2>
            <p class="success">Identify the exact function causing attachments so we can implement a targeted fix.</p>
            <p>Once we know which function is the culprit, we can fix it properly and solve this issue once and for all!</p>
        </div>
    </div>
</body>
</html>
