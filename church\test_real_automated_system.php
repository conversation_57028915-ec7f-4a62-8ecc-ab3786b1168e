<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🤖 Testing REAL Automated Birthday Notification System\n";
echo "=====================================================\n\n";

// This tests the EXACT same code path as the automated cron job
echo "🎯 This uses the same sendReminders() method as the cron job\n";
echo "📧 This will send 'b_notification' type emails (the real system)\n\n";

// Create BirthdayReminder instance (same as cron job)
$reminder = new BirthdayReminder($pdo);

echo "🚀 Running automated birthday reminder system...\n";
echo "⏰ This is the EXACT same process that runs automatically\n\n";

// Call the same method that the cron job calls
$results = $reminder->sendReminders();

echo "📊 Results from REAL automated system:\n";
echo "- Total Sent: " . ($results['total_sent'] ?? 0) . "\n";
echo "- Total Failed: " . ($results['total_failed'] ?? 0) . "\n";

if (!empty($results['sent'])) {
    echo "\n📧 Emails sent by type:\n";
    $emailTypes = [];
    foreach ($results['sent'] as $sentEmail) {
        $type = $sentEmail['type'] ?? 'unknown';
        $emailTypes[$type] = ($emailTypes[$type] ?? 0) + 1;
    }
    foreach ($emailTypes as $type => $count) {
        echo "- $type: $count emails\n";
    }
}

if (!empty($results['failed'])) {
    echo "\n❌ Failed emails:\n";
    foreach ($results['failed'] as $failedEmail) {
        echo "- " . ($failedEmail['email'] ?? 'unknown') . ": " . ($failedEmail['error'] ?? 'unknown error') . "\n";
    }
}

echo "\n✅ Test completed!\n";
echo "🔍 Check email_debug.log for the latest headers from the REAL system\n";
echo "📝 Look for 'b_notification' type emails with Content-Disposition: inline\n";
echo "🎯 These are the emails that members actually receive!\n";
?>
