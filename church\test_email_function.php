<?php
require_once 'config.php';

echo "🔍 Testing Email Function Directly\n";
echo "===================================\n\n";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "📧 1. Testing Global sendEmail Function:\n";
echo "=======================================\n";

// Test the global sendEmail function directly
$testEmail = '<EMAIL>';
$testName = 'Godwin Bointa';
$testSubject = 'Test Birthday Notification';
$testContent = '
<h2>🎂 Test Birthday Notification</h2>
<p>Dear Godwin,</p>
<p>This is a test notification about <PERSON>\'s upcoming birthday on July 21st!</p>
<p>Best regards,<br>Freedom Assembly Church</p>
';

echo "Sending test email to: $testEmail\n";
echo "Subject: $testSubject\n\n";

try {
    $result = sendEmail($testEmail, $testName, $testSubject, $testContent, true);
    
    if ($result) {
        echo "✅ EMAIL SENT SUCCESSFULLY!\n";
        echo "The email function is working correctly.\n";
    } else {
        echo "❌ EMAIL SENDING FAILED!\n";
        echo "The global sendEmail function returned false.\n";
    }
    
} catch (Exception $e) {
    echo "❌ EXCEPTION: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n📋 2. Checking Email Settings:\n";
echo "==============================\n";

// Check if email settings are properly loaded
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM email_settings WHERE setting_key IN ('smtp_host', 'smtp_username', 'sender_email')");
$stmt->execute();
$settings = $stmt->fetchAll();

foreach ($settings as $setting) {
    echo $setting['setting_key'] . ": " . (empty($setting['setting_value']) ? 'NOT SET' : 'SET') . "\n";
}

echo "\n🧪 3. Testing with Different Recipients:\n";
echo "========================================\n";

// Test with all 3 recipients
$recipients = [
    ['email' => '<EMAIL>', 'name' => 'Godwin Bointa'],
    ['email' => '<EMAIL>', 'name' => 'Jennifer Godson'],
    ['email' => '<EMAIL>', 'name' => 'Ndivhuwo Machiba']
];

$successCount = 0;
$failCount = 0;

foreach ($recipients as $recipient) {
    echo "Testing email to: " . $recipient['name'] . " (" . $recipient['email'] . ")\n";
    
    $personalizedContent = str_replace('Dear Godwin,', 'Dear ' . $recipient['name'] . ',', $testContent);
    
    try {
        $result = sendEmail($recipient['email'], $recipient['name'], $testSubject, $personalizedContent, true);
        
        if ($result) {
            echo "  ✅ SUCCESS\n";
            $successCount++;
        } else {
            echo "  ❌ FAILED\n";
            $failCount++;
        }
        
    } catch (Exception $e) {
        echo "  ❌ EXCEPTION: " . $e->getMessage() . "\n";
        $failCount++;
    }
    
    // Small delay between emails
    sleep(1);
}

echo "\n📊 Results Summary:\n";
echo "===================\n";
echo "Successful emails: $successCount\n";
echo "Failed emails: $failCount\n";

if ($successCount > 0) {
    echo "\n✅ EMAIL SYSTEM IS WORKING!\n";
    echo "The issue might be in the BirthdayReminder class logic.\n";
    
    echo "\n🔄 Testing BirthdayReminder Again:\n";
    echo "==================================\n";
    
    try {
        require_once 'send_birthday_reminders.php';
        $birthdayReminder = new BirthdayReminder($pdo);
        
        // Try again with verbose error reporting
        echo "Attempting sendMemberBirthdayNotifications with error logging...\n";
        
        // Enable error logging
        ini_set('log_errors', 1);
        ini_set('error_log', 'php_errors.log');
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications(30, null, 1);
        
        echo "Result:\n";
        var_dump($result);
        
        // Check if error log was created
        if (file_exists('php_errors.log')) {
            echo "\nError log contents:\n";
            echo file_get_contents('php_errors.log');
        }
        
    } catch (Exception $e) {
        echo "Exception in BirthdayReminder: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "\n❌ EMAIL SYSTEM IS NOT WORKING!\n";
    echo "All test emails failed. Check SMTP configuration.\n";
}

echo "\n🎯 CONCLUSION:\n";
echo "===============\n";
if ($successCount > 0) {
    echo "The email system works, so the issue is in the BirthdayReminder class.\n";
    echo "Check the detailed output above for specific errors.\n";
} else {
    echo "The email system is not working. Fix SMTP configuration first.\n";
}
?>
