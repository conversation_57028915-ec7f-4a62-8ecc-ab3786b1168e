<?php
require_once 'config.php';

echo "🔍 Verifying Correct Duplicate Removal\n";
echo "======================================\n\n";

try {
    // Check all birthday notification templates
    $stmt = $pdo->prepare("
        SELECT id, template_name, content 
        FROM email_templates 
        WHERE (template_name LIKE '%Notification%' OR template_name LIKE '%Birthday%')
        AND content LIKE '%{member_image}%'
        ORDER BY id
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    foreach ($templates as $template) {
        echo "📋 Template {$template['id']}: {$template['template_name']}\n";
        echo str_repeat("-", 60) . "\n";
        
        $content = $template['content'];
        
        // Find the member_image and show what comes after it
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            // Get a larger context to see the full structure
            $start = max(0, $pos - 50);
            $end = min(strlen($content), $pos + 800);
            $context = substr($content, $start, $end - $start);
            
            echo "🖼️ Structure around {member_image}:\n";
            echo htmlspecialchars($context) . "\n\n";
            
            // Check what immediately follows the image
            $afterImage = substr($content, $pos + strlen('{member_image}'));
            $next200chars = substr($afterImage, 0, 200);
            
            echo "📄 What comes immediately after {member_image}:\n";
            echo htmlspecialchars($next200chars) . "\n\n";
            
            // Look for name placeholders in the next 300 characters
            if (preg_match_all('/\{[^}]*name[^}]*\}/', $next200chars, $matches)) {
                echo "🔍 Name placeholders found after image:\n";
                foreach ($matches[0] as $i => $match) {
                    echo "   " . ($i + 1) . ". $match\n";
                }
            } else {
                echo "✅ No name placeholders immediately after image\n";
            }
            
            // Count total name occurrences in the template
            $namePatterns = [
                '{birthday_member_name}',
                '{birthday_member_full_name}',
                '{birthday_member_first_name}',
                '{full_name}',
                '{member_name}'
            ];
            
            echo "\n📊 Total name placeholder counts:\n";
            foreach ($namePatterns as $pattern) {
                $count = substr_count($content, $pattern);
                if ($count > 0) {
                    echo "   $pattern: $count occurrences\n";
                }
            }
        }
        
        echo "\n" . str_repeat("=", 70) . "\n\n";
    }
    
    // Now let's specifically check what the user wants
    echo "🎯 USER REQUIREMENT CHECK:\n";
    echo str_repeat("=", 50) . "\n";
    echo "✅ GOAL: Remove ONLY the name immediately after {member_image}\n";
    echo "✅ KEEP: The styled name with emojis later in the template\n\n";
    
    echo "📧 EXPECTED STRUCTURE:\n";
    echo "1. {member_image}\n";
    echo "2. [Birthday details - date, age]\n";
    echo "3. 🎂 {birthday_member_name} 🎂 (styled name - KEEP THIS)\n";
    echo "4. [Celebration suggestions]\n\n";
    
    echo "❌ WHAT TO AVOID:\n";
    echo "1. {member_image}\n";
    echo "2. {birthday_member_name} (plain name - REMOVE THIS)\n";
    echo "3. 🎂 {birthday_member_name} 🎂 (styled name - KEEP THIS)\n\n";
    
    // Check if we need to make any corrections
    echo "🔧 VERIFICATION:\n";
    
    foreach ($templates as $template) {
        $content = $template['content'];
        $pos = strpos($content, '{member_image}');
        
        if ($pos !== false) {
            $afterImage = substr($content, $pos + strlen('{member_image}'), 300);
            
            // Check if there's still a name immediately after the image
            if (preg_match('/^\s*<[^>]*>\s*\{[^}]*name[^}]*\}/', $afterImage)) {
                echo "⚠️ Template {$template['id']}: Still has name immediately after image\n";
            } else {
                echo "✅ Template {$template['id']}: Clean - no name immediately after image\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
