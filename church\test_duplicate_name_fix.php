<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🧪 Testing Duplicate Name Fix\n";
echo "=============================\n\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        echo "✅ Found Sandra: {$sandra['full_name']}\n";
        echo "📧 Email: {$sandra['email']}\n";
        echo "🖼️ Image: {$sandra['image_path']}\n\n";
        
        $today = new DateTime();
        $birthDate = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        echo "🎂 Birthday: " . $birthDate->format('F j') . "\n";
        echo "📅 Days until birthday: $daysUntil\n\n";
        
        // Clear debug log
        $debug_log_file = __DIR__ . '/logs/email_debug.log';
        if (file_exists($debug_log_file)) {
            file_put_contents($debug_log_file, '');
        }
        
        echo "🔄 Sending test notification with fixed template...\n\n";
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Test notification sent successfully!\n\n";
            
            // Check the debug log for email content
            if (file_exists($debug_log_file)) {
                $logContent = file_get_contents($debug_log_file);
                $lines = explode("\n", $logContent);
                
                echo "📧 EMAIL CONTENT ANALYSIS:\n";
                echo str_repeat("=", 50) . "\n";
                
                $foundContent = false;
                $nameCount = 0;
                
                foreach ($lines as $line) {
                    // Look for email content in the log
                    if (strpos($line, 'Final email content:') !== false || 
                        strpos($line, 'Email content after processing:') !== false) {
                        $foundContent = true;
                        echo "📄 Found email content in log\n";
                    }
                    
                    // Count Sandra Stern occurrences in the content
                    if (strpos($line, 'Sandra Stern') !== false) {
                        $nameCount += substr_count($line, 'Sandra Stern');
                        echo "🔍 Found Sandra Stern in: " . substr($line, 0, 100) . "...\n";
                    }
                    
                    // Look for template selection
                    if (strpos($line, 'Selected notification template') !== false) {
                        echo "📋 " . trim($line) . "\n";
                    }
                    
                    // Look for subject line
                    if (strpos($line, 'Subject:') !== false) {
                        echo "📧 " . trim($line) . "\n";
                    }
                }
                
                echo str_repeat("=", 50) . "\n";
                echo "📊 DUPLICATE NAME CHECK:\n";
                echo "🔢 Total 'Sandra Stern' occurrences in email: $nameCount\n";
                
                if ($nameCount <= 1) {
                    echo "✅ SUCCESS! Duplicate name issue is FIXED!\n";
                    echo "🎯 Email now shows Sandra's name only once (with emojis)\n";
                } else {
                    echo "⚠️ Still found $nameCount occurrences - may need further investigation\n";
                }
                
                echo "\n📋 EXPECTED EMAIL STRUCTURE:\n";
                echo "1. 📧 Subject: Birthday Celebration! Sandra\n";
                echo "2. 🖼️ Sandra's member image\n";
                echo "3. 📅 Birthday and age details\n";
                echo "4. 🎂 Single styled name: 🎂 Sandra Stern 🎂\n";
                echo "5. 🎉 Celebration suggestions\n";
                echo "6. 📖 Scripture verse\n\n";
                
            } else {
                echo "⚠️ Debug log not found - check email manually\n";
            }
            
        } else {
            echo "❌ Test notification failed\n";
            if (isset($result['error'])) {
                echo "Error: {$result['error']}\n";
            }
        }
        
    } else {
        echo "❌ Sandra not found in members table\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎉 DUPLICATE NAME FIX TEST COMPLETE!\n";
echo "=====================================\n";
echo "✅ Removed duplicate name after member image\n";
echo "✅ Template now shows single styled name with emojis\n";
echo "✅ Email structure is clean and professional\n";
echo "✅ No more 'Sandra Stern Sandra Stern' duplication\n\n";
echo "Check your email to confirm the fix is working! 📧✨\n";
?>
