<?php
require_once 'config.php';

echo "🔧 Removing ALL Member Names After Images\n";
echo "=========================================\n\n";

try {
    // Template 58 - Remove the remaining h2 with birthday_member_name
    echo "🎯 Removing final name from Template 58...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 58");
    $stmt->execute();
    $template58 = $stmt->fetch();
    
    if ($template58) {
        $content = $template58['content'];
        
        // Remove the h2 with birthday_member_name that comes right after {member_image}
        $content = preg_replace(
            '/(\{member_image\})\s*<h2[^>]*>🎉\s*\{birthday_member_name\}\s*🎉<\/h2>/',
            '$1',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
        $stmt->execute([$content]);
        
        echo "✅ Template 58 - Removed final name display\n";
        
        // Show result
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 200);
            echo "📄 Now shows: " . trim($preview) . "\n\n";
        }
    }
    
    // Template 37 - Remove the member-name div
    echo "🎯 Removing final name from Template 37...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 37");
    $stmt->execute();
    $template37 = $stmt->fetch();
    
    if ($template37) {
        $content = $template37['content'];
        
        // Remove the div with class="member-name" that comes after {member_image}
        $content = preg_replace(
            '/(\{member_image\})\s*<div class="member-name">🎉\s*\{birthday_member_full_name\}\s*🎉<\/div>/',
            '$1',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 37");
        $stmt->execute([$content]);
        
        echo "✅ Template 37 - Removed final name display\n";
        
        // Show result
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 200);
            echo "📄 Now shows: " . trim($preview) . "\n\n";
        }
    }
    
    echo "🎉 COMPLETE! All duplicate member names removed!\n";
    echo "📧 Email templates now show ONLY the member image.\n\n";
    
    // Final verification test
    echo "🧪 Final verification test...\n";
    
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%' LIMIT 1");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        require_once 'send_birthday_reminders.php';
        $birthdayReminder = new BirthdayReminder($pdo);
        
        // Clear debug log
        $debug_log_file = __DIR__ . '/logs/email_debug.log';
        if (file_exists($debug_log_file)) {
            file_put_contents($debug_log_file, '');
        }
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, 2);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Final verification email sent!\n";
            echo "📧 The email should now show ONLY Sandra's image without any duplicate name text below it.\n";
            echo "🎯 Check your Gmail - you should see a clean member image without redundant names!\n";
        } else {
            echo "❌ Verification test failed\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
