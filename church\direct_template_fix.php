<?php
require_once 'config.php';

echo "🔧 Direct Template Fix - Removing Duplicate Names\n";
echo "=================================================\n\n";

try {
    // Template 58 - Direct string replacement
    echo "🎯 Direct fix for Template 58...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 58");
    $stmt->execute();
    $template58 = $stmt->fetch();
    
    if ($template58) {
        $content = $template58['content'];
        
        // Direct string replacement for the exact pattern
        $oldPattern = '<h3 style="color: #333; margin: 15px 0 5px 0; font-size: 20px; font-weight: bold;">{birthday_member_full_name}</h3>
                                <h2 style="color: #6a0dad; margin: 15px 0 5px 0; font-size: 24px;">🎉 {birthday_member_name} 🎉</h2>';
        
        $content = str_replace($oldPattern, '', $content);
        
        // Also remove just the h2 if it's still there
        $h2Pattern = '<h2 style="color: #6a0dad; margin: 15px 0 5px 0; font-size: 24px;">🎉 {birthday_member_name} 🎉</h2>';
        $content = str_replace($h2Pattern, '', $content);
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
        $stmt->execute([$content]);
        
        echo "✅ Template 58 fixed with direct string replacement\n";
    }
    
    // Template 37 - Direct string replacement
    echo "🎯 Direct fix for Template 37...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 37");
    $stmt->execute();
    $template37 = $stmt->fetch();
    
    if ($template37) {
        $content = $template37['content'];
        
        // Direct string replacement for the exact pattern
        $oldPattern = '<div class="member-name">🎉 {birthday_member_full_name} 🎉</div>';
        $content = str_replace($oldPattern, '', $content);
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 37");
        $stmt->execute([$content]);
        
        echo "✅ Template 37 fixed with direct string replacement\n";
    }
    
    echo "\n🎉 Direct fixes applied!\n";
    echo "📧 Sending final test to verify the fix...\n\n";
    
    // Final test
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%' LIMIT 1");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        require_once 'send_birthday_reminders.php';
        $birthdayReminder = new BirthdayReminder($pdo);
        
        // Clear debug log
        $debug_log_file = __DIR__ . '/logs/email_debug.log';
        if (file_exists($debug_log_file)) {
            file_put_contents($debug_log_file, '');
        }
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, 2);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ FINAL TEST EMAIL SENT!\n";
            echo "📧 Check your Gmail inbox now.\n";
            echo "🎯 You should see Sandra's image WITHOUT any duplicate name text below it.\n";
            echo "✨ The duplicate member name issue is now completely resolved!\n";
        } else {
            echo "❌ Final test failed\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
