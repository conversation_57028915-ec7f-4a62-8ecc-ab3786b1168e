<?php
require_once 'config.php';

echo "🔍 Checking Template Flags and Selection Logic\n";
echo "==============================================\n\n";

// Check the is_birthday_template flag for all templates
$stmt = $pdo->query("SELECT id, template_name, is_birthday_template FROM email_templates ORDER BY id");
$templates = $stmt->fetchAll();

echo "📧 Template Flags:\n";
echo "==================\n";
foreach ($templates as $template) {
    $flag = $template['is_birthday_template'] ? 'YES' : 'NO';
    echo "ID: " . str_pad($template['id'], 3) . " | is_birthday_template: " . str_pad($flag, 3) . " | " . $template['template_name'] . "\n";
}

echo "\n🎯 Templates that would be selected for birthday notifications:\n";
echo "==============================================================\n";
echo "Query: SELECT * FROM email_templates WHERE is_birthday_template = 0 AND template_name LIKE '%Notification%'\n\n";

$stmt = $pdo->prepare("SELECT id, template_name, is_birthday_template FROM email_templates WHERE is_birthday_template = 0 AND template_name LIKE '%Notification%'");
$stmt->execute();
$notificationTemplates = $stmt->fetchAll();

if ($notificationTemplates) {
    foreach ($notificationTemplates as $template) {
        echo "✅ ID: " . $template['id'] . " - " . $template['template_name'] . "\n";
        
        // Check if this template has {member_image}
        $stmt2 = $pdo->prepare("SELECT content FROM email_templates WHERE id = ?");
        $stmt2->execute([$template['id']]);
        $content = $stmt2->fetchColumn();
        $hasPlaceholder = strpos($content, '{member_image}') !== false;
        echo "   Has {member_image}: " . ($hasPlaceholder ? 'YES' : 'NO') . "\n";
    }
} else {
    echo "❌ No templates found! This explains why the system can't find notification templates.\n";
}

echo "\n🔍 Templates with {member_image} and their flags:\n";
echo "================================================\n";

$stmt = $pdo->query("SELECT id, template_name, is_birthday_template, content FROM email_templates");
$allTemplates = $stmt->fetchAll();

foreach ($allTemplates as $template) {
    if (strpos($template['content'], '{member_image}') !== false) {
        $flag = $template['is_birthday_template'] ? 'YES' : 'NO';
        echo "ID: " . $template['id'] . " - " . $template['template_name'] . "\n";
        echo "  is_birthday_template: " . $flag . "\n";
        echo "  Would be selected for notifications: " . ($template['is_birthday_template'] == 0 && strpos($template['template_name'], 'Notification') !== false ? 'YES' : 'NO') . "\n";
        echo "\n";
    }
}

echo "🎯 Solution:\n";
echo "=============\n";
echo "The templates with {member_image} need to have is_birthday_template = 0 to be selected\n";
echo "for birthday notifications, OR the selection logic needs to be updated.\n";
?>
