<?php
/**
 * Fix Modal Consistency Across Admin Pages
 */

echo "🔧 Fixing Modal Consistency Across Admin Pages\n";
echo "==============================================\n\n";

// Define the admin directory
$adminDir = __DIR__;

// Get all PHP files in admin directory
$phpFiles = glob($adminDir . '/*.php');

$modalUpdates = [];
$totalModalsFound = 0;
$totalModalsUpdated = 0;

foreach ($phpFiles as $file) {
    $filename = basename($file);
    
    // Skip this script and includes
    if ($filename === 'fix_modal_consistency.php' || strpos($filename, 'include') !== false) {
        continue;
    }
    
    $content = file_get_contents($file);
    $originalContent = $content;
    $fileModalsFound = 0;
    $fileModalsUpdated = 0;
    
    // Find all modal-dialog instances
    if (preg_match_all('/<div class="modal-dialog([^"]*)"/', $content, $matches, PREG_OFFSET_CAPTURE)) {
        foreach ($matches[0] as $index => $match) {
            $fullMatch = $match[0];
            $currentClasses = $matches[1][$index][0];
            $fileModalsFound++;
            $totalModalsFound++;
            
            // Determine appropriate modal size based on context
            $contextBefore = substr($content, max(0, $match[1] - 500), 500);
            $contextAfter = substr($content, $match[1], 500);
            $fullContext = $contextBefore . $contextAfter;
            
            $newClass = '';
            
            // Analyze context to determine appropriate modal size
            if (preg_match('/delete|remove|confirm/i', $fullContext)) {
                $newClass = 'modal-confirmation';
            } elseif (preg_match('/notification|send|email|template/i', $fullContext)) {
                $newClass = 'modal-notification';
            } elseif (preg_match('/form|edit|add|create|input/i', $fullContext)) {
                $newClass = 'modal-form';
            } elseif (preg_match('/details|view|preview|content/i', $fullContext)) {
                $newClass = 'modal-wide';
            } else {
                $newClass = 'modal-form'; // Default to form size
            }
            
            // Check if it already has a size class
            if (!preg_match('/modal-(sm|lg|xl|confirmation|form|wide|notification)/', $currentClasses)) {
                $newFullMatch = '<div class="modal-dialog ' . $newClass . trim($currentClasses) . '"';
                $content = str_replace($fullMatch, $newFullMatch, $content);
                $fileModalsUpdated++;
                $totalModalsUpdated++;
                
                echo "📝 {$filename}: Added '{$newClass}' class\n";
            } else {
                echo "ℹ️  {$filename}: Modal already has size class\n";
            }
        }
    }
    
    // Save the file if changes were made
    if ($content !== $originalContent) {
        file_put_contents($file, $content);
        echo "✅ Updated {$filename} ({$fileModalsUpdated} modals)\n";
    } elseif ($fileModalsFound > 0) {
        echo "ℹ️  {$filename} already has proper modal classes ({$fileModalsFound} modals)\n";
    }
    
    if ($fileModalsFound > 0) {
        echo "\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 SUMMARY:\n";
echo "- Total modals found: {$totalModalsFound}\n";
echo "- Total modals updated: {$totalModalsUpdated}\n";
echo "- Files processed: " . count($phpFiles) . "\n\n";

echo "🎯 MODAL SIZE CLASSES APPLIED:\n";
echo "- modal-confirmation (450px): Delete confirmations, simple actions\n";
echo "- modal-form (650px): Forms, data entry, editing\n";
echo "- modal-notification (700px): Email sending, notifications, templates\n";
echo "- modal-wide (800px): Content viewing, details, previews\n\n";

echo "✅ All admin page modals should now have consistent widths!\n";
echo "🔄 Refresh your admin pages to see the consistent modal sizing.\n";
?>
