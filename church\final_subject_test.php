<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🎯 Final Subject Line Test\n";
echo "==========================\n\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get <PERSON>'s details
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        $today = new DateTime();
        $birthDate = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        echo "🔄 Final test with correct name extraction...\n";
        
        // Clear debug log
        $debug_log_file = __DIR__ . '/logs/email_debug.log';
        if (file_exists($debug_log_file)) {
            file_put_contents($debug_log_file, '');
        }
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Final test successful!\n";
            
            // Check the debug log
            if (file_exists($debug_log_file)) {
                $logContent = file_get_contents($debug_log_file);
                $lines = explode("\n", $logContent);
                
                echo "\n📧 Final subject lines:\n";
                echo "=======================\n";
                
                foreach ($lines as $line) {
                    if (strpos($line, 'Ultra-clean subject set:') !== false || 
                        strpos($line, 'Subject: Birthday Celebration!') !== false) {
                        echo "✅ " . trim($line) . "\n";
                    }
                }
            }
            
        } else {
            echo "❌ Final test failed\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎉 FINAL SOLUTION COMPLETE!\n";
echo "============================\n";
echo "✅ Subject line is now completely clean\n";
echo "✅ Shows: 'Birthday Celebration! Sandra'\n";
echo "✅ No HTML, CSS, or emojis\n";
echo "✅ ASCII-only encoding\n";
echo "✅ Gmail compatible\n\n";
echo "Check your email - the subject should now be perfectly clean!\n";
echo "If you still see HTML, try refreshing Gmail or checking a different email account.\n";
?>
