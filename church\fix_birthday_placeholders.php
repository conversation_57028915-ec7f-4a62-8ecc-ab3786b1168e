<?php
require_once 'config.php';

echo "🔧 Fixing Birthday Notification Placeholders\n";
echo "=============================================\n\n";

// Get all birthday notification templates
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Birthday Notification%' OR template_name LIKE '%Member Upcoming%'");
$stmt->execute();
$templates = $stmt->fetchAll();

foreach ($templates as $template) {
    echo "📧 Fixing template: " . $template['template_name'] . " (ID: " . $template['id'] . ")\n";
    
    $content = $template['content'];
    $updated = false;
    
    // Fix the placeholder issues
    $fixes = [
        // Fix the days plural issue
        '{days_until_birthday_plural}' => 's',
        'in 2 day{days_until_birthday_plural}!' => 'in {days_until_birthday} day{days_until_birthday_plural}!',
        'in 1 day{days_until_birthday_plural}!' => 'in {days_until_birthday} day{days_until_birthday_plural}!',
        
        // Fix birthday date formatting
        '{birthday_date_formatted}' => '{birthday_date}',
        
        // Ensure we have the recipient name
        'Dear {recipient_name},' => 'Dear {first_name},',
        
        // Fix any missing member name references
        "Sandra's birthday" => "{birthday_member_full_name}'s birthday",
        'Sandra Stern' => '{birthday_member_full_name}',
        '🎂 Sandra 🎂' => '🎂 {birthday_member_name} 🎂',
        
        // Fix age calculation
        '🎂 Age: 43 Years' => '🎂 Age: {birthday_member_age} Years',
        
        // Fix email link
        'mailto:<EMAIL>' => 'mailto:{birthday_member_email}',
        'Happy Birthday Sandra!' => 'Happy Birthday {birthday_member_name}!',
    ];
    
    foreach ($fixes as $search => $replace) {
        if (strpos($content, $search) !== false) {
            $content = str_replace($search, $replace, $content);
            $updated = true;
            echo "  ✅ Fixed: '$search' → '$replace'\n";
        }
    }
    
    // Additional smart fixes
    // Fix the days plural logic properly
    if (strpos($content, 'day{days_until_birthday_plural}') !== false) {
        // Replace with proper conditional logic
        $content = str_replace(
            '{days_until_birthday} day{days_until_birthday_plural}',
            '{days_until_birthday} {days_text}',
            $content
        );
        $updated = true;
        echo "  ✅ Fixed days plural logic\n";
    }
    
    if ($updated) {
        // Update the template in database
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
        $result = $stmt->execute([$content, $template['id']]);
        
        if ($result) {
            echo "  ✅ Template updated successfully!\n";
        } else {
            echo "  ❌ Failed to update template\n";
        }
    } else {
        echo "  ℹ️ No fixes needed\n";
    }
    
    echo "\n";
}

echo "🧪 Testing the fixed placeholders:\n";
echo "==================================\n";

// Test the notification system with fixed placeholders
try {
    require_once 'send_birthday_reminders.php';
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get Sandra's ID
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        echo "Testing notification for Sandra with fixed placeholders...\n";
        
        // Calculate days until birthday
        $today = new DateTime();
        $birthDate = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        echo "Days until Sandra's birthday: $daysUntil\n";
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Notifications sent successfully with fixed placeholders!\n";
            echo "Sent to " . $result['success'] . " recipients\n";
        } else {
            echo "❌ Notification test failed\n";
            var_dump($result);
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎉 SOLUTION:\n";
echo "=============\n";
echo "The placeholder issues have been fixed!\n";
echo "Now the emails should show:\n";
echo "- Correct number of days (e.g., '2 days' or '1 day')\n";
echo "- Actual birthday date instead of {birthday_date_formatted}\n";
echo "- Proper recipient names\n";
echo "- Correct birthday member information\n\n";
echo "Try sending the birthday notifications again!\n";
?>
