<?php
require_once 'config.php';

echo "🔍 Verifying Template Fix\n";
echo "=========================\n\n";

try {
    // Check Template 58 content after fix
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 58");
    $stmt->execute();
    $template = $stmt->fetch();
    
    if ($template) {
        echo "✅ Template 58: {$template['template_name']}\n\n";
        
        $content = $template['content'];
        
        // Count name placeholders
        $nameCount = substr_count($content, '{birthday_member_name}');
        echo "📊 {birthday_member_name} occurrences: $nameCount\n";
        
        // Show all occurrences with context
        $pos = 0;
        $occurrence = 1;
        
        echo "\n🔍 All {birthday_member_name} occurrences:\n";
        echo str_repeat("-", 60) . "\n";
        
        while (($pos = strpos($content, '{birthday_member_name}', $pos)) !== false) {
            $start = max(0, $pos - 80);
            $end = min(strlen($content), $pos + 100);
            $context = substr($content, $start, $end - $start);
            
            echo "Occurrence $occurrence (position $pos):\n";
            echo htmlspecialchars($context) . "\n";
            echo str_repeat("-", 40) . "\n";
            
            $pos++;
            $occurrence++;
        }
        
        // Check if the h2 duplicate was actually removed
        if (strpos($content, '<h2') !== false && strpos($content, '{birthday_member_name}') !== false) {
            echo "\n🔍 Checking for remaining h2 tags with names:\n";
            
            if (preg_match_all('/<h2[^>]*>.*?\{birthday_member_name\}.*?<\/h2>/s', $content, $matches)) {
                echo "⚠️ Found " . count($matches[0]) . " h2 tags with birthday_member_name:\n";
                foreach ($matches[0] as $i => $match) {
                    echo ($i + 1) . ". " . htmlspecialchars($match) . "\n";
                }
            } else {
                echo "✅ No h2 tags with birthday_member_name found\n";
            }
        }
        
        // Check the area around member_image
        echo "\n🖼️ Content around {member_image}:\n";
        echo str_repeat("-", 60) . "\n";
        
        $imagePos = strpos($content, '{member_image}');
        if ($imagePos !== false) {
            $start = max(0, $imagePos - 100);
            $end = min(strlen($content), $imagePos + 400);
            $context = substr($content, $start, $end - $start);
            echo htmlspecialchars($context) . "\n";
        }
        echo str_repeat("-", 60) . "\n";
        
    } else {
        echo "❌ Template 58 not found\n";
    }
    
    // Also check what template is actually being selected for notifications
    echo "\n🎯 Checking which template is being used for notifications:\n";
    echo str_repeat("=", 60) . "\n";
    
    $stmt = $pdo->prepare("
        SELECT id, template_name, content 
        FROM email_templates 
        WHERE is_birthday_template = 0
        AND template_name LIKE '%Notification%'
        AND content LIKE '%{member_image}%'
        ORDER BY RAND() LIMIT 1
    ");
    $stmt->execute();
    $notificationTemplate = $stmt->fetch();
    
    if ($notificationTemplate) {
        echo "📋 Selected notification template: ID {$notificationTemplate['id']} - {$notificationTemplate['template_name']}\n";
        
        if ($notificationTemplate['id'] == 58) {
            echo "✅ This is the template we just fixed!\n";
        } else {
            echo "⚠️ This is a DIFFERENT template - we may need to fix this one too!\n";
            
            // Check this template for duplicates
            $nameCount = substr_count($notificationTemplate['content'], '{birthday_member_name}');
            echo "📊 {birthday_member_name} occurrences in this template: $nameCount\n";
            
            if ($nameCount > 1) {
                echo "🚨 This template also has duplicate names!\n";
                
                // Show context around member_image
                $imagePos = strpos($notificationTemplate['content'], '{member_image}');
                if ($imagePos !== false) {
                    $start = max(0, $imagePos - 100);
                    $end = min(strlen($notificationTemplate['content']), $imagePos + 300);
                    $context = substr($notificationTemplate['content'], $start, $end - $start);
                    echo "🔍 Context around {member_image}:\n";
                    echo htmlspecialchars($context) . "\n";
                }
            }
        }
    }
    
    // Check all notification templates for duplicates
    echo "\n🔍 Checking ALL notification templates for duplicates:\n";
    echo str_repeat("=", 60) . "\n";
    
    $stmt = $pdo->prepare("
        SELECT id, template_name 
        FROM email_templates 
        WHERE is_birthday_template = 0
        AND template_name LIKE '%Notification%'
        AND content LIKE '%{member_image}%'
    ");
    $stmt->execute();
    $allTemplates = $stmt->fetchAll();
    
    foreach ($allTemplates as $tmpl) {
        $stmt2 = $pdo->prepare("SELECT content FROM email_templates WHERE id = ?");
        $stmt2->execute([$tmpl['id']]);
        $content = $stmt2->fetchColumn();
        
        $nameCount = substr_count($content, '{birthday_member_name}');
        echo "📋 Template {$tmpl['id']} ({$tmpl['template_name']}): $nameCount name occurrences";
        
        if ($nameCount > 1) {
            echo " ⚠️ HAS DUPLICATES!";
        } else {
            echo " ✅ Clean";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
