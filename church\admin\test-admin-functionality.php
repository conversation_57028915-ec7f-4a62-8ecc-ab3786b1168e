<?php
/**
 * Test Admin Functionality After Modal Fix
 */

// Security check
if (!defined('ADMIN_ACCESS')) {
    define('ADMIN_ACCESS', true);
}

// Include the configuration file
require_once '../config.php';

// Set page variables
$page_title = 'Admin Functionality Test';
$page_header = 'Admin Section Functionality Test';
$page_description = 'Verify admin section is working after modal fixes';

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Status Alert -->
            <div class="alert alert-success">
                <h5><i class="bi bi-check-circle-fill me-2"></i>Admin Section Fixed!</h5>
                <p class="mb-0">The modal centering issue has been resolved. The admin section should now be fully functional.</p>
            </div>
            
            <!-- Functionality Test Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="bi bi-gear-fill me-2"></i>Admin Functionality Test</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>✅ Test Basic Functionality:</h6>
                            <div class="list-group">
                                <div class="list-group-item">
                                    <strong>Page Scrolling:</strong> 
                                    <span class="text-success">Should work normally</span>
                                </div>
                                <div class="list-group-item">
                                    <strong>Button Clicks:</strong> 
                                    <button class="btn btn-sm btn-primary">Test Button</button>
                                    <span class="text-success ms-2">Should be clickable</span>
                                </div>
                                <div class="list-group-item">
                                    <strong>Links:</strong> 
                                    <a href="#" class="text-primary">Test Link</a>
                                    <span class="text-success ms-2">Should be clickable</span>
                                </div>
                                <div class="list-group-item">
                                    <strong>Form Inputs:</strong> 
                                    <input type="text" class="form-control form-control-sm d-inline-block" style="width: 150px;" placeholder="Test input">
                                    <span class="text-success ms-2">Should be editable</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>🎯 Test Modal Centering:</h6>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
                                    Test Modal Centering
                                </button>
                                <button type="button" class="btn btn-success" onclick="testPageInteraction()">
                                    Test Page Interaction
                                </button>
                                <button type="button" class="btn btn-info" onclick="testScrolling()">
                                    Test Scrolling
                                </button>
                            </div>
                            
                            <div class="mt-3">
                                <h6>Expected Results:</h6>
                                <ul class="small">
                                    <li>✅ Page should be scrollable</li>
                                    <li>✅ All buttons should be clickable</li>
                                    <li>✅ Modals should open centered</li>
                                    <li>✅ No frozen interface</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Content for Scrolling Test -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Scrolling Test Content</h5>
                </div>
                <div class="card-body">
                    <p>This content is here to test scrolling functionality. The page should scroll normally.</p>
                    <?php for ($i = 1; $i <= 20; $i++): ?>
                        <p>Test paragraph <?php echo $i; ?>: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                    <?php endfor; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Modal -->
<div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModalLabel">Modal Centering Test</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-success">
                    <h6>✅ Modal Fix Successful!</h6>
                    <p class="mb-0">If you can see this modal and it appears centered, the fix is working correctly!</p>
                </div>
                <p>This modal should be:</p>
                <ul>
                    <li>✅ Centered in the viewport</li>
                    <li>✅ Not too wide (optimized width)</li>
                    <li>✅ Properly positioned</li>
                    <li>✅ Not interfering with page functionality</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="alert('Modal interaction works!')">Test Interaction</button>
            </div>
        </div>
    </div>
</div>

<script>
function testPageInteraction() {
    alert('✅ Page interaction is working! The admin section is functional.');
}

function testScrolling() {
    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    setTimeout(() => {
        alert('✅ Scrolling is working! The page scrolled to the bottom.');
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 2000);
}

// Test modal events
document.addEventListener('show.bs.modal', function(e) {
    console.log('✅ Modal opening:', e.target.id);
});

document.addEventListener('shown.bs.modal', function(e) {
    console.log('✅ Modal shown and should be centered');
    
    // Check if modal is centered
    const modal = e.target;
    const modalDialog = modal.querySelector('.modal-dialog');
    const rect = modalDialog.getBoundingClientRect();
    
    const centerX = rect.left + rect.width / 2;
    const viewportCenterX = window.innerWidth / 2;
    const horizontalDiff = Math.abs(centerX - viewportCenterX);
    
    if (horizontalDiff < 100) {
        console.log('✅ Modal is properly centered horizontally');
    } else {
        console.log('⚠️ Modal centering may need adjustment');
    }
});

// Test page functionality on load
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ Admin page loaded successfully');
    console.log('✅ JavaScript is working');
    console.log('✅ Page should be interactive and scrollable');
});
</script>

<?php include 'includes/footer.php'; ?>
