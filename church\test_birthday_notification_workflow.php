<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Testing Birthday Notification Workflow\n";
echo "=========================================\n\n";

$birthdayReminder = new BirthdayReminder($pdo);

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

echo "🎂 Step 1: Finding Upcoming Birthdays\n";
echo "=====================================\n";

// Test the getUpcomingBirthdays method
try {
    $upcomingBirthdays = $birthdayReminder->getUpcomingBirthdays();
    echo "✅ getUpcomingBirthdays() returned: " . count($upcomingBirthdays) . " members\n";
    
    foreach ($upcomingBirthdays as $member) {
        echo "  - " . $member['full_name'] . " (ID: " . $member['id'] . ") - Birthday: " . $member['birth_date'] . "\n";
        echo "    Email: " . ($member['email'] ?? 'none') . "\n";
    }
    
    if (empty($upcomingBirthdays)) {
        echo "❌ No upcoming birthdays found by getUpcomingBirthdays() method!\n";
        echo "This explains the 'no recipient within 7 days' error.\n\n";
        
        echo "🔍 Let me check what this method is actually doing...\n";
        
        // Let's manually check what should be returned
        echo "\n📊 Manual Check - Members with birthdays in next 7 days:\n";
        echo "========================================================\n";
        
        $stmt = $pdo->prepare("SELECT id, full_name, first_name, birth_date, email FROM members WHERE birth_date IS NOT NULL AND email IS NOT NULL AND email != ''");
        $stmt->execute();
        $allMembers = $stmt->fetchAll();
        
        $today = new DateTime();
        $manualUpcoming = [];
        
        foreach ($allMembers as $member) {
            $birthDate = new DateTime($member['birth_date']);
            $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
            
            // If birthday already passed this year, check next year
            if ($thisYearBirthday < $today) {
                $thisYearBirthday->add(new DateInterval('P1Y'));
            }
            
            $daysUntilBirthday = $today->diff($thisYearBirthday)->days;
            
            if ($daysUntilBirthday <= 7) {
                $manualUpcoming[] = [
                    'member' => $member,
                    'days' => $daysUntilBirthday,
                    'birthday_date' => $thisYearBirthday->format('Y-m-d')
                ];
                echo "  ✅ " . $member['full_name'] . " (ID: " . $member['id'] . ") in " . $daysUntilBirthday . " days\n";
            }
        }
        
        if (empty($manualUpcoming)) {
            echo "❌ Manual check also found no upcoming birthdays!\n";
        } else {
            echo "✅ Manual check found " . count($manualUpcoming) . " upcoming birthdays\n";
            echo "🔍 This means the getUpcomingBirthdays() method has a bug!\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error calling getUpcomingBirthdays(): " . $e->getMessage() . "\n";
}

echo "\n🎂 Step 2: Testing Notification for Sandra Stern (ID: 30)\n";
echo "=========================================================\n";

// We know Sandra Stern (ID: 30) has a birthday on July 21st
$sandraId = 30;

// First, verify Sandra exists and has the right birthday
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$sandraId]);
$sandra = $stmt->fetch();

if ($sandra) {
    echo "✅ Found Sandra: " . $sandra['full_name'] . "\n";
    echo "   Birthday: " . $sandra['birth_date'] . "\n";
    echo "   Email: " . ($sandra['email'] ?? 'none') . "\n";
    
    // Calculate days until birthday
    $birthDate = new DateTime($sandra['birth_date']);
    $today = new DateTime();
    $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
    
    if ($thisYearBirthday < $today) {
        $thisYearBirthday->add(new DateInterval('P1Y'));
    }
    
    $daysUntilBirthday = $today->diff($thisYearBirthday)->days;
    echo "   Days until birthday: " . $daysUntilBirthday . "\n\n";
    
    echo "🔄 Testing sendMemberBirthdayNotifications() for Sandra...\n";
    
    try {
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandraId, null, $daysUntilBirthday);
        echo "✅ sendMemberBirthdayNotifications() completed\n";
        echo "Result: " . ($result ? 'Success' : 'Failed') . "\n";
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
    
} else {
    echo "❌ Sandra Stern (ID: 30) not found!\n";
}

echo "\n🔍 Step 3: Checking Main Workflow Methods\n";
echo "=========================================\n";

// Check what methods are available for the main workflow
$reflection = new ReflectionClass($birthdayReminder);
$methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);

echo "Public methods that might be the main entry point:\n";
foreach ($methods as $method) {
    $name = $method->getName();
    if (strpos($name, 'send') === 0 || strpos($name, 'run') === 0 || strpos($name, 'process') === 0) {
        echo "  - " . $name . "()\n";
    }
}

echo "\n🎯 Diagnosis:\n";
echo "=============\n";
echo "The issue is likely that the getUpcomingBirthdays() method is not\n";
echo "correctly finding Sandra's upcoming birthday, which causes the\n";
echo "'no recipient within 7 days' error.\n\n";
echo "We need to debug the getUpcomingBirthdays() method to see why\n";
echo "it's not finding Sandra when our manual check does find her.\n";
?>
