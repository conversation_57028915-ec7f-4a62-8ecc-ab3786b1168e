<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Debugging Email Sending Issues\n";
echo "==================================\n\n";

echo "📧 1. Checking Email Configuration:\n";
echo "===================================\n";

// Check email settings
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM email_settings");
$stmt->execute();
$emailSettings = $stmt->fetchAll();

$hasSmtpConfig = false;
foreach ($emailSettings as $setting) {
    echo $setting['setting_key'] . ": " . ($setting['setting_value'] ? '***configured***' : 'NOT SET') . "\n";
    if ($setting['setting_key'] === 'smtp_host' && !empty($setting['setting_value'])) {
        $hasSmtpConfig = true;
    }
}

if (!$hasSmtpConfig) {
    echo "\n❌ SMTP NOT CONFIGURED!\n";
    echo "This is likely why emails are failing.\n\n";
    
    echo "🔧 Setting up basic email configuration...\n";
    
    // Set up basic email configuration for testing
    $defaultSettings = [
        'smtp_host' => 'smtp.hostinger.com',
        'smtp_auth' => '1',
        'smtp_username' => '<EMAIL>',
        'smtp_password' => '!3wlI!dL',
        'smtp_secure' => 'ssl',
        'smtp_port' => '465',
        'sender_email' => '<EMAIL>',
        'sender_name' => 'Freedom Assembly Church'
    ];
    
    foreach ($defaultSettings as $key => $value) {
        $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->execute([$key, $value, $value]);
    }
    
    echo "✅ Email configuration updated\n";
} else {
    echo "\n✅ SMTP appears to be configured\n";
}

echo "\n🧪 2. Testing Email Sending:\n";
echo "============================\n";

try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get one recipient to test with
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Godwin%'");
    $stmt->execute();
    $testRecipient = $stmt->fetch();
    
    if ($testRecipient) {
        echo "Testing email to: " . $testRecipient['full_name'] . " (" . $testRecipient['email'] . ")\n";
        
        // Get a notification template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Birthday Notification%' LIMIT 1");
        $stmt->execute();
        $template = $stmt->fetch();
        
        if ($template) {
            echo "Using template: " . $template['template_name'] . "\n\n";
            
            // Try to send a test email
            echo "🔄 Attempting to send test notification email...\n";
            
            // Prepare member data for Sandra (birthday person)
            $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
            $stmt->execute();
            $sandra = $stmt->fetch();
            
            if ($sandra) {
                // Test the email sending directly
                $success = $birthdayReminder->sendEmail($testRecipient, $template, 1, 'birthday_notification');
                
                if ($success) {
                    echo "✅ TEST EMAIL SENT SUCCESSFULLY!\n";
                    echo "The email system is working. The issue might be elsewhere.\n";
                } else {
                    echo "❌ TEST EMAIL FAILED!\n";
                    echo "This confirms the email sending is the problem.\n";
                    
                    // Check for more detailed error information
                    echo "\n🔍 Checking for detailed error logs...\n";
                    
                    // Check if there are any failed emails logged
                    $failedEmails = $birthdayReminder->getFailedEmails();
                    if (!empty($failedEmails)) {
                        echo "Failed emails found:\n";
                        foreach ($failedEmails as $failed) {
                            echo "  - " . $failed['email'] . ": " . $failed['error'] . "\n";
                        }
                    } else {
                        echo "No detailed error information available.\n";
                    }
                }
            } else {
                echo "❌ Sandra not found for test\n";
            }
        } else {
            echo "❌ No notification template found\n";
        }
    } else {
        echo "❌ No test recipient found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Exception during email test: " . $e->getMessage() . "\n";
}

echo "\n📋 3. Checking PHP Mail Configuration:\n";
echo "======================================\n";

// Check if mail() function is available
if (function_exists('mail')) {
    echo "✅ PHP mail() function is available\n";
} else {
    echo "❌ PHP mail() function is NOT available\n";
}

// Check if PHPMailer is available
if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    echo "✅ PHPMailer is available\n";
} else {
    echo "❌ PHPMailer is NOT available\n";
}

echo "\n🎯 4. Quick Fix Attempt:\n";
echo "========================\n";

// Try to send notifications again with better error handling
try {
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Enable error reporting for email sending
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    echo "Attempting to send notifications with enhanced error reporting...\n";
    
    $result = $birthdayReminder->sendMemberBirthdayNotifications(30, null, 1);
    
    echo "Result with enhanced error reporting:\n";
    var_dump($result);
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "\n";
}

echo "\n🔧 SOLUTION:\n";
echo "=============\n";
echo "The issue is EMAIL SENDING FAILURE, not recipient detection.\n";
echo "All 3 recipients were found, but all 3 emails failed to send.\n";
echo "This could be due to:\n";
echo "1. SMTP configuration issues\n";
echo "2. Email server connectivity problems\n";
echo "3. Authentication failures\n";
echo "4. Missing email libraries\n\n";
echo "Check the detailed output above to identify the specific email issue.\n";
?>
