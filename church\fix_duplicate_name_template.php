<?php
require_once 'config.php';

echo "🔧 Fixing Duplicate Name in Birthday Template\n";
echo "=============================================\n\n";

try {
    // Get Template ID 58 - Clean Birthday Notification
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 58");
    $stmt->execute();
    $template = $stmt->fetch();
    
    if (!$template) {
        echo "❌ Template ID 58 not found\n";
        exit;
    }
    
    echo "✅ Found template: {$template['template_name']}\n\n";
    
    $content = $template['content'];
    
    // Show current content around the duplicate
    echo "🔍 BEFORE FIX - Content around member image:\n";
    echo str_repeat("-", 60) . "\n";
    
    $pos = strpos($content, '{member_image}');
    if ($pos !== false) {
        $start = max(0, $pos - 50);
        $end = min(strlen($content), $pos + 300);
        $context = substr($content, $start, $end - $start);
        echo htmlspecialchars($context) . "\n";
    }
    echo str_repeat("-", 60) . "\n\n";
    
    // Count current occurrences of birthday_member_name
    $nameCount = substr_count($content, '{birthday_member_name}');
    echo "📊 Current {birthday_member_name} occurrences: $nameCount\n\n";
    
    // Fix: Remove the first name display that appears right after the image
    // Pattern to match: {member_image} followed by the h2 with the name
    $pattern = '/(\{member_image\})\s*<h2[^>]*>🎂\s*\{birthday_member_name\}\s*🎂<\/h2>/';
    
    if (preg_match($pattern, $content)) {
        echo "✅ Found the duplicate name pattern to remove\n";
        
        // Replace with just the member image (remove the h2 name)
        $newContent = preg_replace($pattern, '$1', $content);
        
        // Verify the fix
        $newNameCount = substr_count($newContent, '{birthday_member_name}');
        echo "📊 After fix {birthday_member_name} occurrences: $newNameCount\n";
        
        if ($newNameCount < $nameCount) {
            echo "✅ Successfully reduced duplicate names\n\n";
            
            // Show the fixed content
            echo "🔍 AFTER FIX - Content around member image:\n";
            echo str_repeat("-", 60) . "\n";
            
            $pos = strpos($newContent, '{member_image}');
            if ($pos !== false) {
                $start = max(0, $pos - 50);
                $end = min(strlen($newContent), $pos + 300);
                $context = substr($newContent, $start, $end - $start);
                echo htmlspecialchars($context) . "\n";
            }
            echo str_repeat("-", 60) . "\n\n";
            
            // Update the template in database
            $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
            if ($updateStmt->execute([$newContent])) {
                echo "✅ Template updated successfully in database!\n\n";
                
                echo "🎯 SUMMARY OF CHANGES:\n";
                echo "- Removed duplicate name display after member image\n";
                echo "- Kept the styled name display with emojis\n";
                echo "- Template now shows: Image → Birthday details → Styled name\n";
                echo "- Fixed the duplicate 'Sandra Stern' issue\n\n";
                
                echo "📧 The email will now show:\n";
                echo "1. Member image\n";
                echo "2. Birthday and age details\n";
                echo "3. Single styled name with emojis (🎂 Sandra Stern 🎂)\n";
                echo "4. Celebration suggestions\n\n";
                
                echo "✅ DUPLICATE NAME ISSUE FIXED!\n";
                
            } else {
                echo "❌ Failed to update template in database\n";
            }
            
        } else {
            echo "⚠️ No reduction in name count - pattern might not have matched\n";
        }
        
    } else {
        echo "❌ Could not find the duplicate name pattern\n";
        echo "🔍 Let me try a different approach...\n\n";
        
        // Alternative approach - look for the specific h2 pattern
        $altPattern = '/<h2[^>]*>🎂\s*\{birthday_member_name\}\s*🎂<\/h2>/';
        
        if (preg_match($altPattern, $content, $matches)) {
            echo "✅ Found h2 name pattern: " . htmlspecialchars($matches[0]) . "\n";
            
            // Find all occurrences
            preg_match_all($altPattern, $content, $allMatches, PREG_OFFSET_CAPTURE);
            
            if (count($allMatches[0]) > 1) {
                echo "⚠️ Multiple h2 name patterns found\n";
                foreach ($allMatches[0] as $i => $match) {
                    echo "   " . ($i + 1) . ". Position {$match[1]}: " . htmlspecialchars($match[0]) . "\n";
                }
            }
            
            // Remove the first occurrence (the one right after the image)
            $newContent = preg_replace($altPattern, '', $content, 1);
            
            $newNameCount = substr_count($newContent, '{birthday_member_name}');
            echo "📊 After alternative fix {birthday_member_name} occurrences: $newNameCount\n";
            
            if ($newNameCount < $nameCount) {
                // Update the template
                $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
                if ($updateStmt->execute([$newContent])) {
                    echo "✅ Template updated with alternative fix!\n";
                } else {
                    echo "❌ Failed to update template\n";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
