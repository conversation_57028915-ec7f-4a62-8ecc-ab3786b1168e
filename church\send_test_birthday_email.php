<?php
/**
 * Final Test: Send a Real Birthday Email
 * This will test the complete fix in a real-world scenario
 */

require_once 'config.php';

// Test configuration
$testEmail = '<EMAIL>';
$testName = 'Test User';

echo "<h1>🎉 Final Birthday Email Test</h1>";
echo "<p><strong>Purpose:</strong> Send a real birthday email to verify the complete fix works</p>";
echo "<p><strong>Test Email:</strong> $testEmail</p>";

// Create realistic birthday member data
$birthdayMemberData = [
    'birthday_member_name' => '<PERSON> Smith',
    'birthday_member_full_name' => '<PERSON> Birthday Smith',
    'birthday_member_age' => '25',
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    'member_image_url' => 'http://localhost/campaign/church/uploads/members/test_birthday.jpg',
    '_birthday_member_original_image_path' => 'uploads/members/test_birthday.jpg',
    '_original_image_path' => 'uploads/members/test_birthday.jpg',
    '_is_birthday_notification' => true,
    'email' => $testEmail,
    'recipient_email' => $testEmail,
    'name' => $testName,
    'full_name' => $testName
];

// Create realistic birthday email content
$subject = "🎉 Happy Birthday Jane Birthday Smith!";
$body = '
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .birthday-card { max-width: 600px; margin: 0 auto; padding: 20px; background: #f9f9f9; border-radius: 10px; }
        .birthday-image { text-align: center; margin: 20px 0; }
        .birthday-image img { width: 150px; height: 150px; border-radius: 50%; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
        .birthday-message { text-align: center; font-size: 18px; color: #2c3e50; }
        .celebration { background: linear-gradient(45deg, #ff758c, #ff7eb3); color: white; padding: 15px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="birthday-card">
        <h1 style="text-align: center; color: #e74c3c;">🎉 Happy Birthday! 🎂</h1>
        
        <div class="birthday-image">
            <img src="http://localhost/campaign/church/uploads/members/test_birthday.jpg" alt="Jane Birthday Smith" />
        </div>
        
        <div class="birthday-message">
            <p>Dear Church Family,</p>
            <p>Today we celebrate <strong>Jane Birthday Smith</strong> who is turning <strong>25</strong>!</p>
        </div>
        
        <div class="celebration">
            <h3>🎈 Join us in wishing Jane a wonderful birthday! 🎈</h3>
            <p>May this new year of life bring you joy, blessings, and countless reasons to smile.</p>
        </div>
        
        <p style="text-align: center; color: #7f8c8d; font-size: 14px;">
            This email was sent using the REAL FIX - images should appear inline only, with NO attachments!
        </p>
    </div>
</body>
</html>';

echo "<h2>📧 Sending Test Email...</h2>";

try {
    // Send using the fixed sendEmail function
    $result = sendEmail($testEmail, $testName, $subject, $body, true, $birthdayMemberData);
    
    if ($result) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3>✅ SUCCESS!</h3>";
        echo "<p><strong>Email sent successfully to:</strong> $testEmail</p>";
        echo "<p><strong>Subject:</strong> $subject</p>";
        echo "<p><strong>Expected Result:</strong> Birthday image should appear inline only, NO file attachments</p>";
        echo "</div>";
        
        echo "<h3>🔍 Verification Steps:</h3>";
        echo "<ol>";
        echo "<li>Check your email inbox for the birthday email</li>";
        echo "<li>Verify the birthday image appears inline in the email body</li>";
        echo "<li><strong>CRITICAL:</strong> Confirm there are NO file attachments</li>";
        echo "<li>If you see attachments, the fix needs more work</li>";
        echo "</ol>";
        
        echo "<h3>📋 Debug Information:</h3>";
        echo "<p>Check these logs for technical details:</p>";
        echo "<ul>";
        echo "<li><code>/logs/email_debug.log</code> - Look for Content-Disposition headers</li>";
        echo "<li>Should show: <code>Content-Disposition: inline</code> (without filename)</li>";
        echo "<li>Should show: <code>Content-Type: image/jpeg; name=</code> (empty name)</li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h3>❌ FAILED!</h3>";
        echo "<p>Email could not be sent. Check the error logs for details.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3>❌ ERROR!</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🎯 Expected Results</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>✅ If the fix works correctly:</h4>";
echo "<ul>";
echo "<li>Birthday image appears inline in the email body</li>";
echo "<li>NO file attachments are shown in your email client</li>";
echo "<li>Email looks professional and clean</li>";
echo "<li>Content-Disposition headers show 'inline' without filename</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>⚠️ If attachments still appear:</h4>";
echo "<ul>";
echo "<li>The real fix needs additional work</li>";
echo "<li>Check debug logs for Content-Disposition headers</li>";
echo "<li>Look for any filename parameters that shouldn't be there</li>";
echo "</ul>";
echo "</div>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<strong>🚀 This is the final test to confirm the birthday email attachment issue is completely resolved!</strong>";
echo "</p>";
?>
