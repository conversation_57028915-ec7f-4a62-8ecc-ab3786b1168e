<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🎉 Testing Complete Birthday Email Fix\n";
echo "======================================\n\n";

// Create BirthdayReminder instance
$birthdayReminder = new BirthdayReminder($pdo);

// Find Godwin for testing
$stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE ?");
$stmt->execute(['%Godwin%']);
$godwin = $stmt->fetch();

if (!$godwin) {
    echo "❌ Godwin not found\n";
    exit;
}

echo "👤 Birthday Member: " . $godwin['full_name'] . "\n";
echo "🖼️ Image: " . ($godwin['image_path'] ?? 'none') . "\n";
echo "📧 Email: " . ($godwin['email'] ?? 'none') . "\n\n";

// Get a recipient
$stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? AND email IS NOT NULL LIMIT 1");
$stmt->execute([$godwin['id']]);
$recipient = $stmt->fetch();

echo "📨 Recipient: " . $recipient['full_name'] . " (" . $recipient['email'] . ")\n\n";

// Test the new template selection
echo "🔍 Testing new template selection:\n";
echo "===================================\n";

$reflection = new ReflectionClass($birthdayReminder);
$method = $reflection->getMethod('getBirthdayNotificationTemplate');
$method->setAccessible(true);

$template = $method->invoke($birthdayReminder);

if (!$template) {
    echo "❌ No template selected\n";
    exit;
}

echo "✅ Selected Template: ID " . $template['id'] . " - " . $template['template_name'] . "\n";
$hasPlaceholder = strpos($template['content'], '{member_image}') !== false;
echo "✅ Has {member_image}: " . ($hasPlaceholder ? 'YES' : 'NO') . "\n\n";

// Process the template
echo "🔄 Processing template with member data:\n";
echo "========================================\n";

$subject = $birthdayReminder->processBirthdayMemberTemplate($template['subject'], $recipient, $godwin, 2);
$content = $birthdayReminder->processBirthdayMemberTemplate($template['content'], $recipient, $godwin, 2);

echo "📝 Subject: " . $subject . "\n\n";

// Check for image URLs in the processed content
$imageUrls = [];
if (preg_match_all('/src="([^"]*\.(?:jpg|jpeg|png|gif))/i', $content, $matches)) {
    $imageUrls = $matches[1];
}

echo "🖼️ Image URLs found: " . count($imageUrls) . "\n";
foreach ($imageUrls as $url) {
    echo "  - " . $url . "\n";
}

if (count($imageUrls) > 0) {
    echo "✅ SUCCESS: Template processing includes member image!\n";
} else {
    echo "❌ ISSUE: No image URLs found in processed content\n";
}

echo "\n📧 Sending test email:\n";
echo "======================\n";

// Prepare member data for email
$memberData = [
    'full_name' => $recipient['full_name'],
    'first_name' => $recipient['first_name'],
    'email' => $recipient['email'],
    
    // Birthday member data
    'birthday_member_name' => 'Godwin',
    'birthday_member_full_name' => 'Godwin Bointa',
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/profiles/68778b33a7e2e.jpg',
    'birthday_member_image_url' => 'http://localhost/campaign/church/uploads/profiles/68778b33a7e2e.jpg',
    
    // Critical flags for image embedding
    '_is_birthday_notification' => true,
    '_birthday_member_original_image_path' => $godwin['image_path'],
    '_original_image_path' => $godwin['image_path'],
    
    'days_text' => 'in 2 days',
    'organization_name' => 'Freedom Assembly Church',
];

// Send the email
$result = sendEmail(
    '<EMAIL>',
    'Test Recipient',
    $subject,
    $content,
    true,
    $memberData
);

if ($result) {
    echo "✅ Email sent successfully!\n";
    echo "📧 Check <EMAIL> for the email\n";
    echo "🔍 Check email_debug.log for image embedding details\n";
} else {
    echo "❌ Email failed to send\n";
}

echo "\n🎯 Fix Summary:\n";
echo "===============\n";
echo "✅ URL generation: Fixed malformed localhost. URLs\n";
echo "✅ Template processing: Removed duplicate/corrupted image tag generation\n";
echo "✅ Template selection: Now prioritizes templates with {member_image}\n";
echo "✅ Image embedding: Should now work correctly in emails\n";
echo "\nThe birthday notification system should now consistently include\n";
echo "member images as inline attachments instead of broken external links!\n";
?>
