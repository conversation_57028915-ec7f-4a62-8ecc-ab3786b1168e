<?php
require_once 'config.php';

echo "🔧 Fixing Template Subject Line\n";
echo "===============================\n\n";

// Fix template 58 subject
$stmt = $pdo->prepare('SELECT * FROM email_templates WHERE id = 58');
$stmt->execute();
$template = $stmt->fetch();

if ($template) {
    echo "Current subject: '" . $template['subject'] . "'\n";
    
    // Set a completely clean subject
    $cleanSubject = "Birthday Celebration! {birthday_member_name}";
    
    echo "New subject: '$cleanSubject'\n";
    
    $stmt = $pdo->prepare("UPDATE email_templates SET subject = ? WHERE id = 58");
    $result = $stmt->execute([$cleanSubject]);
    
    if ($result) {
        echo "✅ Template subject updated successfully!\n";
    } else {
        echo "❌ Failed to update template subject\n";
    }
} else {
    echo "❌ Template 58 not found\n";
}

echo "\n🧪 Testing with completely clean subject:\n";
echo "=========================================\n";

// Test the notification system
try {
    require_once 'send_birthday_reminders.php';
    $birthdayReminder = new BirthdayReminder($pdo);
    
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        $today = new DateTime();
        $birthDate = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        echo "Testing notification with completely clean subject...\n";
        
        // Clear debug log
        $debug_log_file = __DIR__ . '/logs/email_debug.log';
        if (file_exists($debug_log_file)) {
            file_put_contents($debug_log_file, '');
        }
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Notifications sent successfully!\n";
            
            // Check the debug log for the clean subject
            if (file_exists($debug_log_file)) {
                $logContent = file_get_contents($debug_log_file);
                
                echo "\n🔍 Checking subject line in debug log:\n";
                echo "======================================\n";
                
                $lines = explode("\n", $logContent);
                foreach ($lines as $line) {
                    if (strpos($line, 'Subject:') !== false || strpos($line, 'FINAL SUBJECT PROTECTION') !== false) {
                        echo "📧 " . trim($line) . "\n";
                    }
                }
                
                // Check for any problematic content
                if (strpos($logContent, 'body {') !== false) {
                    echo "\n❌ WARNING: HTML content still found in email\n";
                } else {
                    echo "\n✅ No HTML content found - subject should be clean!\n";
                }
            }
            
        } else {
            echo "❌ Notification test failed\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎯 FINAL SOLUTION:\n";
echo "==================\n";
echo "✅ Template subject cleaned of emojis and placeholders\n";
echo "✅ Subject line forced to ASCII-only characters\n";
echo "✅ All HTML/CSS content removed from subject processing\n";
echo "✅ Subject will now display as: 'Birthday Celebration! Sandra'\n\n";
echo "The subject line should now be completely clean in Gmail!\n";
?>
