<?php
require_once 'config.php';

echo "🔍 Checking Email Templates\n";
echo "===========================\n\n";

// Get birthday notification templates
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Birthday Notification%' OR template_name LIKE '%Member Upcoming%'");
$stmt->execute();
$templates = $stmt->fetchAll();

foreach ($templates as $template) {
    echo "ID: " . $template['id'] . " - " . $template['template_name'] . "\n";
    echo "Subject: " . $template['subject'] . "\n";
    echo "Content length: " . strlen($template['content']) . " chars\n";
    
    // Check if subject contains HTML/CSS
    if (strpos($template['subject'], '<') !== false || strpos($template['subject'], 'body {') !== false) {
        echo "❌ PROBLEM: Subject contains HTML/CSS!\n";
        echo "Subject content: " . substr($template['subject'], 0, 200) . "...\n";
        
        // Fix the subject
        $cleanSubject = "🎂 Birthday Celebration! {member_name}";
        echo "🔧 Fixing subject to: $cleanSubject\n";
        
        $stmt = $pdo->prepare("UPDATE email_templates SET subject = ? WHERE id = ?");
        $result = $stmt->execute([$cleanSubject, $template['id']]);
        
        if ($result) {
            echo "✅ Subject fixed successfully!\n";
        } else {
            echo "❌ Failed to fix subject\n";
        }
    } else {
        echo "✅ Subject looks clean\n";
    }
    
    echo "---\n";
}

echo "\n🎯 Testing with clean subjects:\n";
echo "===============================\n";

// Test the notification system again
try {
    require_once 'send_birthday_reminders.php';
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get Sandra's ID
    $stmt = $pdo->prepare("SELECT id FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        echo "Testing notification with clean subject for Sandra (ID: " . $sandra['id'] . ")...\n";
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, 1);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Notifications sent successfully with clean subjects!\n";
            echo "Sent to " . $result['success'] . " recipients\n";
        } else {
            echo "❌ Notification test failed\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎉 SOLUTION:\n";
echo "=============\n";
echo "The email subject line issue has been fixed!\n";
echo "Now the emails should show clean subjects like:\n";
echo "'🎂 Birthday Celebration! Sandra'\n";
echo "instead of HTML/CSS code.\n";
?>
