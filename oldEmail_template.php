<?php
session_start();

// Check for flash messages
$flash_message = '';
$flash_type = '';
if (isset($_SESSION['flash_message'])) {
    $flash_message = $_SESSION['flash_message'];
    $flash_type = $_SESSION['flash_type'] ?? 'success';
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
}

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include language system
require_once 'includes/language.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';
$template = null;

// Delete template
if (isset($_GET['delete_id']) && !empty($_GET['delete_id'])) {
    $delete_id = intval($_GET['delete_id']);
    
    // Check if template is in use
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM email_logs WHERE template_id = ?");
    $stmt->execute([$delete_id]);
    $result = $stmt->fetch();
    $is_in_use = $result['count'] > 0;
    
    if ($is_in_use) {
        $error = __('cannot_delete_template_in_use');
    } else {
        $stmt = $conn->prepare("DELETE FROM email_templates WHERE id = ?");
        if ($stmt->execute([$delete_id])) {
            $message = __('template_deleted_successfully');
        } else {
            $error = __('error_deleting_template') . ": " . $conn->errorInfo()[2];
        }
    }
}

// Handle template creation and editing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_template'])) {
        try {
            $template_name = trim($_POST['template_name']);
            $subject = trim($_POST['subject']);
            $content = trim($_POST['content']);
            $category = trim($_POST['template_category']);
            $is_birthday = isset($_POST['is_birthday_template']) ? 1 : 0;

            if (empty($template_name) || empty($subject) || empty($content)) {
                throw new Exception("Template name, subject, and content are required");
            }

            $stmt = $conn->prepare("INSERT INTO email_templates (template_name, subject, content, template_category, is_birthday_template, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$template_name, $subject, $content, $category, $is_birthday]);

            $message = __('template_created_successfully');
        } catch (Exception $e) {
            $error = "Error creating template: " . $e->getMessage();
        }
    }

    if (isset($_POST['update_template'])) {
        try {
            $template_id = intval($_POST['template_id']);
            $template_name = trim($_POST['template_name']);
            $subject = trim($_POST['subject']);
            $content = trim($_POST['content']);
            $category = trim($_POST['template_category']);
            $is_birthday = isset($_POST['is_birthday_template']) ? 1 : 0;

            if (empty($template_name) || empty($subject) || empty($content)) {
                throw new Exception("Template name, subject, and content are required");
            }

            $stmt = $conn->prepare("UPDATE email_templates SET template_name = ?, subject = ?, content = ?, template_category = ?, is_birthday_template = ? WHERE id = ?");
            $stmt->execute([$template_name, $subject, $content, $category, $is_birthday, $template_id]);

            $message = __('template_updated_successfully');
        } catch (Exception $e) {
            $error = "Error updating template: " . $e->getMessage();
        }
    }
}

// Set up sorting and pagination parameters
$sort_column = isset($_GET['sort']) ? $_GET['sort'] : 'id';
$sort_order = isset($_GET['order']) ? $_GET['order'] : 'DESC';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 10; // Items per page

// Validate sort column to prevent SQL injection
$valid_columns = ['id', 'template_name', 'subject', 'is_birthday_template', 'template_category', 'created_at'];
if (!in_array($sort_column, $valid_columns)) {
    $sort_column = 'id';
}

// Validate sort order
if ($sort_order != 'ASC' && $sort_order != 'DESC') {
    $sort_order = 'DESC';
}

// Get total count for pagination
if (!empty($search)) {
    $count_stmt = $conn->prepare("SELECT COUNT(*) FROM email_templates 
                                WHERE template_name LIKE ? OR subject LIKE ?");
    $count_stmt->execute(['%' . $search . '%', '%' . $search . '%']);
} else {
    $count_stmt = $conn->query("SELECT COUNT(*) FROM email_templates");
}
$total_items = $count_stmt->fetchColumn();
$total_pages = ceil($total_items / $per_page);
$page = min($page, max(1, $total_pages)); // Ensure page is within valid range
$offset = ($page - 1) * $per_page;

// Get templates with pagination, sorting, and optional search
if (!empty($search)) {
    $stmt = $conn->prepare("SELECT * FROM email_templates 
                           WHERE template_name LIKE ? OR subject LIKE ? 
                           ORDER BY $sort_column $sort_order
                           LIMIT $offset, $per_page");
    $stmt->execute(['%' . $search . '%', '%' . $search . '%']);
} else {
    $stmt = $conn->prepare("SELECT * FROM email_templates 
                           ORDER BY $sort_column $sort_order
                           LIMIT $offset, $per_page");
    $stmt->execute();
}
$templates = $stmt->fetchAll();

// Close the database connection
$conn = null;

// Set page variables
$page_title = __('email_templates');
$page_header = __('email_templates');
$page_description = __('manage_email_templates_description');

// Include header
include 'includes/header.php';

// Display flash message if it exists
if (!empty($flash_message)) {
    $alert_class = $flash_type === 'success' ? 'alert-success' : 'alert-danger';
    $icon_class = $flash_type === 'success' ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill';
    echo '<div class="alert ' . $alert_class . ' alert-dismissible fade show" role="alert">
            <i class="bi ' . $icon_class . ' me-2"></i>' . htmlspecialchars($flash_message) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}

// Display regular error message if it exists
if (!empty($error)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>' . htmlspecialchars($error) . '
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>

<!-- Instructions panel -->
<div class="alert alert-info mb-4 instruction-panel">
    <div class="d-flex justify-content-between align-items-start">
        <h5><i class="bi bi-info-circle-fill me-2"></i><?php _e('about_email_templates'); ?></h5>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <p class="mb-2"><?php _e('email_templates_help_description'); ?></p>
    <ul class="mb-0">
        <li><strong><?php _e('birthday_templates'); ?>:</strong> <?php _e('birthday_templates_description'); ?></li>
        <li><strong><?php _e('bulk_email_templates'); ?>:</strong> <?php _e('bulk_email_templates_description'); ?></li>
        <li><strong><?php _e('reminder_templates'); ?>:</strong> <?php _e('reminder_templates_description'); ?></li>
    </ul>
    <div class="mt-2">
        <strong><?php _e('note'); ?>:</strong> <?php _e('newsletter_templates_note'); ?>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <!-- Search form -->
        <form action="" method="GET" class="d-flex">
            <input type="text" name="search" class="form-control me-2" placeholder="<?php _e('search_templates_placeholder'); ?>" value="<?php echo htmlspecialchars($search); ?>" data-bs-toggle="tooltip" data-bs-placement="top" title="<?php _e('search_templates_tooltip'); ?>">
            <button type="submit" class="btn btn-outline-primary"><?php _e('search'); ?></button>
            <?php if (!empty($search)): ?>
                <a href="email_templates.php" class="btn btn-outline-secondary ms-2"><?php _e('clear'); ?></a>
            <?php endif; ?>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal" title="<?php _e('create_template_tooltip'); ?>">
            <i class="bi bi-plus-circle"></i> <?php _e('create_new_template'); ?>
        </button>
    </div>
</div>

<!-- Display message about search results if searching -->
<?php if (!empty($search)): ?>
<div class="alert alert-info">
    <p><?php echo sprintf(__('showing_search_results'), count($templates), htmlspecialchars($search)); ?></p>
</div>
<?php endif; ?>

<!-- Templates Table -->
<div class="card">
    <div class="card-body">
        <?php if (count($templates) > 0): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>
                            <a href="?sort=id&order=<?php echo ($sort_column == 'id' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                ID
                                <?php if ($sort_column == 'id'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort=template_name&order=<?php echo ($sort_column == 'template_name' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                <?php _e('template_name'); ?>
                                <?php if ($sort_column == 'template_name'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort=subject&order=<?php echo ($sort_column == 'subject' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                <?php _e('subject'); ?>
                                <?php if ($sort_column == 'subject'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort=template_category&order=<?php echo ($sort_column == 'template_category' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                <?php _e('type'); ?>
                                <?php if ($sort_column == 'template_category'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="?sort=created_at&order=<?php echo ($sort_column == 'created_at' && $sort_order == 'ASC') ? 'DESC' : 'ASC'; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>">
                                <?php _e('created'); ?>
                                <?php if ($sort_column == 'created_at'): ?>
                                <i class="bi bi-sort-<?php echo $sort_order == 'ASC' ? 'up' : 'down'; ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th><?php _e('actions'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $row_number = 1;
                    foreach ($templates as $t): 
                    ?>
                    <tr>
                        <td><?php echo $row_number++; ?></td>
                        <td><?php echo $t['id']; ?></td>
                        <td><?php echo htmlspecialchars($t['template_name']); ?></td>
                        <td><?php echo htmlspecialchars($t['subject']); ?></td>
                        <td>
                            <?php 
                            // Check if it's a birthday template first, regardless of category
                            if ($t['is_birthday_template']) {
                                $badge_class = 'bg-warning';
                                $category_label = __('birthday');
                            } else {
                                // Otherwise use the category from the database
                                $category = $t['template_category'] ?? 'general';
                                switch ($category) {
                                    case 'bulk':
                                        $badge_class = 'bg-primary';
                                        $category_label = __('bulk');
                                        break;
                                    case 'birthday':
                                        $badge_class = 'bg-warning';
                                        $category_label = __('birthday');
                                        break;
                                    default:
                                        $badge_class = 'bg-info';
                                        $category_label = __('general');
                                }
                            }
                            ?>
                            <span class="badge <?php echo $badge_class; ?>"><?php echo $category_label; ?></span>
                        </td>
                        <td><?php echo date('M d, Y', strtotime($t['created_at'])); ?></td>
                        <td>
                            <a href="preview_template.php?id=<?php echo $t['id']; ?>" class="btn btn-sm btn-info">
                                <i class="bi bi-eye"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-primary" onclick="editTemplate(<?php echo htmlspecialchars(json_encode($t)); ?>)">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <a href="javascript:void(0);" onclick="confirmDelete(<?php echo $t['id']; ?>)" class="btn btn-sm btn-danger">
                                <i class="bi bi-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <p class="text-center"><?php _e('no_templates_found'); ?> <a href="create_template.php"><?php _e('create_first_template'); ?></a></p>
        <?php endif; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="card-footer">
        <nav aria-label="Template pagination">
            <ul class="pagination justify-content-center mb-0">
                <!-- Previous page -->
                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $page-1; ?>&sort=<?php echo $sort_column; ?>&order=<?php echo $sort_order; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                
                <!-- Page numbers -->
                <?php
                $start_page = max(1, $page - 2);
                $end_page = min($total_pages, $page + 2);
                
                if ($start_page > 1) {
                    echo '<li class="page-item"><a class="page-link" href="?page=1&sort=' . $sort_column . '&order=' . $sort_order . (!empty($search) ? '&search='.urlencode($search) : '') . '">1</a></li>';
                    if ($start_page > 2) {
                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                    }
                }
                
                for ($i = $start_page; $i <= $end_page; $i++) {
                    echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '"><a class="page-link" href="?page=' . $i . '&sort=' . $sort_column . '&order=' . $sort_order . (!empty($search) ? '&search='.urlencode($search) : '') . '">' . $i . '</a></li>';
                }
                
                if ($end_page < $total_pages) {
                    if ($end_page < $total_pages - 1) {
                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                    }
                    echo '<li class="page-item"><a class="page-link" href="?page=' . $total_pages . '&sort=' . $sort_column . '&order=' . $sort_order . (!empty($search) ? '&search='.urlencode($search) : '') . '">' . $total_pages . '</a></li>';
                }
                ?>
                
                <!-- Next page -->
                <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $page+1; ?>&sort=<?php echo $sort_column; ?>&order=<?php echo $sort_order; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
</div>

<!-- Create Template Modal -->
<div class="modal fade" id="createTemplateModal" tabindex="-1" aria-labelledby="createTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createTemplateModalLabel"><?php _e('create_new_template'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="template_name" class="form-label"><?php _e('template_name'); ?> *</label>
                        <input type="text" class="form-control" id="template_name" name="template_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="subject" class="form-label"><?php _e('subject'); ?> *</label>
                        <input type="text" class="form-control" id="subject" name="subject" required>
                    </div>
                    <div class="mb-3">
                        <label for="template_category" class="form-label"><?php _e('category'); ?></label>
                        <select class="form-control" id="template_category" name="template_category">
                            <option value="general"><?php _e('general'); ?></option>
                            <option value="birthday"><?php _e('birthday'); ?></option>
                            <option value="newsletter"><?php _e('newsletter'); ?></option>
                            <option value="announcement"><?php _e('announcement'); ?></option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_birthday_template" name="is_birthday_template">
                            <label class="form-check-label" for="is_birthday_template">
                                <?php _e('birthday_template'); ?>
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="content" class="form-label"><?php _e('content'); ?> *</label>
                        <textarea class="form-control" id="content" name="content" rows="8" required placeholder="<?php _e('template_content_placeholder'); ?>"></textarea>
                        <small class="form-text text-muted"><?php _e('template_placeholders_help'); ?></small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                    <button type="submit" name="create_template" class="btn btn-primary"><?php _e('create_template'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Template Modal -->
<div class="modal fade" id="editTemplateModal" tabindex="-1" aria-labelledby="editTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTemplateModalLabel"><?php _e('edit_template'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" id="edit_template_id" name="template_id">
                    <div class="mb-3">
                        <label for="edit_template_name" class="form-label"><?php _e('template_name'); ?> *</label>
                        <input type="text" class="form-control" id="edit_template_name" name="template_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_subject" class="form-label"><?php _e('subject'); ?> *</label>
                        <input type="text" class="form-control" id="edit_subject" name="subject" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_template_category" class="form-label"><?php _e('category'); ?></label>
                        <select class="form-control" id="edit_template_category" name="template_category">
                            <option value="general"><?php _e('general'); ?></option>
                            <option value="birthday"><?php _e('birthday'); ?></option>
                            <option value="newsletter"><?php _e('newsletter'); ?></option>
                            <option value="announcement"><?php _e('announcement'); ?></option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_birthday_template" name="is_birthday_template">
                            <label class="form-check-label" for="edit_is_birthday_template">
                                <?php _e('birthday_template'); ?>
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_content" class="form-label"><?php _e('content'); ?> *</label>
                        <textarea class="form-control" id="edit_content" name="content" rows="8" required></textarea>
                        <small class="form-text text-muted"><?php _e('template_placeholders_help'); ?></small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                    <button type="submit" name="update_template" class="btn btn-primary"><?php _e('update_template'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel"><?php _e('confirm_delete'); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <?php _e('confirm_delete_template_message'); ?>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php _e('cancel'); ?></button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger"><?php _e('delete'); ?></a>
            </div>
        </div>
    </div>
</div>

<script>
    function confirmDelete(templateId) {
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        confirmBtn.href = `?delete_id=${templateId}`;
        deleteModal.show();
    }

    function editTemplate(template) {
        document.getElementById('edit_template_id').value = template.id;
        document.getElementById('edit_template_name').value = template.template_name;
        document.getElementById('edit_subject').value = template.subject;
        document.getElementById('edit_content').value = template.content;
        document.getElementById('edit_template_category').value = template.template_category || 'general';
        document.getElementById('edit_is_birthday_template').checked = template.is_birthday_template == 1;

        const editModal = new bootstrap.Modal(document.getElementById('editTemplateModal'));
        editModal.show();
    }
</script>

<?php include_once 'includes/footer.php'; ?> 