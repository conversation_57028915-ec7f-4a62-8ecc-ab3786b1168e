<?php
require_once 'config.php';

echo "🔧 Fixing Email System\n";
echo "=======================\n\n";

// Create logs directory if it doesn't exist
if (!file_exists('logs')) {
    mkdir('logs', 0755, true);
    echo "✅ Created logs directory\n";
}

echo "📧 1. Checking Email Settings Variable:\n";
echo "=======================================\n";

// Check if $emailSettings is loaded
global $emailSettings;
if (empty($emailSettings)) {
    echo "❌ \$emailSettings is empty or not loaded!\n";
    echo "🔧 Loading email settings manually...\n";
    
    // Load email settings manually
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM email_settings");
    $stmt->execute();
    $settings = $stmt->fetchAll();
    
    $emailSettings = [];
    foreach ($settings as $setting) {
        $emailSettings[$setting['setting_key']] = $setting['setting_value'];
    }
    
    echo "✅ Loaded " . count($emailSettings) . " email settings\n";
    
    // Show key settings (without passwords)
    $keySettings = ['smtp_host', 'smtp_port', 'smtp_username', 'sender_email'];
    foreach ($keySettings as $key) {
        echo "  $key: " . ($emailSettings[$key] ?? 'NOT SET') . "\n";
    }
} else {
    echo "✅ \$emailSettings is loaded with " . count($emailSettings) . " settings\n";
}

echo "\n🧪 2. Testing Email with Debug Logging:\n";
echo "=======================================\n";

// Clear any existing debug log
$debug_log_file = __DIR__ . '/logs/email_debug.log';
if (file_exists($debug_log_file)) {
    unlink($debug_log_file);
}

echo "Sending test email with debug logging enabled...\n";

$result = sendEmail(
    '<EMAIL>',
    'Godwin Bointa',
    'Test Email Debug',
    '<h2>Test Email</h2><p>This is a test email to debug the system.</p>',
    true
);

echo "Email result: " . ($result ? 'SUCCESS' : 'FAILED') . "\n\n";

// Check debug log
if (file_exists($debug_log_file)) {
    echo "📋 Debug Log Contents:\n";
    echo "======================\n";
    echo file_get_contents($debug_log_file);
} else {
    echo "❌ No debug log created\n";
}

// Check global error variable
global $last_email_error;
if (!empty($last_email_error)) {
    echo "\n❌ Last Email Error: " . $last_email_error . "\n";
}

echo "\n🔧 3. Checking PHPMailer:\n";
echo "=========================\n";

// Check if PHPMailer is available
if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    echo "✅ PHPMailer class is available\n";
    
    // Try to create PHPMailer instance
    try {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        echo "✅ PHPMailer instance created successfully\n";
        
        // Test SMTP configuration
        $mail->isSMTP();
        $mail->Host = $emailSettings['smtp_host'] ?? '';
        $mail->SMTPAuth = true;
        $mail->Username = $emailSettings['smtp_username'] ?? '';
        $mail->Password = $emailSettings['smtp_password'] ?? '';
        $mail->SMTPSecure = $emailSettings['smtp_secure'] ?? 'ssl';
        $mail->Port = $emailSettings['smtp_port'] ?? 465;
        
        echo "SMTP Host: " . $mail->Host . "\n";
        echo "SMTP Port: " . $mail->Port . "\n";
        echo "SMTP Username: " . $mail->Username . "\n";
        echo "SMTP Secure: " . $mail->SMTPSecure . "\n";
        
        // Test SMTP connection
        echo "\n🔄 Testing SMTP connection...\n";
        
        $mail->SMTPDebug = 2; // Enable verbose debug output
        $mail->Debugoutput = function($str, $level) {
            echo "SMTP Debug: $str\n";
        };
        
        // Try to connect
        if ($mail->smtpConnect()) {
            echo "✅ SMTP connection successful!\n";
            $mail->smtpClose();
        } else {
            echo "❌ SMTP connection failed!\n";
        }
        
    } catch (Exception $e) {
        echo "❌ PHPMailer error: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ PHPMailer class not found\n";
    echo "Checking for PHPMailer files...\n";
    
    $phpmailerPaths = [
        'vendor/autoload.php',
        'PHPMailer/PHPMailer.php',
        'phpmailer/phpmailer/src/PHPMailer.php'
    ];
    
    foreach ($phpmailerPaths as $path) {
        if (file_exists($path)) {
            echo "  Found: $path\n";
        }
    }
}

echo "\n🎯 4. Quick Fix Attempt:\n";
echo "========================\n";

// Try to fix common issues
echo "Attempting to fix email system...\n";

// Ensure email settings are properly formatted
$requiredSettings = [
    'smtp_host' => 'smtp.hostinger.com',
    'smtp_port' => '465',
    'smtp_username' => '<EMAIL>',
    'smtp_password' => '!3wlI!dL',
    'smtp_secure' => 'ssl',
    'smtp_auth' => '1',
    'sender_email' => '<EMAIL>',
    'sender_name' => 'Freedom Assembly Church'
];

$updated = 0;
foreach ($requiredSettings as $key => $value) {
    if (empty($emailSettings[$key]) || $emailSettings[$key] !== $value) {
        $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->execute([$key, $value, $value]);
        $emailSettings[$key] = $value;
        $updated++;
    }
}

if ($updated > 0) {
    echo "✅ Updated $updated email settings\n";
    
    echo "\n🔄 Testing email again after fixes...\n";
    
    $result = sendEmail(
        '<EMAIL>',
        'Godwin Bointa',
        'Test Email After Fix',
        '<h2>Test Email After Fix</h2><p>Testing email system after configuration fixes.</p>',
        true
    );
    
    echo "Email result after fix: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
    
    if ($result) {
        echo "\n🎉 EMAIL SYSTEM FIXED!\n";
        echo "Now try the birthday notifications again.\n";
    } else {
        echo "\n❌ Email system still not working\n";
        echo "Check the debug log above for specific errors.\n";
    }
} else {
    echo "No settings needed updating\n";
}

echo "\n📋 Final Status:\n";
echo "================\n";
if (file_exists($debug_log_file)) {
    echo "Latest debug log:\n";
    echo file_get_contents($debug_log_file);
}
?>
