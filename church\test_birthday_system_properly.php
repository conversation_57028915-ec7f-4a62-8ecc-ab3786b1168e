<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Testing Birthday System with Correct Parameters\n";
echo "==================================================\n\n";

$birthdayReminder = new BirthdayReminder($pdo);

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

echo "🎂 Testing getUpcomingBirthdays() for different days ahead:\n";
echo "==========================================================\n";

// Test for each day in the next 7 days
for ($daysAhead = 0; $daysAhead <= 7; $daysAhead++) {
    echo "Day +$daysAhead: ";
    
    try {
        $birthdays = $birthdayReminder->getUpcomingBirthdays($daysAhead);
        echo count($birthdays) . " members\n";
        
        foreach ($birthdays as $member) {
            echo "  - " . $member['full_name'] . " (ID: " . $member['id'] . ")\n";
            echo "    Birth date: " . ($member['birth_date'] ?? $member['date_of_birth'] ?? 'none') . "\n";
            echo "    Email: " . ($member['email'] ?? 'none') . "\n";
        }
        
        if ($daysAhead == 2 && count($birthdays) > 0) {
            echo "  ✅ Found birthdays for day +2 (July 21st)!\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

echo "🔍 Testing the main birthday notification workflow:\n";
echo "===================================================\n";

// Now let's see if there's a main method that processes all upcoming birthdays
// Check what the main entry point should be

echo "Looking for main workflow methods...\n";

// Check if there's a method that processes notifications for a range of days
$reflection = new ReflectionClass($birthdayReminder);
$methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);

foreach ($methods as $method) {
    $name = $method->getName();
    if (strpos($name, 'send') === 0 && strpos($name, 'Birthday') !== false) {
        $params = $method->getParameters();
        echo "Method: " . $name . "(";
        $paramNames = [];
        foreach ($params as $param) {
            $paramNames[] = ($param->isOptional() ? '[' : '') . '$' . $param->getName() . ($param->isOptional() ? ']' : '');
        }
        echo implode(', ', $paramNames) . ")\n";
    }
}

echo "\n🎯 Testing sendBirthdayReminders() method:\n";
echo "==========================================\n";

// This might be the main method
try {
    echo "Calling sendBirthdayReminders()...\n";
    $result = $birthdayReminder->sendBirthdayReminders();
    echo "✅ sendBirthdayReminders() completed\n";
    echo "Result: " . ($result ? 'Success' : 'Failed') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 Testing sendBirthdayEmails() method:\n";
echo "=======================================\n";

try {
    echo "Calling sendBirthdayEmails()...\n";
    $result = $birthdayReminder->sendBirthdayEmails();
    echo "✅ sendBirthdayEmails() completed\n";
    echo "Result: " . ($result ? 'Success' : 'Failed') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🔍 Manual test for Sandra Stern's birthday notification:\n";
echo "========================================================\n";

// Test specifically for Sandra (ID: 30) with 2 days ahead
try {
    echo "Testing sendMemberBirthdayNotifications(30, null, 2)...\n";
    $result = $birthdayReminder->sendMemberBirthdayNotifications(30, null, 2);
    echo "✅ Notification sent for Sandra\n";
    echo "Result: " . ($result ? 'Success' : 'Failed') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n🎯 Diagnosis:\n";
echo "=============\n";
echo "Now we should be able to see:\n";
echo "1. Whether getUpcomingBirthdays() finds Sandra for day +2\n";
echo "2. Which main method should be used for the workflow\n";
echo "3. Whether the notification system works when called correctly\n";
?>
