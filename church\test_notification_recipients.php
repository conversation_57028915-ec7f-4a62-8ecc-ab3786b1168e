<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Testing Birthday Notification Recipients\n";
echo "===========================================\n\n";

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

echo "🎯 Understanding the Issue:\n";
echo "===========================\n";
echo "You want to send NOTIFICATION emails to OTHER members about <PERSON>'s upcoming birthday.\n";
echo "<PERSON> should NOT receive the notification - only other members should be notified.\n\n";

// Check <PERSON>'s details
echo "🎂 Birthday Person (<PERSON>):\n";
echo "============================\n";
$stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
$stmt->execute();
$sandra = $stmt->fetch();

if ($sandra) {
    echo "✅ <PERSON> found:\n";
    echo "   ID: " . $sandra['id'] . "\n";
    echo "   Email: " . $sandra['email'] . "\n";
    echo "   Birthday: " . $sandra['birth_date'] . "\n";
    echo "   Status: " . $sandra['status'] . "\n\n";
} else {
    echo "❌ Sandra not found\n\n";
    exit;
}

// Check all other active members (potential notification recipients)
echo "📧 Potential Notification Recipients (OTHER members):\n";
echo "=====================================================\n";

$stmt = $pdo->prepare("
    SELECT id, full_name, email, status, birth_date 
    FROM members 
    WHERE id != ? 
    ORDER BY full_name
");
$stmt->execute([$sandra['id']]);
$otherMembers = $stmt->fetchAll();

echo "Total other members (excluding Sandra): " . count($otherMembers) . "\n\n";

$eligibleRecipients = [];
$ineligibleRecipients = [];

foreach ($otherMembers as $member) {
    echo "👤 " . $member['full_name'] . " (ID: " . $member['id'] . "):\n";
    echo "   Email: " . ($member['email'] ?? 'none') . "\n";
    echo "   Status: " . ($member['status'] ?? 'none') . "\n";
    
    // Check eligibility criteria
    $eligible = true;
    $reasons = [];
    
    if (empty($member['email'])) {
        $eligible = false;
        $reasons[] = "No email address";
    }
    
    if ($member['status'] !== 'active') {
        $eligible = false;
        $reasons[] = "Status is '" . ($member['status'] ?? 'none') . "' (not 'active')";
    }
    
    if ($eligible) {
        echo "   ✅ ELIGIBLE for notifications\n";
        $eligibleRecipients[] = $member;
    } else {
        echo "   ❌ NOT ELIGIBLE: " . implode(", ", $reasons) . "\n";
        $ineligibleRecipients[] = $member;
    }
    
    echo "\n";
}

echo "📊 Summary:\n";
echo "===========\n";
echo "Eligible notification recipients: " . count($eligibleRecipients) . "\n";
echo "Ineligible recipients: " . count($ineligibleRecipients) . "\n\n";

if (count($eligibleRecipients) == 0) {
    echo "❌ NO ELIGIBLE RECIPIENTS FOUND!\n";
    echo "This explains the error: 'No notification emails were sent. Please check if there are eligible recipients.'\n\n";
    
    echo "🔧 To fix this issue:\n";
    echo "=====================\n";
    
    if (count($ineligibleRecipients) > 0) {
        echo "You have " . count($ineligibleRecipients) . " other members, but they need to be fixed:\n\n";
        
        foreach ($ineligibleRecipients as $member) {
            echo "👤 " . $member['full_name'] . ":\n";
            
            if (empty($member['email'])) {
                echo "   🔧 Add an email address\n";
            }
            
            if ($member['status'] !== 'active') {
                echo "   🔧 Change status to 'active'\n";
                
                // Offer to fix the status
                echo "   💡 Fixing status now...\n";
                $stmt = $pdo->prepare("UPDATE members SET status = 'active' WHERE id = ?");
                $result = $stmt->execute([$member['id']]);
                
                if ($result) {
                    echo "   ✅ Status updated to 'active'\n";
                } else {
                    echo "   ❌ Failed to update status\n";
                }
            }
            
            echo "\n";
        }
    } else {
        echo "You need to add more members to the database who can receive notifications.\n";
        echo "Each member needs:\n";
        echo "- A valid email address\n";
        echo "- Status set to 'active'\n";
        echo "- Must not be the birthday person (Sandra)\n";
    }
    
} else {
    echo "✅ Found " . count($eligibleRecipients) . " eligible recipients!\n\n";
    
    echo "🔄 Testing notification sending:\n";
    echo "=================================\n";
    
    try {
        $birthdayReminder = new BirthdayReminder($pdo);
        
        // Calculate days until Sandra's birthday
        $today = new DateTime();
        $sandrasBirthday = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $sandrasBirthday->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        echo "Sandra's birthday is in " . $daysUntil . " days\n";
        echo "Attempting to send notifications to " . count($eligibleRecipients) . " members...\n\n";
        
        // Test the notification sending
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
        
        if ($result) {
            echo "✅ Notifications sent successfully!\n";
        } else {
            echo "❌ Notification sending failed\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing notifications: " . $e->getMessage() . "\n";
    }
}

echo "\n🎯 Next Steps:\n";
echo "==============\n";
if (count($eligibleRecipients) == 0) {
    echo "1. Fix the member statuses and email addresses above\n";
    echo "2. Try the birthday notifications again\n";
    echo "3. The system should now find eligible recipients\n";
} else {
    echo "1. You have eligible recipients, so notifications should work\n";
    echo "2. If you're still getting errors, there might be a template issue\n";
    echo "3. Check that you have notification templates set up\n";
}
?>
