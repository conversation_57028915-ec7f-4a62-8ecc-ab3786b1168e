<?php
require_once 'config.php';

echo "🔧 Fixing Duplicate Member Names in Email Templates\n";
echo "==================================================\n\n";

try {
    // Fix Template ID 58 (Clean Birthday Notification)
    echo "🎯 Fixing Template ID 58 (Clean Birthday Notification)...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 58");
    $stmt->execute();
    $template58 = $stmt->fetch();
    
    if ($template58) {
        $content = $template58['content'];
        
        // Remove the duplicate member name displays after the image
        // Keep only the image, remove both name displays
        $content = preg_replace(
            '/\{member_image\}\s*<h3[^>]*>\{birthday_member_full_name\}<\/h3>\s*<h2[^>]*>🎉\s*\{birthday_member_name\}\s*🎉<\/h2>/',
            '{member_image}',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
        $stmt->execute([$content]);
        
        echo "✅ Fixed Template 58 - Removed duplicate member names after image\n";
    } else {
        echo "❌ Template 58 not found\n";
    }
    
    // Fix Template ID 37 (Member Upcoming Birthday Notification 1)
    echo "\n🎯 Fixing Template ID 37 (Member Upcoming Birthday Notification 1)...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 37");
    $stmt->execute();
    $template37 = $stmt->fetch();
    
    if ($template37) {
        $content = $template37['content'];
        
        // Remove the duplicate member name display after the image
        // Keep only the image, remove the styled member name
        $content = preg_replace(
            '/\{member_image\}\s*<div class="member-name">🎉\s*\{birthday_member_full_name\}\s*🎉<\/div>/',
            '{member_image}',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 37");
        $stmt->execute([$content]);
        
        echo "✅ Fixed Template 37 - Removed duplicate member name after image\n";
    } else {
        echo "❌ Template 37 not found\n";
    }
    
    // Fix Template ID 47 (Member Upcoming Birthday Notification 3) if needed
    echo "\n🎯 Checking Template ID 47 (Member Upcoming Birthday Notification 3)...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 47");
    $stmt->execute();
    $template47 = $stmt->fetch();
    
    if ($template47) {
        $content = $template47['content'];
        
        // Check if it has duplicate names after image
        if (preg_match('/\{birthday_member_image_url\}.*?<div class="member-name">\{birthday_member_first_name\}<\/div>/s', $content)) {
            // Remove the member name div after the image
            $content = preg_replace(
                '/<div class="member-name">\{birthday_member_first_name\}<\/div>/',
                '',
                $content
            );
            
            // Update the template
            $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 47");
            $stmt->execute([$content]);
            
            echo "✅ Fixed Template 47 - Removed duplicate member name after image\n";
        } else {
            echo "ℹ️  Template 47 doesn't have duplicate names after image\n";
        }
    } else {
        echo "❌ Template 47 not found\n";
    }
    
    echo "\n🎉 All duplicate member name fixes complete!\n";
    echo "📧 Email templates now show only the member image without redundant name text.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
