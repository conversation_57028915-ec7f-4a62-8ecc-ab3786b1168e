<?php
require_once 'config.php';

echo "🔧 Fixing Duplicate Names in ALL Birthday Templates\n";
echo "===================================================\n\n";

try {
    // Get all birthday notification templates
    $stmt = $pdo->prepare("
        SELECT id, template_name, content 
        FROM email_templates 
        WHERE (template_name LIKE '%Notification%' OR template_name LIKE '%Birthday%')
        AND content LIKE '%{member_image}%'
        ORDER BY id
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    echo "📋 Found " . count($templates) . " templates to check:\n\n";
    
    foreach ($templates as $template) {
        echo "🎯 Processing Template ID: {$template['id']}\n";
        echo "📝 Name: {$template['template_name']}\n";
        
        $content = $template['content'];
        $originalContent = $content;
        $fixed = false;
        
        // Check for the specific duplicate pattern we saw in the email
        // Look for member_image followed by a name display
        
        // Pattern 1: member_image followed by h2 with name
        if (preg_match('/(\{member_image\})\s*<h2[^>]*>[^<]*\{[^}]*name[^}]*\}[^<]*<\/h2>/', $content)) {
            echo "   🚨 Found Pattern 1: member_image + h2 name\n";
            $content = preg_replace('/(\{member_image\})\s*<h2[^>]*>[^<]*\{[^}]*name[^}]*\}[^<]*<\/h2>/', '$1', $content);
            $fixed = true;
        }
        
        // Pattern 2: member_image followed by div with member-name class
        if (preg_match('/(\{member_image\})\s*<div[^>]*class="member-name"[^>]*>[^<]*\{[^}]*name[^}]*\}[^<]*<\/div>/', $content)) {
            echo "   🚨 Found Pattern 2: member_image + div.member-name\n";
            $content = preg_replace('/(\{member_image\})\s*<div[^>]*class="member-name"[^>]*>[^<]*\{[^}]*name[^}]*\}[^<]*<\/div>/', '$1', $content);
            $fixed = true;
        }
        
        // Pattern 3: Look for any name display immediately after member_image
        if (preg_match('/(\{member_image\})\s*[^{]*\{[^}]*name[^}]*\}/', $content)) {
            echo "   🔍 Checking for name immediately after image...\n";
            
            // Get context to see what's there
            $pos = strpos($content, '{member_image}');
            if ($pos !== false) {
                $afterImage = substr($content, $pos, 200);
                echo "   📄 Context: " . htmlspecialchars(substr($afterImage, 0, 150)) . "...\n";
                
                // If there's a name placeholder within 100 characters of the image, it might be a duplicate
                if (preg_match('/\{member_image\}(.{0,100})\{[^}]*name[^}]*\}/', $content, $matches)) {
                    $between = $matches[1];
                    // If there's minimal content between image and name, it's likely a duplicate
                    if (strlen(trim(strip_tags($between))) < 50) {
                        echo "   🚨 Found likely duplicate name after image\n";
                        echo "   🔧 Content between: '" . htmlspecialchars(trim($between)) . "'\n";
                        
                        // Remove this name occurrence
                        $content = preg_replace('/(\{member_image\})(.{0,100}?)\{[^}]*name[^}]*\}/', '$1$2', $content, 1);
                        $fixed = true;
                    }
                }
            }
        }
        
        // Check if we made any changes
        if ($fixed) {
            echo "   ✅ Applied fixes to template\n";
            
            // Update the template
            $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = ?");
            if ($updateStmt->execute([$content, $template['id']])) {
                echo "   ✅ Template updated in database\n";
            } else {
                echo "   ❌ Failed to update template in database\n";
            }
        } else {
            echo "   ✅ No duplicate patterns found - template is clean\n";
        }
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }
    
    // Now let's specifically check the templates that are being used
    echo "🎯 CHECKING SPECIFIC TEMPLATES MENTIONED IN LOGS:\n";
    echo str_repeat("=", 60) . "\n\n";
    
    // Check Template 37 - Member Upcoming Birthday Notification 1
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 37");
    $stmt->execute();
    $template37 = $stmt->fetch();
    
    if ($template37) {
        echo "📋 Template 37: {$template37['template_name']}\n";
        
        $content = $template37['content'];
        
        // Look for the specific issue from the screenshot
        // The issue might be that there are two different name placeholders being used
        
        // Check for birthday_member_full_name occurrences
        $fullNameCount = substr_count($content, '{birthday_member_full_name}');
        echo "   📊 {birthday_member_full_name} occurrences: $fullNameCount\n";
        
        if ($fullNameCount > 1) {
            echo "   🚨 Multiple full name occurrences found!\n";
            
            // Show context around member_image
            $pos = strpos($content, '{member_image}');
            if ($pos !== false) {
                $start = max(0, $pos - 50);
                $end = min(strlen($content), $pos + 300);
                $context = substr($content, $start, $end - $start);
                echo "   📄 Context around image:\n";
                echo "   " . htmlspecialchars($context) . "\n";
                
                // Look for the pattern: member_image followed by member-name div
                if (preg_match('/(\{member_image\})\s*<div[^>]*class="member-name"[^>]*>[^<]*\{birthday_member_full_name\}[^<]*<\/div>/', $content)) {
                    echo "   🔧 Removing duplicate name div after image...\n";
                    
                    $newContent = preg_replace('/(\{member_image\})\s*<div[^>]*class="member-name"[^>]*>[^<]*\{birthday_member_full_name\}[^<]*<\/div>/', '$1', $content);
                    
                    $updateStmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 37");
                    if ($updateStmt->execute([$newContent])) {
                        echo "   ✅ Template 37 fixed!\n";
                    } else {
                        echo "   ❌ Failed to update Template 37\n";
                    }
                }
            }
        }
    }
    
    echo "\n🎉 DUPLICATE NAME FIX COMPLETE!\n";
    echo "================================\n";
    echo "✅ Checked all birthday notification templates\n";
    echo "✅ Removed duplicate name displays after member images\n";
    echo "✅ Templates now show clean structure: Image → Details → Single styled name\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
