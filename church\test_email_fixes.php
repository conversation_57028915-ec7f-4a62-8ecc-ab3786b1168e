<?php
/**
 * Comprehensive Test Script for Email Attachment Fix
 * Tests the fixes implemented to prevent images from appearing as both inline and attachments
 */

// Include the config file with our fixes
require_once 'config.php';

// Test configuration
$TEST_EMAIL = '<EMAIL>'; // Change this to your test email
$TEST_NAME = 'Test User';

// Create test log file
$test_log_file = __DIR__ . '/logs/email_test_results.log';
if (!file_exists(dirname($test_log_file))) {
    mkdir(dirname($test_log_file), 0755, true);
}

function logTest($message) {
    global $test_log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($test_log_file, "[$timestamp] $message\n", FILE_APPEND);
    echo "[$timestamp] $message\n";
}

function createTestMemberData($type = 'regular') {
    $baseData = [
        'full_name' => 'John Test Doe',
        'first_name' => '<PERSON>',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'phone_number' => '555-0123',
        'home_address' => '123 Test Street',
        'birth_date' => '1990-05-15',
        '_original_image_path' => 'uploads/members/test_profile.jpg'
    ];

    if ($type === 'birthday') {
        $baseData['_is_birthday_notification'] = true;
        $baseData['birthday_member_name'] = 'Jane Birthday';
        $baseData['birthday_member_full_name'] = 'Jane Birthday Smith';
        $baseData['birthday_member_email'] = '<EMAIL>';
        $baseData['birthday_member_photo_url'] = 'http://localhost/campaign/church/uploads/members/birthday_member.jpg';
        $baseData['_birthday_member_original_image_path'] = 'uploads/members/birthday_member.jpg';
        $baseData['birthday_member_age'] = 30;
        $baseData['upcoming_birthday_date'] = 'May 15, 2024';
        $baseData['days_until_birthday'] = 0;
        $baseData['days_text'] = 'today';
    }

    return $baseData;
}

function createTestEmailBody($type = 'regular') {
    if ($type === 'birthday') {
        return '
        <html>
        <body>
            <h2>🎉 Birthday Celebration! 🎉</h2>
            <p>Dear {full_name},</p>
            <p>Today we celebrate {birthday_member_full_name}\'s {birthday_member_age}th birthday!</p>
            <div style="text-align: center;">
                <img src="{birthday_member_photo_url}" alt="Birthday Member" style="width:150px;height:150px;border-radius:50%;">
            </div>
            <p>Please join us in wishing {birthday_member_name} a very happy birthday!</p>
            <p>Best regards,<br>Church Management Team</p>
        </body>
        </html>';
    } else {
        return '
        <html>
        <body>
            <h2>Welcome Message</h2>
            <p>Dear {full_name},</p>
            <p>This is a test email with your profile image:</p>
            <div style="text-align: center;">
                <img src="{member_image_url}" alt="Profile" style="width:100px;height:100px;border-radius:50%;">
            </div>
            <p>Thank you for being part of our community!</p>
        </body>
        </html>';
    }
}

// Create test image files if they don't exist
function createTestImages() {
    $uploadsDir = __DIR__ . '/uploads/members';
    if (!file_exists($uploadsDir)) {
        mkdir($uploadsDir, 0755, true);
    }

    $testImages = [
        'test_profile.jpg',
        'birthday_member.jpg'
    ];

    foreach ($testImages as $imageName) {
        $imagePath = $uploadsDir . '/' . $imageName;
        if (!file_exists($imagePath)) {
            // Create a simple test image
            $image = imagecreatetruecolor(150, 150);
            $bgColor = imagecolorallocate($image, 100, 150, 200);
            $textColor = imagecolorallocate($image, 255, 255, 255);
            imagefill($image, 0, 0, $bgColor);
            imagestring($image, 5, 30, 70, 'TEST', $textColor);
            imagejpeg($image, $imagePath, 90);
            imagedestroy($image);
            logTest("Created test image: $imagePath");
        }
    }
}

// Test 1: Regular Email with Profile Image
function testRegularEmail() {
    global $TEST_EMAIL, $TEST_NAME;
    
    logTest("=== TEST 1: Regular Email with Profile Image ===");
    
    $memberData = createTestMemberData('regular');
    $emailBody = createTestEmailBody('regular');
    
    // Process template placeholders
    $processedBody = replaceTemplatePlaceholders($emailBody, $memberData);
    
    logTest("Sending regular email test...");
    $result = sendEmail($TEST_EMAIL, $TEST_NAME, 'Test: Regular Email with Profile Image', $processedBody, true, $memberData);
    
    if ($result) {
        logTest("✅ Regular email test PASSED");
    } else {
        logTest("❌ Regular email test FAILED");
    }
    
    return $result;
}

// Test 2: Birthday Notification Email
function testBirthdayEmail() {
    global $TEST_EMAIL, $TEST_NAME;
    
    logTest("=== TEST 2: Birthday Notification Email ===");
    
    $memberData = createTestMemberData('birthday');
    $emailBody = createTestEmailBody('birthday');
    
    // Process template placeholders
    $processedBody = replaceTemplatePlaceholders($emailBody, $memberData);
    
    logTest("Sending birthday notification test...");
    $result = sendEmail($TEST_EMAIL, $TEST_NAME, 'Test: Birthday Notification - Jane\'s Birthday!', $processedBody, true, $memberData);
    
    if ($result) {
        logTest("✅ Birthday email test PASSED");
    } else {
        logTest("❌ Birthday email test FAILED");
    }
    
    return $result;
}

// Test 3: Newsletter Email (should skip images)
function testNewsletterEmail() {
    global $TEST_EMAIL, $TEST_NAME;
    
    logTest("=== TEST 3: Newsletter Email (No Images) ===");
    
    $memberData = createTestMemberData('regular');
    $memberData['template_name'] = 'Monthly Newsletter';
    
    $emailBody = '
    <html>
    <body>
        <h2>Monthly Newsletter</h2>
        <p>Dear {full_name},</p>
        <p>This is our monthly newsletter. No member images should appear.</p>
        <img src="{member_image_url}" alt="Should be removed">
        <p>Thank you for reading!</p>
    </body>
    </html>';
    
    // Process template placeholders
    $processedBody = replaceTemplatePlaceholders($emailBody, $memberData);
    
    logTest("Sending newsletter test...");
    $result = sendEmail($TEST_EMAIL, $TEST_NAME, 'Test: Monthly Newsletter', $processedBody, true, $memberData);
    
    if ($result) {
        logTest("✅ Newsletter email test PASSED");
    } else {
        logTest("❌ Newsletter email test FAILED");
    }
    
    return $result;
}

// Main test execution
function runAllTests() {
    logTest("🚀 Starting Comprehensive Email Fix Tests");
    logTest("Testing email fixes to prevent images appearing as attachments");
    
    // Create test images
    createTestImages();
    
    $results = [];
    
    // Run tests
    $results['regular'] = testRegularEmail();
    sleep(2); // Brief pause between tests
    
    $results['birthday'] = testBirthdayEmail();
    sleep(2);
    
    $results['newsletter'] = testNewsletterEmail();
    
    // Summary
    logTest("\n=== TEST SUMMARY ===");
    $passed = 0;
    $total = count($results);
    
    foreach ($results as $testName => $result) {
        $status = $result ? "✅ PASSED" : "❌ FAILED";
        logTest("$testName: $status");
        if ($result) $passed++;
    }
    
    logTest("\nOverall Result: $passed/$total tests passed");
    
    if ($passed === $total) {
        logTest("🎉 ALL TESTS PASSED! Email fixes are working correctly.");
        logTest("✅ Images should now appear ONLY inline, never as attachments");
    } else {
        logTest("⚠️  Some tests failed. Check the debug logs for details.");
    }
    
    logTest("\n📋 Next Steps:");
    logTest("1. Check your test email inbox for the test emails");
    logTest("2. Verify that images appear inline only (no attachments)");
    logTest("3. Check debug logs in /logs/ directory for detailed information");
    logTest("4. If tests passed, the fixes are ready for production use");
    
    return $passed === $total;
}

// Run the tests
if (php_sapi_name() === 'cli') {
    // Running from command line
    runAllTests();
} else {
    // Running from web browser
    echo "<pre>";
    runAllTests();
    echo "</pre>";
}
?>
