[2025-07-19 06:49:03] 🔍 Starting Email Attachment Diagnostic
[2025-07-19 06:49:03] This will help us understand which function is causing attachment issues
[2025-07-19 06:49:03] Created test birthday image: C:\xampp\htdocs\campaign\church/uploads/members/test_birthday.jpg
[2025-07-19 06:49:03] === TESTING sendEmail() function (config.php) ===
[2025-07-19 06:49:03] Calling sendEmail() with birthday notification data...
[2025-07-19 06:49:07] sendEmail() result: SUCCESS
[2025-07-19 06:49:09] === TESTING sendScheduledEmail() function (email_functions.php) ===
[2025-07-19 06:49:09] Calling sendScheduledEmail() with birthday notification data...
[2025-07-19 06:49:17] 🔍 Starting Email Attachment Diagnostic
[2025-07-19 06:49:17] This will help us understand which function is causing attachment issues
[2025-07-19 06:49:17] === TESTING sendEmail() function (config.php) ===
[2025-07-19 06:49:17] Calling sendEmail() with birthday notification data...
[2025-07-19 06:49:20] sendEmail() result: SUCCESS
[2025-07-19 06:49:22] === TESTING sendScheduledEmail() function (email_functions.php) ===
[2025-07-19 06:49:22] Calling sendScheduledEmail() with birthday notification data...
[2025-07-19 06:50:15] 🔍 Starting Email Attachment Diagnostic
[2025-07-19 06:50:15] This will help us understand which function is causing attachment issues
[2025-07-19 06:50:15] === TESTING sendEmail() function (config.php) ===
[2025-07-19 06:50:15] Calling sendEmail() with birthday notification data...
[2025-07-19 06:50:18] sendEmail() result: SUCCESS
[2025-07-19 06:50:20] === TESTING sendScheduledEmail() function (email_functions.php) ===
[2025-07-19 06:50:20] Calling sendScheduledEmail() with birthday notification data...
[2025-07-19 06:52:14] 🔍 Starting Email Attachment Diagnostic
[2025-07-19 06:52:14] This will help us understand which function is causing attachment issues
[2025-07-19 06:52:14] === TESTING sendEmail() function (config.php) ===
[2025-07-19 06:52:14] Calling sendEmail() with birthday notification data...
[2025-07-19 06:52:17] sendEmail() result: SUCCESS
[2025-07-19 06:52:19] === TESTING sendScheduledEmail() function (email_functions.php) ===
[2025-07-19 06:52:19] Calling sendScheduledEmail() with birthday notification data...
[2025-07-19 06:53:10] 🔍 Starting Email Attachment Diagnostic
[2025-07-19 06:53:10] This will help us understand which function is causing attachment issues
[2025-07-19 06:53:10] === TESTING sendEmail() function (config.php) ===
[2025-07-19 06:53:10] Calling sendEmail() with birthday notification data...
[2025-07-19 06:53:13] sendEmail() result: SUCCESS
[2025-07-19 06:53:15] === TESTING sendScheduledEmail() function (email_functions.php) ===
[2025-07-19 06:53:15] Calling sendScheduledEmail() with birthday notification data...
[2025-07-19 06:53:17] sendScheduledEmail() result: SUCCESS
[2025-07-19 06:53:19] === TESTING sendEmailWithPHPMailer() function (email_functions.php) ===
[2025-07-19 06:53:19] Calling sendEmailWithPHPMailer() with image URL...
[2025-07-19 06:53:27] sendEmailWithPHPMailer() result: FAILED - Email could not be sent after 3 attempts. Error: SMTP Error: The following recipients failed: <EMAIL>: <<EMAIL>>: Sender address rejected: Domain example.com does not accept mail (nullMX)
SMTP server error: RCPT TO command failed Detail: <<EMAIL>>: Sender address rejected: Domain example.com does not accept mail (nullMX)
 SMTP code: 550 Additional SMTP info: 5.7.27
[2025-07-19 06:53:27] 
=== DIAGNOSTIC SUMMARY ===
[2025-07-19 06:53:27] sendEmail: ✅ SUCCESS
[2025-07-19 06:53:27] sendScheduledEmail: ✅ SUCCESS
[2025-07-19 06:53:27] sendEmailWithPHPMailer: ❌ FAILED
[2025-07-19 06:53:27] 
📋 NEXT STEPS:
[2025-07-19 06:53:27] 1. Check your email inbox for 3 test emails
[2025-07-19 06:53:27] 2. For each email, note:
[2025-07-19 06:53:27]    - Does the image appear inline in the email body?
[2025-07-19 06:53:27]    - Does the email have file attachments?
[2025-07-19 06:53:27]    - If yes, what are the attachment names/types?
[2025-07-19 06:53:27] 3. Check debug logs:
[2025-07-19 06:53:27]    - /logs/email_debug.log
[2025-07-19 06:53:27]    - /logs/scheduled_email_debug.log
[2025-07-19 06:53:27]    - /logs/email_attachment_diagnostic.log
[2025-07-19 06:53:27] 
🎯 EXPECTED RESULTS:
[2025-07-19 06:53:27] - sendEmail(): Should have inline image only (no attachments)
[2025-07-19 06:53:27] - sendScheduledEmail(): Should have image URL only (no embedding)
[2025-07-19 06:53:27] - sendEmailWithPHPMailer(): Should have image URL only (no embedding)
[2025-07-19 06:53:27] 
⚠️  IF ATTACHMENTS APPEAR:
[2025-07-19 06:53:27] - Note which function(s) create attachments
[2025-07-19 06:53:27] - Check attachment file names and types
[2025-07-19 06:53:27] - This will help identify the root cause
[2025-07-19 07:08:55] 🔍 Starting Email Attachment Diagnostic
[2025-07-19 07:08:55] This will help us understand which function is causing attachment issues
[2025-07-19 07:08:55] === TESTING sendEmail() function (config.php) ===
[2025-07-19 07:08:55] Calling sendEmail() with birthday notification data...
[2025-07-19 07:08:57] sendEmail() result: SUCCESS
[2025-07-19 07:08:59] === TESTING sendScheduledEmail() function (email_functions.php) ===
[2025-07-19 07:08:59] Calling sendScheduledEmail() with birthday notification data...
[2025-07-19 07:09:02] sendScheduledEmail() result: SUCCESS
[2025-07-19 07:09:04] === TESTING sendEmailWithPHPMailer() function (email_functions.php) ===
[2025-07-19 07:09:04] Calling sendEmailWithPHPMailer() with image URL...
[2025-07-19 07:09:11] sendEmailWithPHPMailer() result: FAILED - Email could not be sent after 3 attempts. Error: SMTP Error: The following recipients failed: <EMAIL>: <<EMAIL>>: Sender address rejected: Domain example.com does not accept mail (nullMX)
SMTP server error: RCPT TO command failed Detail: <<EMAIL>>: Sender address rejected: Domain example.com does not accept mail (nullMX)
 SMTP code: 550 Additional SMTP info: 5.7.27
[2025-07-19 07:09:11] 
=== DIAGNOSTIC SUMMARY ===
[2025-07-19 07:09:11] sendEmail: ✅ SUCCESS
[2025-07-19 07:09:11] sendScheduledEmail: ✅ SUCCESS
[2025-07-19 07:09:11] sendEmailWithPHPMailer: ❌ FAILED
[2025-07-19 07:09:11] 
📋 NEXT STEPS:
[2025-07-19 07:09:11] 1. Check your email inbox for 3 test emails
[2025-07-19 07:09:11] 2. For each email, note:
[2025-07-19 07:09:11]    - Does the image appear inline in the email body?
[2025-07-19 07:09:11]    - Does the email have file attachments?
[2025-07-19 07:09:11]    - If yes, what are the attachment names/types?
[2025-07-19 07:09:11] 3. Check debug logs:
[2025-07-19 07:09:11]    - /logs/email_debug.log
[2025-07-19 07:09:11]    - /logs/scheduled_email_debug.log
[2025-07-19 07:09:11]    - /logs/email_attachment_diagnostic.log
[2025-07-19 07:09:11] 
🎯 EXPECTED RESULTS:
[2025-07-19 07:09:11] - sendEmail(): Should have inline image only (no attachments)
[2025-07-19 07:09:11] - sendScheduledEmail(): Should have image URL only (no embedding)
[2025-07-19 07:09:11] - sendEmailWithPHPMailer(): Should have image URL only (no embedding)
[2025-07-19 07:09:11] 
⚠️  IF ATTACHMENTS APPEAR:
[2025-07-19 07:09:11] - Note which function(s) create attachments
[2025-07-19 07:09:11] - Check attachment file names and types
[2025-07-19 07:09:11] - This will help identify the root cause
