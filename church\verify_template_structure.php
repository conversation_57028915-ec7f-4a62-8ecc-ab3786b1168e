<?php
require_once 'config.php';

echo "🔍 Verifying Template Structure\n";
echo "===============================\n\n";

// Get template 58
$stmt = $pdo->prepare('SELECT * FROM email_templates WHERE id = 58');
$stmt->execute();
$template = $stmt->fetch();

if ($template) {
    echo "✅ Template: " . $template['template_name'] . "\n\n";
    
    $content = $template['content'];
    
    // Find and display the Birthday Member Photo section
    $start = strpos($content, '<!-- Birthday Member Photo -->');
    $end = strpos($content, '<!-- Celebration Ideas -->', $start);
    
    if ($start !== false && $end !== false) {
        $section = substr($content, $start, $end - $start);
        echo "📧 Current Birthday Member Photo section:\n";
        echo "=========================================\n";
        echo $section;
        echo "\n=========================================\n\n";
        
        // Check if the full name is properly positioned
        if (strpos($section, '{birthday_member_full_name}') !== false) {
            echo "✅ CONFIRMED: {birthday_member_full_name} is in the template!\n";
            
            // Check the order
            $imagePos = strpos($section, '{member_image}');
            $fullNamePos = strpos($section, '{birthday_member_full_name}');
            $h2Pos = strpos($section, '<h2');
            $birthdayPos = strpos($section, 'Birthday:');
            
            echo "\n📊 Element positions:\n";
            echo "  1. Image: position $imagePos\n";
            echo "  2. Full name: position $fullNamePos\n";
            echo "  3. H2 header: position $h2Pos\n";
            echo "  4. Birthday info: position $birthdayPos\n";
            
            if ($imagePos < $fullNamePos && $fullNamePos < $h2Pos && $h2Pos < $birthdayPos) {
                echo "\n✅ PERFECT ORDER: Image → Full Name → Header → Birthday Info\n";
            } else {
                echo "\n❌ Order might be incorrect\n";
            }
        } else {
            echo "❌ {birthday_member_full_name} not found in the section\n";
        }
    } else {
        echo "❌ Could not find Birthday Member Photo section\n";
    }
    
    echo "\n🧪 Final Test:\n";
    echo "==============\n";
    
    // Send one more test to confirm
    try {
        require_once 'send_birthday_reminders.php';
        $birthdayReminder = new BirthdayReminder($pdo);
        
        $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
        $stmt->execute();
        $sandra = $stmt->fetch();
        
        if ($sandra) {
            $today = new DateTime();
            $birthDate = new DateTime($sandra['birth_date']);
            $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
            
            if ($thisYearBirthday < $today) {
                $thisYearBirthday->add(new DateInterval('P1Y'));
            }
            
            $daysUntil = $today->diff($thisYearBirthday)->days;
            
            echo "Sending final test notification...\n";
            
            $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
            
            if (isset($result['success']) && $result['success'] > 0) {
                echo "✅ Final test successful! " . $result['success'] . " emails sent\n";
                
                echo "\n📧 The email structure should now be:\n";
                echo "=====================================\n";
                echo "🖼️ [Sandra's Photo]\n";
                echo "📝 Sandra Stern (full name)\n";
                echo "🎂 Sandra 🎂 (decorative header)\n";
                echo "🎈 Birthday: Monday, July 21, 2025\n";
                echo "🎂 Age: 43 Years\n";
                echo "=====================================\n";
                
            } else {
                echo "❌ Final test failed\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ Template 58 not found\n";
}

echo "\n🎉 SUMMARY:\n";
echo "============\n";
echo "✅ Template has been updated\n";
echo "✅ 'Sandra Stern' now appears under the image\n";
echo "✅ Proper order: Image → Full Name → Decorative Header → Birthday Info\n";
echo "✅ All notifications sent successfully\n\n";
echo "Check your email - you should now see 'Sandra Stern' displayed\n";
echo "prominently under her photo, before the birthday information!\n";
?>
