<?php
require_once 'config.php';

echo "🔧 Fixing Notification Settings Table\n";
echo "======================================\n\n";

// First, check the current table structure
echo "📋 Checking automated_emails_settings table structure:\n";
$stmt = $pdo->query("DESCRIBE automated_emails_settings");
$columns = $stmt->fetchAll();

echo "Current columns:\n";
foreach ($columns as $column) {
    echo "  - " . $column['Field'] . " (" . $column['Type'] . ")\n";
}

// Check if 'enabled' column exists
$hasEnabledColumn = false;
foreach ($columns as $column) {
    if ($column['Field'] === 'enabled') {
        $hasEnabledColumn = true;
        break;
    }
}

if (!$hasEnabledColumn) {
    echo "\n❌ 'enabled' column is missing!\n";
    echo "🔧 Adding 'enabled' column...\n";
    
    try {
        $stmt = $pdo->exec("ALTER TABLE automated_emails_settings ADD COLUMN enabled TINYINT(1) DEFAULT 1");
        echo "✅ Added 'enabled' column successfully!\n";
    } catch (Exception $e) {
        echo "❌ Failed to add 'enabled' column: " . $e->getMessage() . "\n";
    }
} else {
    echo "\n✅ 'enabled' column exists\n";
}

echo "\n📋 Current notification settings:\n";
$stmt = $pdo->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'notification'");
$stmt->execute();
$settings = $stmt->fetch();

if ($settings) {
    echo "Found notification settings:\n";
    foreach ($settings as $key => $value) {
        if (!is_numeric($key)) {
            echo "  - $key: " . ($value ?? 'NULL') . "\n";
        }
    }
    
    // Now try to enable notifications
    echo "\n🔧 Enabling notifications...\n";
    try {
        $stmt = $pdo->prepare("UPDATE automated_emails_settings SET enabled = 1 WHERE email_type = 'notification'");
        $result = $stmt->execute();
        
        if ($result) {
            echo "✅ Notifications enabled successfully!\n";
        } else {
            echo "❌ Failed to enable notifications\n";
        }
    } catch (Exception $e) {
        echo "❌ Error enabling notifications: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "❌ No notification settings found\n";
    echo "🔧 Creating notification settings...\n";
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO automated_emails_settings 
            (email_type, days_before, template_ids, enabled, send_time, created_at) 
            VALUES ('notification', 2, '37,46,47', 1, '09:00:00', NOW())
        ");
        $result = $stmt->execute();
        
        if ($result) {
            echo "✅ Created notification settings successfully!\n";
        } else {
            echo "❌ Failed to create notification settings\n";
        }
    } catch (Exception $e) {
        echo "❌ Error creating notification settings: " . $e->getMessage() . "\n";
    }
}

echo "\n🎯 Final Test:\n";
echo "==============\n";

// Test the notification system one more time
try {
    require_once 'send_birthday_reminders.php';
    
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Get Sandra's ID
    $stmt = $pdo->prepare("SELECT id FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        echo "Testing notification for Sandra (ID: " . $sandra['id'] . ")...\n";
        
        // Test with 2 days until birthday
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, 2);
        
        if ($result) {
            echo "✅ Final notification test: SUCCESS\n";
            echo "\n🎉 PROBLEM SOLVED!\n";
            echo "The admin interface should now work correctly.\n";
        } else {
            echo "❌ Final notification test: FAILED\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error in final test: " . $e->getMessage() . "\n";
}

echo "\n✅ Try the admin interface now:\n";
echo "http://localhost/campaign/church/admin/send_birthday_notification.php\n";
?>
