<?php
/**
 * Quick Modal Width Test
 * Simple test to verify modal width optimization is working
 */

// Security check
if (!defined('ADMIN_ACCESS')) {
    define('ADMIN_ACCESS', true);
}

// Include the configuration file
require_once '../config.php';

// Set page variables
$page_title = 'Modal Width Test';
$page_header = 'Modal Width Test';
$page_description = 'Quick test of modal width optimization';

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="bi bi-info-circle-fill me-2"></i>Modal Width Test</h5>
                <p>Click the buttons below to test different modal widths. The modals should now be more reasonably sized.</p>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Test Modal Widths</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Before Optimization (Old Behavior)</h6>
                            <p class="text-muted small">These would have been very wide before</p>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#oldLargeModal">
                                    Old Large Modal (Now Optimized)
                                </button>
                                <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#oldXLModal">
                                    Old XL Modal (Now Optimized)
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>New Optimized Modals</h6>
                            <p class="text-muted small">These use the new width classes</p>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#formModal">
                                    Form Modal (Auto-Optimized)
                                </button>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#confirmModal">
                                    Confirmation Modal (Compact)
                                </button>
                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#mediumModal">
                                    Medium Modal (600px)
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>JavaScript Controls</h6>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="testOptimization()">
                                    Test Auto-Optimization
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="showWidthInfo()">
                                    Show Width Info
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Old Large Modal (Bootstrap modal-lg - now optimized) -->
<div class="modal fade" id="oldLargeModal" tabindex="-1" aria-labelledby="oldLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="oldLargeModalLabel">Old Large Modal (Now Optimized)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p><strong>Before:</strong> This modal would have been 800px wide (very overwhelming)</p>
                <p><strong>Now:</strong> This modal is optimized to 750px (more reasonable)</p>
                <p>The modal-lg class has been optimized to provide a better user experience while maintaining functionality.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Old XL Modal (Bootstrap modal-xl - now optimized) -->
<div class="modal fade" id="oldXLModal" tabindex="-1" aria-labelledby="oldXLModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="oldXLModalLabel">Old XL Modal (Now Optimized)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p><strong>Before:</strong> This modal would have been 1140px wide (extremely overwhelming)</p>
                <p><strong>Now:</strong> This modal is optimized to 900px (much more manageable)</p>
                <p>The modal-xl class has been significantly reduced to provide a better user experience.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Form Modal (Auto-optimized) -->
<div class="modal fade" id="formModal" tabindex="-1" aria-labelledby="formModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="formModalLabel">Form Modal - Auto-Optimized</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email">
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">Message</label>
                        <textarea class="form-control" id="message" rows="3"></textarea>
                    </div>
                </form>
                <p class="text-muted small">This modal should auto-detect as a form and apply optimal width (650px).</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal (Auto-optimized) -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this item?</p>
                <p class="text-muted small">This modal should auto-detect as a confirmation and be compact (450px).</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Medium Modal (Custom class) -->
<div class="modal fade" id="mediumModal" tabindex="-1" aria-labelledby="mediumModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-confirmationmodal-medium">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mediumModalLabel">Medium Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This modal uses the 'modal-medium' class (600px max-width).</p>
                <p>This is now the default size for most modals and provides a good balance between content space and user experience.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function testOptimization() {
    if (window.modalWidthManager) {
        const count = window.modalWidthManager.optimizeAllModals();
        alert(`✅ Modal optimization test complete!\n\nOptimized ${count} modals based on their content.\n\nThe system automatically detects:\n- Forms → 650px width\n- Confirmations → 450px width\n- Tables → 900px width\n- General content → 600px width`);
    } else {
        alert('❌ Modal Width Manager not found!\n\nPlease check that modal-width-manager.js is loading correctly.');
    }
}

function showWidthInfo() {
    let info = '📊 Current Modal Width Information:\n\n';
    
    info += '🔧 Bootstrap Class Optimizations:\n';
    info += '• modal-lg: 800px → 750px (6.25% smaller)\n';
    info += '• modal-xl: 1140px → 900px (21% smaller)\n';
    info += '• modal-sm: 300px → 400px (33% larger)\n';
    info += '• default: 500px → 600px (20% larger)\n\n';
    
    info += '🎯 Custom Width Classes:\n';
    info += '• modal-narrow: 400px (simple messages)\n';
    info += '• modal-medium: 600px (standard content)\n';
    info += '• modal-wide: 800px (complex forms)\n';
    info += '• modal-form: 650px (optimized for forms)\n';
    info += '• modal-confirmation: 450px (compact alerts)\n';
    info += '• modal-preview: 800px (content preview)\n';
    info += '• modal-table: 900px (data tables)\n';
    info += '• modal-editor: 850px (text editors)\n\n';
    
    info += '📱 Responsive Behavior:\n';
    info += '• Desktop: Full custom widths\n';
    info += '• Tablet: Reduced widths\n';
    info += '• Mobile: Full-width with margins\n\n';
    
    info += '🤖 Auto-Optimization:\n';
    info += '• Detects forms, tables, confirmations\n';
    info += '• Applies appropriate width automatically\n';
    info += '• Respects existing custom classes';
    
    alert(info);
}

// Log modal events for debugging
document.addEventListener('show.bs.modal', function(e) {
    const modalDialog = e.target.querySelector('.modal-dialog');
    const classes = Array.from(modalDialog.classList).filter(cls => cls.includes('modal'));
    console.log('🔍 Modal showing:', e.target.id, 'Classes:', classes);
});

// Show success message when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        console.log('✅ Modal Width Optimization System Loaded');
        console.log('📊 Test the modals above to see the width improvements');
        console.log('🔧 Use the JavaScript controls to test auto-optimization');
    }, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
