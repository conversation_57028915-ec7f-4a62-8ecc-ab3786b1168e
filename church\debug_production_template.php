<?php
require_once 'config.php';

echo "🔍 Debugging Production Template Usage\n";
echo "======================================\n\n";

// Check what template is being used for birthday notifications
$stmt = $pdo->query("SELECT * FROM email_templates ORDER BY id");
$templates = $stmt->fetchAll();

echo "📧 All Email Templates:\n";
echo "=======================\n";
foreach ($templates as $template) {
    $hasPlaceholder = strpos($template['content'], '{member_image}') !== false;
    echo "ID: " . $template['id'] . " - " . $template['template_name'] . "\n";
    echo "  Has {member_image}: " . ($hasPlaceholder ? 'YES' : 'NO') . "\n";
    echo "  Subject: " . substr($template['subject'], 0, 50) . "...\n";
    echo "  Content preview: " . substr(strip_tags($template['content']), 0, 100) . "...\n";
    echo "---\n";
}

echo "\n🎯 Templates with {member_image} placeholder:\n";
echo "==============================================\n";
foreach ($templates as $template) {
    if (strpos($template['content'], '{member_image}') !== false) {
        echo "✅ ID: " . $template['id'] . " - " . $template['template_name'] . "\n";
    }
}

echo "\n🔍 Checking birthday notification settings...\n";
echo "==============================================\n";

// Check if there are any settings that determine which template to use
$stmt = $pdo->query("SHOW TABLES LIKE '%setting%'");
$settingTables = $stmt->fetchAll();

if ($settingTables) {
    foreach ($settingTables as $table) {
        $tableName = $table[0];
        echo "📋 Table: " . $tableName . "\n";
        
        $stmt = $pdo->query("SELECT * FROM `$tableName` WHERE name LIKE '%birthday%' OR name LIKE '%template%' OR name LIKE '%notification%'");
        $settings = $stmt->fetchAll();
        
        if ($settings) {
            foreach ($settings as $setting) {
                echo "  - " . $setting['name'] . ": " . $setting['value'] . "\n";
            }
        } else {
            echo "  (No birthday/template related settings)\n";
        }
        echo "\n";
    }
} else {
    echo "No settings tables found\n";
}

echo "🔍 Checking send_birthday_reminders.php for template selection logic...\n";
echo "======================================================================\n";

// Look for template selection in the birthday reminder code
$birthdayFile = file_get_contents('send_birthday_reminders.php');

// Find template selection patterns
if (preg_match('/template.*=.*(\d+)/', $birthdayFile, $matches)) {
    echo "Found hardcoded template ID: " . $matches[1] . "\n";
} else {
    echo "No hardcoded template ID found\n";
}

if (preg_match('/getTemplate.*birthday/i', $birthdayFile, $matches)) {
    echo "Found template selection method: " . $matches[0] . "\n";
} else {
    echo "No template selection method found\n";
}

// Check for template name patterns
if (preg_match_all('/["\']([^"\']*birthday[^"\']*)["\']/', $birthdayFile, $matches)) {
    echo "Found birthday-related template names:\n";
    foreach (array_unique($matches[1]) as $name) {
        echo "  - " . $name . "\n";
    }
} else {
    echo "No birthday template names found\n";
}

echo "\n🎯 Recommendation:\n";
echo "==================\n";
echo "The production system appears to be using a different template than expected.\n";
echo "We need to either:\n";
echo "1. Update the template selection logic to use the correct template with {member_image}\n";
echo "2. Add {member_image} to the template that's actually being used\n";
echo "3. Check the birthday notification configuration\n";
?>
