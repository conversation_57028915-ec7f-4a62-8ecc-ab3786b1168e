<?php
require_once 'config.php';

echo "🔧 Adding Name Under Image in Template\n";
echo "======================================\n\n";

// Get template 58
$stmt = $pdo->prepare('SELECT * FROM email_templates WHERE id = 58');
$stmt->execute();
$template = $stmt->fetch();

if ($template) {
    echo "✅ Found template: " . $template['template_name'] . "\n\n";
    
    $content = $template['content'];
    
    // Look for the current structure and add the name
    echo "🔍 Looking for image section...\n";
    
    // Find the section with the image and birthday info
    if (strpos($content, '{member_image}') !== false) {
        echo "✅ Found {member_image} placeholder\n";
        
        // Look for the pattern where we need to add the name
        $pattern = '/(\{member_image\})\s*(<h2[^>]*>🎂 \{birthday_member_name\} 🎂<\/h2>)/';
        
        if (preg_match($pattern, $content)) {
            echo "✅ Found the h2 header after image\n";
            
            // Add the full name display before the h2 header
            $replacement = '$1' . "\n" . 
                          '                                <h3 style="color: #333; margin: 15px 0 5px 0; font-size: 20px; font-weight: bold;">{birthday_member_full_name}</h3>' . "\n" .
                          '                                $2';
            
            $newContent = preg_replace($pattern, $replacement, $content);
            
            if ($newContent !== $content) {
                echo "✅ Successfully added full name under image\n";
                
                // Update the template
                $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
                $result = $stmt->execute([$newContent]);
                
                if ($result) {
                    echo "✅ Template updated successfully!\n";
                } else {
                    echo "❌ Failed to update template\n";
                }
            } else {
                echo "❌ Pattern replacement failed\n";
            }
        } else {
            echo "❌ Could not find the h2 header pattern\n";
            echo "Let me try a different approach...\n";
            
            // Alternative approach - look for the birthday member photo section
            $imageSection = '<!-- Birthday Member Photo -->';
            if (strpos($content, $imageSection) !== false) {
                echo "✅ Found Birthday Member Photo section\n";
                
                // Find the div with the image and add the name
                $pattern = '/(<div style="margin: 30px 0;">)\s*(\{member_image\})\s*(<h2[^>]*>🎂 \{birthday_member_name\} 🎂<\/h2>)/';
                
                $replacement = '$1' . "\n" . 
                              '                                $2' . "\n" .
                              '                                <h3 style="color: #333; margin: 15px 0 5px 0; font-size: 20px; font-weight: bold;">{birthday_member_full_name}</h3>' . "\n" .
                              '                                $3';
                
                $newContent = preg_replace($pattern, $replacement, $content);
                
                if ($newContent !== $content) {
                    echo "✅ Successfully added full name under image (alternative method)\n";
                    
                    // Update the template
                    $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
                    $result = $stmt->execute([$newContent]);
                    
                    if ($result) {
                        echo "✅ Template updated successfully!\n";
                    } else {
                        echo "❌ Failed to update template\n";
                    }
                } else {
                    echo "❌ Alternative pattern replacement also failed\n";
                    echo "Let me show you the current structure...\n";
                    
                    // Show the relevant section
                    $start = strpos($content, '<!-- Birthday Member Photo -->');
                    $end = strpos($content, '<!-- Celebration Ideas -->', $start);
                    
                    if ($start !== false && $end !== false) {
                        $section = substr($content, $start, $end - $start);
                        echo "Current Birthday Member Photo section:\n";
                        echo "=====================================\n";
                        echo $section;
                        echo "\n=====================================\n";
                    }
                }
            }
        }
    } else {
        echo "❌ Could not find {member_image} placeholder\n";
    }
    
} else {
    echo "❌ Template 58 not found\n";
}

echo "\n🧪 Testing the updated template:\n";
echo "================================\n";

// Test the notification system
try {
    require_once 'send_birthday_reminders.php';
    $birthdayReminder = new BirthdayReminder($pdo);
    
    $stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
    $stmt->execute();
    $sandra = $stmt->fetch();
    
    if ($sandra) {
        $today = new DateTime();
        $birthDate = new DateTime($sandra['birth_date']);
        $thisYearBirthday = new DateTime($today->format('Y') . '-' . $birthDate->format('m-d'));
        
        if ($thisYearBirthday < $today) {
            $thisYearBirthday->add(new DateInterval('P1Y'));
        }
        
        $daysUntil = $today->diff($thisYearBirthday)->days;
        
        echo "Testing notification with name under image...\n";
        
        $result = $birthdayReminder->sendMemberBirthdayNotifications($sandra['id'], null, $daysUntil);
        
        if (isset($result['success']) && $result['success'] > 0) {
            echo "✅ Notifications sent successfully!\n";
            echo "The emails should now show 'Sandra Stern' under the image!\n";
        } else {
            echo "❌ Notification test failed\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n🎯 RESULT:\n";
echo "==========\n";
echo "The template has been updated to show 'Sandra Stern' under the image,\n";
echo "just before the birthday date information.\n";
echo "Check your email to see the full name displayed!\n";
?>
