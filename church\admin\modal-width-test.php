<?php
/**
 * Modal Width Test Page
 * Demonstrates the modal width optimization system
 */

// Security check
if (!defined('ADMIN_ACCESS')) {
    define('ADMIN_ACCESS', true);
}

// Include the configuration file
require_once '../config.php';

// Set page variables
$page_title = 'Modal Width Test';
$page_header = 'Modal Width Optimization Test';
$page_description = 'Test page to verify modal width optimization system';

// Include header
include 'includes/header.php';
?>

<!-- Test Instructions -->
<div class="alert alert-info mb-4">
    <h5><i class="bi bi-info-circle-fill me-2"></i>Modal Width Test Instructions</h5>
    <p>This page demonstrates the new modal width optimization system. Test the following:</p>
    <ul>
        <li><strong>Auto-optimization:</strong> Modals automatically adjust width based on content</li>
        <li><strong>Custom classes:</strong> Use specific width classes for different modal types</li>
        <li><strong>Responsive behavior:</strong> Modals adapt to different screen sizes</li>
        <li><strong>Manual control:</strong> JavaScript utilities for dynamic width changes</li>
    </ul>
</div>

<!-- Test Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Modal Width Test Controls</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Auto-Optimized Modals -->
                    <div class="col-md-6">
                        <h6>Auto-Optimized Modals</h6>
                        <div class="btn-group-vertical d-grid gap-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#formModal">
                                Form Modal (Auto: modal-form)
                            </button>
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#confirmModal">
                                Confirmation Modal (Auto: modal-confirmation)
                            </button>
                            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#tableModal">
                                Table Modal (Auto: modal-table)
                            </button>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#previewModal">
                                Preview Modal (Auto: modal-preview)
                            </button>
                        </div>
                    </div>
                    
                    <!-- Custom Width Classes -->
                    <div class="col-md-6">
                        <h6>Custom Width Classes</h6>
                        <div class="btn-group-vertical d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#narrowModal">
                                Narrow Modal (400px)
                            </button>
                            <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#mediumModal">
                                Medium Modal (600px)
                            </button>
                            <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#wideModal">
                                Wide Modal (800px)
                            </button>
                            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#oldLargeModal">
                                Old Large Modal (Bootstrap modal-lg)
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- JavaScript Controls -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6>JavaScript Controls</h6>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-info" onclick="optimizeAllModals()">
                                Optimize All Modals
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="resetAllModals()">
                                Reset All Modals
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="showModalStats()">
                                Show Modal Stats
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Form Modal (Auto-optimized) -->
<div class="modal fade" id="formModal" tabindex="-1" aria-labelledby="formModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="formModalLabel">Form Modal - Auto-Optimized</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="firstName" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="firstName">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lastName" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="lastName">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email">
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="phone">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <select class="form-control" id="category">
                            <option>Select Category</option>
                            <option>Member</option>
                            <option>Visitor</option>
                            <option>Staff</option>
                        </select>
                    </div>
                </form>
                <p class="text-muted small">This modal should auto-detect as a form and apply 'modal-form' class (650px max-width).</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal (Auto-optimized) -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this item? This action cannot be undone.</p>
                <p class="text-muted small">This modal should auto-detect as a confirmation and apply 'modal-confirmation' class (450px max-width).</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Table Modal (Auto-optimized) -->
<div class="modal fade" id="tableModal" tabindex="-1" aria-labelledby="tableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tableModalLabel">Data Table Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td>************</td>
                            <td><span class="badge bg-success">Active</span></td>
                        </tr>
                        <tr>
                            <td>Jane Smith</td>
                            <td><EMAIL></td>
                            <td>************</td>
                            <td><span class="badge bg-warning">Pending</span></td>
                        </tr>
                    </tbody>
                </table>
                <p class="text-muted small">This modal should auto-detect as a table and apply 'modal-table' class (900px max-width).</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal (Auto-optimized) -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Content Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="preview">
                    <img src="https://via.placeholder.com/400x200" class="img-fluid mb-3" alt="Preview Image">
                    <h6>Sample Content</h6>
                    <p>This is a preview of some content that might be displayed in a modal. The modal width manager should detect this as preview content.</p>
                </div>
                <p class="text-muted small">This modal should auto-detect as a preview and apply 'modal-preview' class (800px max-width).</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Narrow Modal (Custom class) -->
<div class="modal fade" id="narrowModal" tabindex="-1" aria-labelledby="narrowModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-widemodal-narrow">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="narrowModalLabel">Narrow Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is a narrow modal with the 'modal-narrow' class (400px max-width).</p>
                <p>Perfect for simple messages or small forms.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Medium Modal (Custom class) -->
<div class="modal fade" id="mediumModal" tabindex="-1" aria-labelledby="mediumModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-formmodal-medium">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mediumModalLabel">Medium Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is a medium modal with the 'modal-medium' class (600px max-width).</p>
                <p>Good for most forms and content displays. This is now the default size for regular modals.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Wide Modal (Custom class) -->
<div class="modal fade" id="wideModal" tabindex="-1" aria-labelledby="wideModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="wideModalLabel">Wide Modal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is a wide modal with the 'modal-wide' class (800px max-width).</p>
                <p>Suitable for content that needs more horizontal space, like detailed forms or data displays.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Old Large Modal (Bootstrap modal-lg) -->
<div class="modal fade" id="oldLargeModal" tabindex="-1" aria-labelledby="oldLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="oldLargeModalLabel">Old Large Modal (Optimized)</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This modal uses the Bootstrap 'modal-lg' class, but it's now optimized to 750px instead of the default 800px.</p>
                <p>Compare this to the old behavior where it would be much wider and potentially overwhelming.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// Test functions
function optimizeAllModals() {
    if (window.modalWidthManager) {
        const count = window.modalWidthManager.optimizeAllModals();
        alert(`Optimized ${count} modals based on their content.`);
    } else {
        alert('Modal Width Manager not found!');
    }
}

function resetAllModals() {
    if (window.modalWidthManager) {
        const count = window.modalWidthManager.resetAllModals();
        alert(`Reset ${count} modals to default Bootstrap sizes.`);
    } else {
        alert('Modal Width Manager not found!');
    }
}

function showModalStats() {
    if (window.modalWidthManager) {
        const modals = document.querySelectorAll('.modal');
        let stats = 'Modal Width Statistics:\n\n';
        
        modals.forEach((modal, index) => {
            const width = window.modalWidthManager.getModalWidth(modal.id);
            stats += `${modal.id || 'Modal ' + (index + 1)}: ${width}\n`;
        });
        
        alert(stats);
    } else {
        alert('Modal Width Manager not found!');
    }
}

// Log modal events for debugging
document.addEventListener('show.bs.modal', function(e) {
    const modalDialog = e.target.querySelector('.modal-dialog');
    const classes = Array.from(modalDialog.classList).filter(cls => cls.includes('modal'));
    console.log('Modal showing:', e.target.id, 'Classes:', classes);
});
</script>

<?php include 'includes/footer.php'; ?>
