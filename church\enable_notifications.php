<?php
require_once 'config.php';

echo "🔧 Enabling Birthday Notifications\n";
echo "===================================\n\n";

// Enable notification settings
$stmt = $pdo->prepare('UPDATE automated_emails_settings SET enabled = 1 WHERE email_type = "notification"');
$result = $stmt->execute();

if ($result) {
    echo "✅ Notification settings enabled successfully!\n\n";
} else {
    echo "❌ Failed to enable notification settings\n\n";
}

// Verify the change
$stmt = $pdo->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'notification'");
$stmt->execute();
$settings = $stmt->fetch();

if ($settings) {
    echo "📋 Current notification settings:\n";
    echo "  - Email type: " . $settings['email_type'] . "\n";
    echo "  - Days before: " . $settings['days_before'] . "\n";
    echo "  - Template IDs: " . $settings['template_ids'] . "\n";
    echo "  - Enabled: " . ($settings['enabled'] ? 'YES' : 'NO') . "\n";
    echo "  - Send time: " . $settings['send_time'] . "\n\n";
    
    if ($settings['enabled']) {
        echo "🎉 SUCCESS! Notifications are now ENABLED!\n\n";
        
        echo "✅ Now try the admin interface:\n";
        echo "1. Go to: http://localhost/campaign/church/admin/send_birthday_notification.php\n";
        echo "2. Log in with: admin / admin123\n";
        echo "3. Look for Sandra's upcoming birthday (July 21st)\n";
        echo "4. Try sending notifications to other members\n";
        echo "5. It should now work without the 'No notification emails were sent' error!\n";
    } else {
        echo "❌ Notifications are still disabled\n";
    }
} else {
    echo "❌ Could not find notification settings\n";
}
?>
