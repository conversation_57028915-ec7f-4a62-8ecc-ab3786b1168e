<?php
require_once 'config.php';

echo "🔍 Testing Admin-Specific Birthday Function\n";
echo "===========================================\n\n";

echo "📅 Current Date: " . date('Y-m-d H:i:s') . "\n\n";

// Copy the exact function from admin/send_birthday_notification.php
function getUpcomingBirthdays($pdo, $daysToCheck = 14) {
    $results = [];
    $today = date('Y-m-d');
    
    for ($day = 0; $day < $daysToCheck; $day++) {
        $checkDate = date('Y-m-d', strtotime("+$day days"));
        $month = date('m', strtotime($checkDate));
        $day_of_month = date('d', strtotime($checkDate));
        
        // Find members with birthdays on this date
        $stmt = $pdo->prepare("
            SELECT *, 
                   DATE_FORMAT(birth_date, '%m-%d') as birth_day, 
                   DATE_FORMAT(?, '%m-%d') as check_day,
                   ? as days_until_birthday
            FROM members 
            WHERE DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(?, '%m-%d')
            ORDER BY full_name
        ");
        $stmt->execute([$checkDate, $day, $checkDate]);
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($members)) {
            $results[] = [
                'date' => $checkDate,
                'formatted_date' => date('F j, Y', strtotime($checkDate)),
                'days_until' => $day,
                'members' => $members
            ];
        }
    }
    
    return $results;
}

echo "🎯 Testing admin getUpcomingBirthdays() function:\n";
echo "================================================\n";

// Test the admin function for 7 days
$upcomingBirthdays = getUpcomingBirthdays($pdo, 7);

echo "Results from admin function (7 days):\n";
if (empty($upcomingBirthdays)) {
    echo "❌ NO BIRTHDAYS FOUND!\n";
    echo "This would cause the 'no recipient within 7 days' error in the admin interface.\n\n";
    
    echo "🔍 Debugging the admin function:\n";
    echo "================================\n";
    
    // Test each day individually
    for ($day = 0; $day < 7; $day++) {
        $checkDate = date('Y-m-d', strtotime("+$day days"));
        echo "Day +$day ($checkDate): ";
        
        $stmt = $pdo->prepare("
            SELECT *, 
                   DATE_FORMAT(birth_date, '%m-%d') as birth_day, 
                   DATE_FORMAT(?, '%m-%d') as check_day
            FROM members 
            WHERE DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(?, '%m-%d')
        ");
        $stmt->execute([$checkDate, $checkDate]);
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo count($members) . " members\n";
        
        foreach ($members as $member) {
            echo "  - " . $member['full_name'] . " (birth_day: " . $member['birth_day'] . ", check_day: " . $member['check_day'] . ")\n";
            echo "    Email: " . ($member['email'] ?? 'none') . "\n";
            echo "    Status: " . ($member['status'] ?? 'none') . "\n";
        }
        
        if ($day == 2 && count($members) > 0) {
            echo "  ✅ Found members for July 21st using admin function!\n";
        }
        echo "\n";
    }
    
} else {
    echo "✅ Found " . count($upcomingBirthdays) . " birthday groups:\n\n";
    
    foreach ($upcomingBirthdays as $group) {
        echo "📅 " . $group['formatted_date'] . " (in " . $group['days_until'] . " days):\n";
        foreach ($group['members'] as $member) {
            echo "  - " . $member['full_name'] . " (ID: " . $member['id'] . ")\n";
            echo "    Email: " . ($member['email'] ?? 'none') . "\n";
            echo "    Status: " . ($member['status'] ?? 'none') . "\n";
        }
        echo "\n";
    }
}

echo "🔍 Testing specific date format matching:\n";
echo "=========================================\n";

// Test Sandra specifically
$stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE '%Sandra%'");
$stmt->execute();
$sandra = $stmt->fetch();

if ($sandra) {
    echo "Sandra's details:\n";
    echo "  Birth date: " . $sandra['birth_date'] . "\n";
    echo "  DATE_FORMAT(birth_date, '%m-%d'): ";
    
    $stmt = $pdo->prepare("SELECT DATE_FORMAT(birth_date, '%m-%d') as birth_day FROM members WHERE id = ?");
    $stmt->execute([$sandra['id']]);
    $result = $stmt->fetch();
    echo $result['birth_day'] . "\n";
    
    // Test July 21st specifically
    $july21 = '2025-07-21';
    echo "  DATE_FORMAT('$july21', '%m-%d'): ";
    
    $stmt = $pdo->prepare("SELECT DATE_FORMAT(?, '%m-%d') as check_day");
    $stmt->execute([$july21]);
    $result = $stmt->fetch();
    echo $result['check_day'] . "\n";
    
    // Test if they match
    $stmt = $pdo->prepare("
        SELECT * FROM members 
        WHERE id = ? 
        AND DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(?, '%m-%d')
    ");
    $stmt->execute([$sandra['id'], $july21]);
    $match = $stmt->fetch();
    
    echo "  Match for July 21st: " . ($match ? 'YES' : 'NO') . "\n";
    
    if (!$match) {
        echo "  ❌ This is why Sandra is not found by the admin function!\n";
        
        // Check what the actual comparison values are
        $stmt = $pdo->prepare("
            SELECT 
                birth_date,
                DATE_FORMAT(birth_date, '%m-%d') as birth_day,
                DATE_FORMAT(?, '%m-%d') as check_day,
                (DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(?, '%m-%d')) as matches
            FROM members 
            WHERE id = ?
        ");
        $stmt->execute([$july21, $july21, $sandra['id']]);
        $debug = $stmt->fetch();
        
        echo "  Debug comparison:\n";
        echo "    birth_date: " . $debug['birth_date'] . "\n";
        echo "    birth_day: " . $debug['birth_day'] . "\n";
        echo "    check_day: " . $debug['check_day'] . "\n";
        echo "    matches: " . $debug['matches'] . "\n";
    }
} else {
    echo "❌ Sandra not found\n";
}

echo "\n🎯 Solution:\n";
echo "=============\n";
echo "If the admin function is not finding Sandra, there might be:\n";
echo "1. A date format issue in the SQL query\n";
echo "2. A timezone or date calculation problem\n";
echo "3. A difference between the admin function and the main birthday system\n";
echo "\nThe admin interface uses a different birthday detection logic than\n";
echo "the main BirthdayReminder class, which could explain the discrepancy.\n";
?>
