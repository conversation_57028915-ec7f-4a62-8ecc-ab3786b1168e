
[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: AKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigA

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: ooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAorx7WP2kraz8

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: ceOfC2keA/F3iu/8GRW02rSaLFZMqrPb+fEIkluo5JWKhhtRC25cAHK5PiB+1F4W8D/CSx+J1nYa

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: p4v8C3MSzPq/h/7My26tKkSCSOeeKTcZJNhVVZkZHDhCKdmYOvTSbvt+m/3dex7DRXk/ir9oKPwh

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: rPgXQrzwL4ol8T+MPt32DQ4G04zxfZFDy+bIbsQjKMHXbI2RwcN8tGh/tBR+JL7xNo+neBfFFx4r

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 8NXdrb6r4c3act1bR3EBmhn8xrsW7xsoIwkxcHqgHNFmHtoXtfX0fa9vW2tj1iivnu1/bR8P3HwW

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: vPivJ4J8XWvgWCISR6jKmnlrlvtiWnlpEt4ZAwkZj86qu2NjnO0N2vxh/aF8OfBLVtF0/XLLVLub

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: VtP1bUoG0+KN1WPTrX7VOG3yLhmQYQAEFupUc0WYvrFJx5r6afjt956fRXj3hb9prRtdn8GnVfDX

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: iHwfpnjKJH0DV9cW0+x30kiLJDb74LiXyppEYsiShC+xlXLDbVn/AIad8D/8LU/4Qbz73zftf9lf

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 2/8AZT/Y/wDauN39l/a87ftez5vL6Z+Td5nyUWY/b07X5j1iivJ5f2mPCNl4c+Jur6il7o//AAr6

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 7mtdVsdSEMF1LtQNDJDG0nzR3O4CBnKeaeBXo3hrWv8AhJPDmlav9gvdL+32kV19h1KHybq23oG8

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: uaPJ2SLnDLk4IIpWLjUjN2izSooooNAooooAKKKKACiiigAooooAKKKKACiiigAooooA+IvGvgjT

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: pvjv+0fP4y0D4kvo+v6fpcGlSeDbLViuohdMaOdFNsv2eVgzKgFzmMMWB4L1pftA6P46l/YHtPAv

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: iDw3Pf8AxGvtPsoBpXg7RJ7m2jFveW77G+zRNDAywKuQCsZdZBFlQAPsuiq5jz3hFaaT+K/4u/z8

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: j5O/aE8C33x++MXwButGk8a+HtBH/CQfbNf0e0utKvtM/wBHjEe9poQ1v5jxFB5ijerHbkMDXa/s

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: teFb7wFfeOvDWv6Bev4rsLu1F/47uPtU0fiuIwYtp1nuXd/MjjXZJArtFE5bYcOQPfKKV9LGscOl

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: U9rfV/5Jfp+h8Ef8K18Xf8OqP+ES/wCEV1v/AISv/oBf2dN9u/5Dnm/6jbv/ANX8/T7vPTmtr9rP

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 4FahaeMvBs/hHSvF3iKE+GvGMF3JcajqWtLBJLo7x26AzyS+U0jsUULtMjYHzEDH27RT5mYvBQcF

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: FvZRX/gLv+J8aWmj6z8UfAP7NHgfSvDfiGw1PwdqGh65r95rmiXem2djHp9sEmh82eJPNmd5AqLE

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: HB2sWKqN1eX/APCp/i5/w7Z/4Rv7D/3J/wDwit3/AG5/yGPM+/8AaP8Atr/x7f6vj/br9G6KOYTw

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: UZX5pbpr77L9D5y+N3w1/wCEk/a2/Z98QL4V/tSysP7Z/tPVBp3nR22y2V7LzpdpCbZi7RbiMOSV

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: +bNfRtFFTe52QpqEpSX2nf8ABL9AooooNgooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKA

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: M3xFoFr4o0a40y8lvYbafbvfTr+eynG1gw2zQOkicgZ2sMjIOQSD8aeAoNQPwe/aZ8RyeKfF0+se

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: Edb8VaXok9x4q1KVbO3gs/3CiNrgo7IXYq7qzhsNuyoI+3a8w0j9nrw5ovgv4l+GIL3VGsPH+oan

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: qWqSSSxmWGS+jEcwgIjAVQB8oYOQepaqTscdek6kk0uj/wCAcD+yrps+ueEvA+v6povxCg1KTw/a

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 30mua94vlvNO1CaS3QOy2o1GX7/mNIokgULjOEYKK848rxD8KPhj/bPxlm+KGl69Yar5mq/EDw74

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: pjm0uPfqWIZo9Pa7ZTblHiQw/YD8hYeVmvo34cfB+8+GtroWnWvxC8UapoOjWiWNto2ow6Z5BhSL

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: y41Z4rNJjtAUg+YCSo3EjIPOeIv2XbHxf4YuPCmvePvGut+C7i7W4m8P6jeWtwksa3IuFga7e3N4

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: YwyqoJuN4UAb+Kd9TndCXs0ktUn2tfTV/wCe5wPxn8LCx/ak+C2h2niHxdZ6P4ul1+bWbK28W6pF

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: FO0Nos0QRVuB5Kq7EhYti44xgAV9P6bp8Wk6da2MDTvDbRJCjXM7zylVAALySFndsDlmJYnkkk5r

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: k/FXwm0jxf8AEnwL43vLm9i1Xwf9u+wQwOggl+1wiGXzQULHCqCu1lweueldrUt3OulT5JTdt3p6

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: WX63CiiikdIUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRR

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: RQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFF

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: ABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUA

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: FFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: UUUAf//Z

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: --b2=_TXEAm86z4vd3bFM96ylJPq7ixOAunI3BqFImUGhHA--

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: --b1=_TXEAm86z4vd3bFM96ylJPq7ixOAunI3BqFImUGhHA--

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 07:50:42] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbNx294dz2T22l

[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 07:50:42] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 07:50:42] Email sent <NAME_EMAIL>
[2025-07-19 07:50:42] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 07:50:42] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye


================================================================================
[2025-07-19 08:00:00] SYSTEMATIC FIX TEST: Starting comprehensive test
================================================================================

[2025-07-19 08:00:01] Global sendEmail called for Test User <<EMAIL>>
[2025-07-19 08:00:01] Initial email body length: 378 characters
[2025-07-19 08:00:01] Birthday notification detected via explicit flag
[2025-07-19 08:00:01] Birthday member data extracted: Array
(
    [name] => Jane Test Birthday
    [email] => 
    [image_path] => uploads/members/test_birthday.jpg
    [photo_url] => http://localhost/campaign/church/uploads/members/test_birthday.jpg
)

[2025-07-19 08:00:01] Email type: Birthday Notification
[2025-07-19 08:00:01] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:00:01] Set Reply-To address: <EMAIL>
[2025-07-19 08:00:01] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:00:01] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:00:01] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:00:01] SYSTEMATIC FIX: Found 1 images referenced in HTML
[2025-07-19 08:00:01] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:01] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:00:01] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:01] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_8c56f51ebe9a1465dc204e0822101056, mime=image/jpeg
[2025-07-19 08:00:01] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:00:01] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/members/test_birthday.jpg as CID: birthday_image_8c56f51ebe9a1465dc204e0822101056
[2025-07-19 08:00:01] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:00:01] SYSTEMATIC FIX: SUCCESS - All original URLs replaced with CIDs
[2025-07-19 08:00:01] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:00:01] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:00:01]   - Email type: Birthday Notification
[2025-07-19 08:00:01]   - Images found in HTML: 1
[2025-07-19 08:00:01]   - Images successfully embedded: 1
[2025-07-19 08:00:01]   - Processed image URLs: 1
[2025-07-19 08:00:01]   - Processed image paths: 1
[2025-07-19 08:00:01] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:00:01]   - URL: http://localhost/campaign/church/uploads/members/test_birthday.jpg -> CID: birthday_image_8c56f51ebe9a1465dc204e0822101056
[2025-07-19 08:00:01]   - Embedded images: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:01] Set HTML body (363 chars) and plain text alternative (77 chars)
[2025-07-19 08:00:01] Attempting to send email to: <EMAIL> with subject: 🎉 Test 1: Single Image Birthday
[2025-07-19 08:00:01] Email content validation: Body length=363, HTML=Yes
[2025-07-19 08:00:01] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:00:01] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:00:02] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:00:02] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:00:02] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:00:02] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:00:02] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:00:02] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:00:02] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:00:02] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:00:02] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:00:02] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:00:03] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:00:03] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:00:01 +0200

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: To: Test User <<EMAIL>>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Subject: =?UTF-8?Q?=F0=9F=8E=89_Test_1:_Single_Image_Birthday?=

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_6Wt1VrONJ1naGYL1shhecu1h3Kwef8bcSVMdebaMgk"

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: --b1=_6Wt1VrONJ1naGYL1shhecu1h3Kwef8bcSVMdebaMgk

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=us-ascii

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Birthday Test Happy Birthday Jane! Join us in celebrating Jane's special day!

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: --b1=_6Wt1VrONJ1naGYL1shhecu1h3Kwef8bcSVMdebaMgk

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_6Wt1VrONJ1naGYL1shhecu1h3Kwef8bcSVMdebaMgk";

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: --b2=_6Wt1VrONJ1naGYL1shhecu1h3Kwef8bcSVMdebaMgk

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=us-ascii

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: <html>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: <head><title>Birthday Test</title></head>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER:     <h1>Happy Birthday Jane!</h1>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center;">

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER:         <img src="cid:birthday_image_8c56f51ebe9a1465dc204e0822101056" alt="Jane Test Birthday" style="width: 150px; height: 150px; border-radius: 50%;">

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER:     <p>Join us in celebrating Jane's special day!</p>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: </body>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: --b2=_6Wt1VrONJ1naGYL1shhecu1h3Kwef8bcSVMdebaMgk

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/jpeg; name=

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_8c56f51ebe9a1465dc204e0822101056>

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: /9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcg

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: SlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwK

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: DAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQU

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: FBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAyADI

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: AwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMF

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: BQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkq

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: NDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqi

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: o6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/E

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: AB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMR

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: BAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVG

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: R0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKz

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: tLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: +4KKKK4j9TCiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACi

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: iigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKK

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: KACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooo

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: AKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigA

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: ooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAorx7WP2kraz8

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: ceOfC2keA/F3iu/8GRW02rSaLFZMqrPb+fEIkluo5JWKhhtRC25cAHK5PiB+1F4W8D/CSx+J1nYa

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: p4v8C3MSzPq/h/7My26tKkSCSOeeKTcZJNhVVZkZHDhCKdmYOvTSbvt+m/3dex7DRXk/ir9oKPwh

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: rPgXQrzwL4ol8T+MPt32DQ4G04zxfZFDy+bIbsQjKMHXbI2RwcN8tGh/tBR+JL7xNo+neBfFFx4r

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 8NXdrb6r4c3act1bR3EBmhn8xrsW7xsoIwkxcHqgHNFmHtoXtfX0fa9vW2tj1iivnu1/bR8P3HwW

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: vPivJ4J8XWvgWCISR6jKmnlrlvtiWnlpEt4ZAwkZj86qu2NjnO0N2vxh/aF8OfBLVtF0/XLLVLub

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: VtP1bUoG0+KN1WPTrX7VOG3yLhmQYQAEFupUc0WYvrFJx5r6afjt956fRXj3hb9prRtdn8GnVfDX

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: iHwfpnjKJH0DV9cW0+x30kiLJDb74LiXyppEYsiShC+xlXLDbVn/AIad8D/8LU/4Qbz73zftf9lf

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 2/8AZT/Y/wDauN39l/a87ftez5vL6Z+Td5nyUWY/b07X5j1iivJ5f2mPCNl4c+Jur6il7o//AAr6

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 7mtdVsdSEMF1LtQNDJDG0nzR3O4CBnKeaeBXo3hrWv8AhJPDmlav9gvdL+32kV19h1KHybq23oG8

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: uaPJ2SLnDLk4IIpWLjUjN2izSooooNAooooAKKKKACiiigAooooAKKKKACiiigAooooA+IvGvgjT

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: pvjv+0fP4y0D4kvo+v6fpcGlSeDbLViuohdMaOdFNsv2eVgzKgFzmMMWB4L1pftA6P46l/YHtPAv

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: iDw3Pf8AxGvtPsoBpXg7RJ7m2jFveW77G+zRNDAywKuQCsZdZBFlQAPsuiq5jz3hFaaT+K/4u/z8

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: j5O/aE8C33x++MXwButGk8a+HtBH/CQfbNf0e0utKvtM/wBHjEe9poQ1v5jxFB5ijerHbkMDXa/s

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: teFb7wFfeOvDWv6Bev4rsLu1F/47uPtU0fiuIwYtp1nuXd/MjjXZJArtFE5bYcOQPfKKV9LGscOl

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: U9rfV/5Jfp+h8Ef8K18Xf8OqP+ES/wCEV1v/AISv/oBf2dN9u/5Dnm/6jbv/ANX8/T7vPTmtr9rP

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 4FahaeMvBs/hHSvF3iKE+GvGMF3JcajqWtLBJLo7x26AzyS+U0jsUULtMjYHzEDH27RT5mYvBQcF

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: FvZRX/gLv+J8aWmj6z8UfAP7NHgfSvDfiGw1PwdqGh65r95rmiXem2djHp9sEmh82eJPNmd5AqLE

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: HB2sWKqN1eX/APCp/i5/w7Z/4Rv7D/3J/wDwit3/AG5/yGPM+/8AaP8Atr/x7f6vj/br9G6KOYTw

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: UZX5pbpr77L9D5y+N3w1/wCEk/a2/Z98QL4V/tSysP7Z/tPVBp3nR22y2V7LzpdpCbZi7RbiMOSV

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: +bNfRtFFTe52QpqEpSX2nf8ABL9AooooNgooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKA

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: M3xFoFr4o0a40y8lvYbafbvfTr+eynG1gw2zQOkicgZ2sMjIOQSD8aeAoNQPwe/aZ8RyeKfF0+se

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: Edb8VaXok9x4q1KVbO3gs/3CiNrgo7IXYq7qzhsNuyoI+3a8w0j9nrw5ovgv4l+GIL3VGsPH+oan

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: qWqSSSxmWGS+jEcwgIjAVQB8oYOQepaqTscdek6kk0uj/wCAcD+yrps+ueEvA+v6povxCg1KTw/a

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 30mua94vlvNO1CaS3QOy2o1GX7/mNIokgULjOEYKK848rxD8KPhj/bPxlm+KGl69Yar5mq/EDw74

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: pjm0uPfqWIZo9Pa7ZTblHiQw/YD8hYeVmvo34cfB+8+GtroWnWvxC8UapoOjWiWNto2ow6Z5BhSL

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: y41Z4rNJjtAUg+YCSo3EjIPOeIv2XbHxf4YuPCmvePvGut+C7i7W4m8P6jeWtwksa3IuFga7e3N4

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: YwyqoJuN4UAb+Kd9TndCXs0ktUn2tfTV/wCe5wPxn8LCx/ak+C2h2niHxdZ6P4ul1+bWbK28W6pF

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: FO0Nos0QRVuB5Kq7EhYti44xgAV9P6bp8Wk6da2MDTvDbRJCjXM7zylVAALySFndsDlmJYnkkk5r

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: k/FXwm0jxf8AEnwL43vLm9i1Xwf9u+wQwOggl+1wiGXzQULHCqCu1lweueldrUt3OulT5JTdt3p6

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: WX63CiiikdIUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRR

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: RQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFF

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: ABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUA

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: FFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: UUUAf//Z

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: --b2=_6Wt1VrONJ1naGYL1shhecu1h3Kwef8bcSVMdebaMgk--

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: --b1=_6Wt1VrONJ1naGYL1shhecu1h3Kwef8bcSVMdebaMgk--

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:00:03] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbbk2pXDz5Z5qk

[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:00:03] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:00:03] Email sent <NAME_EMAIL>
[2025-07-19 08:00:03] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:00:03] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:00:03] Global sendEmail called for Test User <<EMAIL>>
[2025-07-19 08:00:03] Initial email body length: 700 characters
[2025-07-19 08:00:03] Birthday notification detected via explicit flag
[2025-07-19 08:00:03] Birthday member data extracted: Array
(
    [name] => John Multi Test
    [email] => 
    [image_path] => uploads/members/test_birthday.jpg
    [photo_url] => http://localhost/campaign/church/uploads/members/test_birthday.jpg
)

[2025-07-19 08:00:03] Email type: Birthday Notification
[2025-07-19 08:00:03] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:00:03] Set Reply-To address: <EMAIL>
[2025-07-19 08:00:03] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:00:03] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:00:03] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:00:03] SYSTEMATIC FIX: Found 1 images referenced in HTML
[2025-07-19 08:00:03] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:03] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:00:03] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:03] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_b13d91093256122ffdced0bff66b0b7d, mime=image/jpeg
[2025-07-19 08:00:03] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:00:03] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/members/test_birthday.jpg as CID: birthday_image_b13d91093256122ffdced0bff66b0b7d
[2025-07-19 08:00:03] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:00:03] SYSTEMATIC FIX: SUCCESS - All original URLs replaced with CIDs
[2025-07-19 08:00:03] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:00:03] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:00:03]   - Email type: Birthday Notification
[2025-07-19 08:00:03]   - Images found in HTML: 1
[2025-07-19 08:00:03]   - Images successfully embedded: 1
[2025-07-19 08:00:03]   - Processed image URLs: 1
[2025-07-19 08:00:03]   - Processed image paths: 1
[2025-07-19 08:00:03] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:00:03]   - URL: http://localhost/campaign/church/uploads/members/test_birthday.jpg -> CID: birthday_image_b13d91093256122ffdced0bff66b0b7d
[2025-07-19 08:00:03]   - Embedded images: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:03] Set HTML body (685 chars) and plain text alternative (184 chars)
[2025-07-19 08:00:03] Attempting to send email to: <EMAIL> with subject: 🔍 Test 2: Multiple Images
[2025-07-19 08:00:03] Email content validation: Body length=685, HTML=Yes
[2025-07-19 08:00:04] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:00:04] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:00:04] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:00:04] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:00:04] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:00:04] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:00:05] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:00:05] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:00:05] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:00:05] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:00:05] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:00:05] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:00:05] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:00:05] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:00:06] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:00:03 +0200

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: To: Test User <<EMAIL>>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Subject: =?UTF-8?Q?=F0=9F=94=8D_Test_2:_Multiple_Images?=

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_JP7l45Y0UI4Qm3DHgFkW1fbQjUAcpkAfxyhXAiGVt4"

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: --b1=_JP7l45Y0UI4Qm3DHgFkW1fbQjUAcpkAfxyhXAiGVt4

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=us-ascii

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Multi Image Test Multi Image Test This email has multiple potential images, but only one is referenced in HTML: Only the image above should be embedded, not any others from memberData.

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: --b1=_JP7l45Y0UI4Qm3DHgFkW1fbQjUAcpkAfxyhXAiGVt4

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_JP7l45Y0UI4Qm3DHgFkW1fbQjUAcpkAfxyhXAiGVt4";

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: --b2=_JP7l45Y0UI4Qm3DHgFkW1fbQjUAcpkAfxyhXAiGVt4

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=us-ascii

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: <html>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: <head><title>Multi Image Test</title></head>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     <h1>Multi Image Test</h1>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     <p>This email has multiple potential images, but only one is referenced in HTML:</p>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     <!-- This image IS referenced and SHOULD be embedded -->

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center;">

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:         <img src="cid:birthday_image_b13d91093256122ffdced0bff66b0b7d" alt="Referenced Image" style="width: 100px; height: 100px;">

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     <!-- This image is NOT referenced in HTML and should NOT be embedded -->

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     <!-- birthday_member_photo_url is available but not used in HTML -->

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER:     <p>Only the image above should be embedded, not any others from memberData.</p>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: </body>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: --b2=_JP7l45Y0UI4Qm3DHgFkW1fbQjUAcpkAfxyhXAiGVt4

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/jpeg; name=

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_b13d91093256122ffdced0bff66b0b7d>

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: /9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcg

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: SlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwK

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: DAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQU

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: FBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAyADI

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: AwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMF

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: BQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkq

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: NDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqi

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: o6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/E

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: AB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMR

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: BAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVG

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: R0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKz

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: tLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: +4KKKK4j9TCiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACi

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: iigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKK

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: KACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooo

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: AKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigA

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: ooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAorx7WP2kraz8

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: ceOfC2keA/F3iu/8GRW02rSaLFZMqrPb+fEIkluo5JWKhhtRC25cAHK5PiB+1F4W8D/CSx+J1nYa

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: p4v8C3MSzPq/h/7My26tKkSCSOeeKTcZJNhVVZkZHDhCKdmYOvTSbvt+m/3dex7DRXk/ir9oKPwh

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: rPgXQrzwL4ol8T+MPt32DQ4G04zxfZFDy+bIbsQjKMHXbI2RwcN8tGh/tBR+JL7xNo+neBfFFx4r

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 8NXdrb6r4c3act1bR3EBmhn8xrsW7xsoIwkxcHqgHNFmHtoXtfX0fa9vW2tj1iivnu1/bR8P3HwW

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: vPivJ4J8XWvgWCISR6jKmnlrlvtiWnlpEt4ZAwkZj86qu2NjnO0N2vxh/aF8OfBLVtF0/XLLVLub

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: VtP1bUoG0+KN1WPTrX7VOG3yLhmQYQAEFupUc0WYvrFJx5r6afjt956fRXj3hb9prRtdn8GnVfDX

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: iHwfpnjKJH0DV9cW0+x30kiLJDb74LiXyppEYsiShC+xlXLDbVn/AIad8D/8LU/4Qbz73zftf9lf

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 2/8AZT/Y/wDauN39l/a87ftez5vL6Z+Td5nyUWY/b07X5j1iivJ5f2mPCNl4c+Jur6il7o//AAr6

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 7mtdVsdSEMF1LtQNDJDG0nzR3O4CBnKeaeBXo3hrWv8AhJPDmlav9gvdL+32kV19h1KHybq23oG8

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: uaPJ2SLnDLk4IIpWLjUjN2izSooooNAooooAKKKKACiiigAooooAKKKKACiiigAooooA+IvGvgjT

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: pvjv+0fP4y0D4kvo+v6fpcGlSeDbLViuohdMaOdFNsv2eVgzKgFzmMMWB4L1pftA6P46l/YHtPAv

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: iDw3Pf8AxGvtPsoBpXg7RJ7m2jFveW77G+zRNDAywKuQCsZdZBFlQAPsuiq5jz3hFaaT+K/4u/z8

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: j5O/aE8C33x++MXwButGk8a+HtBH/CQfbNf0e0utKvtM/wBHjEe9poQ1v5jxFB5ijerHbkMDXa/s

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: teFb7wFfeOvDWv6Bev4rsLu1F/47uPtU0fiuIwYtp1nuXd/MjjXZJArtFE5bYcOQPfKKV9LGscOl

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: U9rfV/5Jfp+h8Ef8K18Xf8OqP+ES/wCEV1v/AISv/oBf2dN9u/5Dnm/6jbv/ANX8/T7vPTmtr9rP

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 4FahaeMvBs/hHSvF3iKE+GvGMF3JcajqWtLBJLo7x26AzyS+U0jsUULtMjYHzEDH27RT5mYvBQcF

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: FvZRX/gLv+J8aWmj6z8UfAP7NHgfSvDfiGw1PwdqGh65r95rmiXem2djHp9sEmh82eJPNmd5AqLE

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: HB2sWKqN1eX/APCp/i5/w7Z/4Rv7D/3J/wDwit3/AG5/yGPM+/8AaP8Atr/x7f6vj/br9G6KOYTw

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: UZX5pbpr77L9D5y+N3w1/wCEk/a2/Z98QL4V/tSysP7Z/tPVBp3nR22y2V7LzpdpCbZi7RbiMOSV

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: +bNfRtFFTe52QpqEpSX2nf8ABL9AooooNgooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKA

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: M3xFoFr4o0a40y8lvYbafbvfTr+eynG1gw2zQOkicgZ2sMjIOQSD8aeAoNQPwe/aZ8RyeKfF0+se

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: Edb8VaXok9x4q1KVbO3gs/3CiNrgo7IXYq7qzhsNuyoI+3a8w0j9nrw5ovgv4l+GIL3VGsPH+oan

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: qWqSSSxmWGS+jEcwgIjAVQB8oYOQepaqTscdek6kk0uj/wCAcD+yrps+ueEvA+v6povxCg1KTw/a

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 30mua94vlvNO1CaS3QOy2o1GX7/mNIokgULjOEYKK848rxD8KPhj/bPxlm+KGl69Yar5mq/EDw74

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: pjm0uPfqWIZo9Pa7ZTblHiQw/YD8hYeVmvo34cfB+8+GtroWnWvxC8UapoOjWiWNto2ow6Z5BhSL

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: y41Z4rNJjtAUg+YCSo3EjIPOeIv2XbHxf4YuPCmvePvGut+C7i7W4m8P6jeWtwksa3IuFga7e3N4

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: YwyqoJuN4UAb+Kd9TndCXs0ktUn2tfTV/wCe5wPxn8LCx/ak+C2h2niHxdZ6P4ul1+bWbK28W6pF

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: FO0Nos0QRVuB5Kq7EhYti44xgAV9P6bp8Wk6da2MDTvDbRJCjXM7zylVAALySFndsDlmJYnkkk5r

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: k/FXwm0jxf8AEnwL43vLm9i1Xwf9u+wQwOggl+1wiGXzQULHCqCu1lweueldrUt3OulT5JTdt3p6

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: WX63CiiikdIUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRR

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: RQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFF

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: ABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUA

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: FFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: UUUAf//Z

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: --b2=_JP7l45Y0UI4Qm3DHgFkW1fbQjUAcpkAfxyhXAiGVt4--

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: --b1=_JP7l45Y0UI4Qm3DHgFkW1fbQjUAcpkAfxyhXAiGVt4--

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:00:06] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbbn1wT8z2SrxG

[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:00:06] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:00:06] Email sent <NAME_EMAIL>
[2025-07-19 08:00:06] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:00:06] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:00:06] Global sendEmail called for Test User <<EMAIL>>
[2025-07-19 08:00:06] Initial email body length: 765 characters
[2025-07-19 08:00:06] Birthday notification detected via explicit flag
[2025-07-19 08:00:06] Birthday member data extracted: Array
(
    [name] => Jane Test Birthday
    [email] => 
    [image_path] => uploads/members/test_birthday.jpg
    [photo_url] => http://localhost/campaign/church/uploads/members/test_birthday.jpg
)

[2025-07-19 08:00:06] Email type: Birthday Notification
[2025-07-19 08:00:06] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:00:06] Set Reply-To address: <EMAIL>
[2025-07-19 08:00:06] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:00:06] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:00:06] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:00:06] SYSTEMATIC FIX: Found 1 images referenced in HTML
[2025-07-19 08:00:06] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:06] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:00:06] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:06] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_3ca3f5ab3c3f997ef51ade029edf7b39, mime=image/jpeg
[2025-07-19 08:00:06] SYSTEMATIC FIX: URL replacement - Before: 3 occurrences, After: 0 occurrences, CID references: 3
[2025-07-19 08:00:06] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/members/test_birthday.jpg as CID: birthday_image_3ca3f5ab3c3f997ef51ade029edf7b39
[2025-07-19 08:00:06] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:00:06] SYSTEMATIC FIX: SUCCESS - All original URLs replaced with CIDs
[2025-07-19 08:00:06] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:00:06] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:00:06]   - Email type: Birthday Notification
[2025-07-19 08:00:06]   - Images found in HTML: 1
[2025-07-19 08:00:06]   - Images successfully embedded: 1
[2025-07-19 08:00:06]   - Processed image URLs: 1
[2025-07-19 08:00:06]   - Processed image paths: 1
[2025-07-19 08:00:06] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:00:06]   - URL: http://localhost/campaign/church/uploads/members/test_birthday.jpg -> CID: birthday_image_3ca3f5ab3c3f997ef51ade029edf7b39
[2025-07-19 08:00:06]   - Embedded images: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:00:06] Set HTML body (720 chars) and plain text alternative (161 chars)
[2025-07-19 08:00:06] Attempting to send email to: <EMAIL> with subject: 🔄 Test 3: Duplicate Images
[2025-07-19 08:00:06] Email content validation: Body length=720, HTML=Yes
[2025-07-19 08:00:07] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:00:07] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:00:07] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:00:07] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:00:07] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:00:07] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:00:07] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:00:07] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:00:08] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:00:08] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:00:08] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:00:08] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:00:06 +0200

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: To: Test User <<EMAIL>>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Subject: =?UTF-8?Q?=F0=9F=94=84_Test_3:_Duplicate_Images?=

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_zTs0Cq62WlPmMrqeWctRqwvgxGyRaKMru8ZdbQSj70"

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: --b1=_zTs0Cq62WlPmMrqeWctRqwvgxGyRaKMru8ZdbQSj70

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=us-ascii

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Duplicate Image Test Duplicate Image Test This email has the same image referenced multiple times: All three images should use the same CID (only one embedding).

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: --b1=_zTs0Cq62WlPmMrqeWctRqwvgxGyRaKMru8ZdbQSj70

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_zTs0Cq62WlPmMrqeWctRqwvgxGyRaKMru8ZdbQSj70";

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: --b2=_zTs0Cq62WlPmMrqeWctRqwvgxGyRaKMru8ZdbQSj70

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=us-ascii

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: <html>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: <head><title>Duplicate Image Test</title></head>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:     <h1>Duplicate Image Test</h1>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:     <p>This email has the same image referenced multiple times:</p>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:     

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:     <!-- Same image referenced 3 times -->

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center;">

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:         <img src="cid:birthday_image_3ca3f5ab3c3f997ef51ade029edf7b39" alt="Image 1" style="width: 80px; height: 80px;">

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:         <img src="cid:birthday_image_3ca3f5ab3c3f997ef51ade029edf7b39" alt="Image 2" style="width: 80px; height: 80px;">

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:         <img src="cid:birthday_image_3ca3f5ab3c3f997ef51ade029edf7b39" alt="Image 3" style="width: 80px; height: 80px;">

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:     

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER:     <p>All three images should use the same CID (only one embedding).</p>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: </body>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: --b2=_zTs0Cq62WlPmMrqeWctRqwvgxGyRaKMru8ZdbQSj70

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/jpeg; name=

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_3ca3f5ab3c3f997ef51ade029edf7b39>

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: /9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcg

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: SlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwK

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: DAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQU

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: FBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAyADI

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: AwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMF

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: BQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkq

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: NDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqi

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: o6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/E

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: AB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMR

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: BAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVG

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: R0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKz

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: tLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: +4KKKK4j9TCiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACi

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: iigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKK

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: KACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooo

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: AKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigA

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: ooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAorx7WP2kraz8

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: ceOfC2keA/F3iu/8GRW02rSaLFZMqrPb+fEIkluo5JWKhhtRC25cAHK5PiB+1F4W8D/CSx+J1nYa

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: p4v8C3MSzPq/h/7My26tKkSCSOeeKTcZJNhVVZkZHDhCKdmYOvTSbvt+m/3dex7DRXk/ir9oKPwh

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: rPgXQrzwL4ol8T+MPt32DQ4G04zxfZFDy+bIbsQjKMHXbI2RwcN8tGh/tBR+JL7xNo+neBfFFx4r

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 8NXdrb6r4c3act1bR3EBmhn8xrsW7xsoIwkxcHqgHNFmHtoXtfX0fa9vW2tj1iivnu1/bR8P3HwW

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: vPivJ4J8XWvgWCISR6jKmnlrlvtiWnlpEt4ZAwkZj86qu2NjnO0N2vxh/aF8OfBLVtF0/XLLVLub

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: VtP1bUoG0+KN1WPTrX7VOG3yLhmQYQAEFupUc0WYvrFJx5r6afjt956fRXj3hb9prRtdn8GnVfDX

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: iHwfpnjKJH0DV9cW0+x30kiLJDb74LiXyppEYsiShC+xlXLDbVn/AIad8D/8LU/4Qbz73zftf9lf

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 2/8AZT/Y/wDauN39l/a87ftez5vL6Z+Td5nyUWY/b07X5j1iivJ5f2mPCNl4c+Jur6il7o//AAr6

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 7mtdVsdSEMF1LtQNDJDG0nzR3O4CBnKeaeBXo3hrWv8AhJPDmlav9gvdL+32kV19h1KHybq23oG8

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: uaPJ2SLnDLk4IIpWLjUjN2izSooooNAooooAKKKKACiiigAooooAKKKKACiiigAooooA+IvGvgjT

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: pvjv+0fP4y0D4kvo+v6fpcGlSeDbLViuohdMaOdFNsv2eVgzKgFzmMMWB4L1pftA6P46l/YHtPAv

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: iDw3Pf8AxGvtPsoBpXg7RJ7m2jFveW77G+zRNDAywKuQCsZdZBFlQAPsuiq5jz3hFaaT+K/4u/z8

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: j5O/aE8C33x++MXwButGk8a+HtBH/CQfbNf0e0utKvtM/wBHjEe9poQ1v5jxFB5ijerHbkMDXa/s

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: teFb7wFfeOvDWv6Bev4rsLu1F/47uPtU0fiuIwYtp1nuXd/MjjXZJArtFE5bYcOQPfKKV9LGscOl

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: U9rfV/5Jfp+h8Ef8K18Xf8OqP+ES/wCEV1v/AISv/oBf2dN9u/5Dnm/6jbv/ANX8/T7vPTmtr9rP

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 4FahaeMvBs/hHSvF3iKE+GvGMF3JcajqWtLBJLo7x26AzyS+U0jsUULtMjYHzEDH27RT5mYvBQcF

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: FvZRX/gLv+J8aWmj6z8UfAP7NHgfSvDfiGw1PwdqGh65r95rmiXem2djHp9sEmh82eJPNmd5AqLE

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: HB2sWKqN1eX/APCp/i5/w7Z/4Rv7D/3J/wDwit3/AG5/yGPM+/8AaP8Atr/x7f6vj/br9G6KOYTw

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: UZX5pbpr77L9D5y+N3w1/wCEk/a2/Z98QL4V/tSysP7Z/tPVBp3nR22y2V7LzpdpCbZi7RbiMOSV

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: +bNfRtFFTe52QpqEpSX2nf8ABL9AooooNgooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKA

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: M3xFoFr4o0a40y8lvYbafbvfTr+eynG1gw2zQOkicgZ2sMjIOQSD8aeAoNQPwe/aZ8RyeKfF0+se

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: Edb8VaXok9x4q1KVbO3gs/3CiNrgo7IXYq7qzhsNuyoI+3a8w0j9nrw5ovgv4l+GIL3VGsPH+oan

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: qWqSSSxmWGS+jEcwgIjAVQB8oYOQepaqTscdek6kk0uj/wCAcD+yrps+ueEvA+v6povxCg1KTw/a

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 30mua94vlvNO1CaS3QOy2o1GX7/mNIokgULjOEYKK848rxD8KPhj/bPxlm+KGl69Yar5mq/EDw74

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: pjm0uPfqWIZo9Pa7ZTblHiQw/YD8hYeVmvo34cfB+8+GtroWnWvxC8UapoOjWiWNto2ow6Z5BhSL

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: y41Z4rNJjtAUg+YCSo3EjIPOeIv2XbHxf4YuPCmvePvGut+C7i7W4m8P6jeWtwksa3IuFga7e3N4

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: YwyqoJuN4UAb+Kd9TndCXs0ktUn2tfTV/wCe5wPxn8LCx/ak+C2h2niHxdZ6P4ul1+bWbK28W6pF

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: FO0Nos0QRVuB5Kq7EhYti44xgAV9P6bp8Wk6da2MDTvDbRJCjXM7zylVAALySFndsDlmJYnkkk5r

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: k/FXwm0jxf8AEnwL43vLm9i1Xwf9u+wQwOggl+1wiGXzQULHCqCu1lweueldrUt3OulT5JTdt3p6

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: WX63CiiikdIUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRR

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: RQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFF

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: ABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUA

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: FFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: UUUAf//Z

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: --b2=_zTs0Cq62WlPmMrqeWctRqwvgxGyRaKMru8ZdbQSj70--

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: --b1=_zTs0Cq62WlPmMrqeWctRqwvgxGyRaKMru8ZdbQSj70--

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:00:08] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:00:09] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbbq5fl9z2SrxC

[2025-07-19 08:00:09] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:00:09] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:00:09] Email sent <NAME_EMAIL>
[2025-07-19 08:00:09] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:00:09] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:01:19] Global sendEmail called for Ndivhuwo Machiba <<EMAIL>>
[2025-07-19 08:01:19] Initial email body length: 6005 characters
[2025-07-19 08:01:19] Birthday notification detected via explicit flag
[2025-07-19 08:01:19] Birthday member data extracted: Array
(
    [name] => Ndivhuwo Machiba
    [email] => <EMAIL>
    [image_path] => uploads/685dc5657df2f.png
    [photo_url] => http://localhost/campaign/church/uploads/685dc5657df2f.png
)

[2025-07-19 08:01:19] Email type: Birthday Notification
[2025-07-19 08:01:19] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:01:20] Set Reply-To address: <EMAIL>
[2025-07-19 08:01:20] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:01:20] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:01:20] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:01:20] SYSTEMATIC FIX: Found 2 images referenced in HTML
[2025-07-19 08:01:20] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:20] SYSTEMATIC FIX: Referenced image 2: http://localhost/campaign/church/track.php?id=email_687b34afebd147.52397876&mid=29
[2025-07-19 08:01:20] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:01:20] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:20] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_270f749bb1b36f80c2ba07e78b7a8da4, mime=image/png
[2025-07-19 08:01:20] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:01:20] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/685dc5657df2f.png as CID: birthday_image_270f749bb1b36f80c2ba07e78b7a8da4
[2025-07-19 08:01:20] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:01:20] SYSTEMATIC FIX: WARNING - 1 original URLs still present in HTML!
[2025-07-19 08:01:20] SYSTEMATIC FIX: Remaining URL: http://localhost/campaign/church/track.php?id=email_687b34afebd147.52397876&mid=29
[2025-07-19 08:01:20] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:01:20] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:01:20]   - Email type: Birthday Notification
[2025-07-19 08:01:20]   - Images found in HTML: 2
[2025-07-19 08:01:20]   - Images successfully embedded: 1
[2025-07-19 08:01:20]   - Processed image URLs: 1
[2025-07-19 08:01:20]   - Processed image paths: 1
[2025-07-19 08:01:20] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:01:20]   - URL: http://localhost/campaign/church/uploads/685dc5657df2f.png -> CID: birthday_image_270f749bb1b36f80c2ba07e78b7a8da4
[2025-07-19 08:01:20]   - Embedded images: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:20] Set HTML body (5998 chars) and plain text alternative (2813 chars)
[2025-07-19 08:01:20] Attempting to send email to: <EMAIL> with subject: Your Birthday is Coming Up !
[2025-07-19 08:01:20] Email content validation: Body length=5998, HTML=Yes
[2025-07-19 08:01:20] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:01:20] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:01:20] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:01:20] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:01:21] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:01:21] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:21] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:01:21] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:21] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:01:21] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:01:22] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:01:22] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:01:22] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:01:20 +0200

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: To: Ndivhuwo Machiba <<EMAIL>>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Subject: Your Birthday is Coming Up !

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_an8IUl75SHWwVCMmuBi5E0Ykqla2X5ouVvahlNOc"

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: --b1=_an8IUl75SHWwVCMmuBi5E0Ykqla2X5ouVvahlNOc

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=us-ascii

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Your Birthday Reminder body { margin: 0; padding: 0; font-family: "Poppins"=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: , Arial, sans-serif; background-color: #f4f6fc; color: #333; } .container {=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:  max-width: 650px; margin: 0 auto; padding: 30px; } .card { background-colo=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: r: #ffffff; border-radius: 16px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1)=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ; padding: 40px; position: relative; overflow: hidden; border: 1px solid #e=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 2e8f0; } .card::before { content: ""; position: absolute; top: 0; left: 0; =

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: width: 100%; height: 6px; background: linear-gradient(90deg, #6a73f9, #d67a=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ff); } .header { text-align: center; margin-bottom: 25px; } .header h1 { co=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: lor: #6a73f9; font-size: 32px; margin-bottom: 5px; font-weight: 600; } .mem=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ber-photo { width: 180px; height: 180px; border-radius: 50%; object-fit: co=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ver; margin: 0 auto 20px; display: block; border: 5px solid #f4f6fc; box-sh=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: adow: 0 10px 25px rgba(0, 0, 0, 0.15); } .content { color: #4a5568; line-he=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ight: 1.8; font-size: 18px; margin-bottom: 25px; } .highlight-box { backgro=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: und-color: #f0f5ff; border-radius: 12px; padding: 25px; margin: 25px 0; tex=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: t-align: center; box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1); } .date-highlig=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ht { color: #6a73f9; font-weight: bold; font-size: 22px; display: block; ma=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: rgin: 15px 0; } .age-highlight { background: linear-gradient(90deg, #6a73f9=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: , #d67aff); color: white; font-weight: bold; font-size: 20px; padding: 6px =

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 18px; border-radius: 30px; display: inline-block; margin: 10px 0; } .footer=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:  { color: #718096; font-size: 14px; margin-top: 30px; text-align: center; b=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: order-top: 1px solid #e2e8f0; padding-top: 20px; } .button { display: inlin=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: e-block; background: linear-gradient(90deg, #6a73f9, #d67aff); color: white=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ; text-decoration: none; padding: 14px 28px; border-radius: 40px; font-weig=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ht: 600; margin: 20px 0; transition: transform 0.3s ease, background 0.3s e=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ase; } .button:hover { transform: translateY(-3px); background: linear-grad=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ient(90deg, #4a56f6, #ad5eff); } .scripture { font-style: italic; color: #7=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 18096; padding: 18px; border-left: 5px solid #6a73f9; margin: 20px 0; backg=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: round-color: #f0f5ff; } .social-icons { margin-top: 20px; text-align: cente=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: r; } .social-icons a { margin: 0 10px; color: #4a5568; font-size: 22px; tex=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: t-decoration: none; transition: color 0.3s ease; } .social-icons a:hover { =

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: color: #6a73f9; } Happy Birthday Soon! Hello Ndivhuwo Machiba, We wanted to=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:  remind you that your birthday is coming up today! Your special day is on: =

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Saturday, July 19, 2025 You will be turning: 40 years We hope you have a wo=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: nderful celebration filled with joy and blessings! "For I know the plans I =

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: have for you," declares the LORD, "plans to prosper you and not to harm you=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: , plans to give you hope and a future." - Jeremiah 29:11 May this new year =

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: of your life be filled with God's grace and favor! Blessings,Freedom Assemb=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ly Church &#x1F4F1; &#x2709; &#x1F310;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: --b1=_an8IUl75SHWwVCMmuBi5E0Ykqla2X5ouVvahlNOc

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_an8IUl75SHWwVCMmuBi5E0Ykqla2X5ouVvahlNOc";

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: --b2=_an8IUl75SHWwVCMmuBi5E0Ykqla2X5ouVvahlNOc

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=us-ascii

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: <html lang="en">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: <head>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:     <meta charset="utf-8">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:     <title>Your Birthday Reminder</title>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:     <style>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         body {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-family: "Poppins", Arial, sans-serif;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             background-color: #f4f6fc;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .container {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             max-width: 650px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0 auto;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .card {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             background-color: #ffffff;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 16px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             padding: 40px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             position: relative;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             overflow: hidden;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border: 1px solid #e2e8f0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .card::before {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             content: "";

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             position: absolute;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             top: 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             left: 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             height: 6px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(90deg, #6a73f9, #d67aff);

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .header {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin-bottom: 25px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .header h1 {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: #6a73f9;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 32px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin-bottom: 5px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: 600;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .member-photo {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             width: 180px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             height: 180px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 50%;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             object-fit: cover;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0 auto 20px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             display: block;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border: 5px solid #f4f6fc;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .content {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: #4a5568;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             line-height: 1.8;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin-bottom: 25px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .highlight-box {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             background-color: #f0f5ff;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             padding: 25px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 25px 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .date-highlight {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: #6a73f9;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 22px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             display: block;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 15px 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .age-highlight {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(90deg, #6a73f9, #d67aff);

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: white;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             padding: 6px 18px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 30px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 10px 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .footer {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: #718096;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 14px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 30px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border-top: 1px solid #e2e8f0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             padding-top: 20px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .button {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(90deg, #6a73f9, #d67aff);

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: white;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             padding: 14px 28px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 40px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: 600;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             transition: transform 0.3s ease, background 0.3s ease;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .button:hover {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             transform: translateY(-3px);

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(90deg, #4a56f6, #ad5eff);

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .scripture {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-style: italic;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: #718096;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             padding: 18px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             border-left: 5px solid #6a73f9;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             background-color: #f0f5ff;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .social-icons {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 20px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .social-icons a {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0 10px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: #4a5568;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 22px;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             transition: color 0.3s ease;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         .social-icons a:hover {

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             color: #6a73f9;

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:     </style>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: </head>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:     <div class="container">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         <div class="card">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             <div class="header">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <h1>Happy Birthday Soon!</h1>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             <img src="cid:birthday_image_270f749bb1b36f80c2ba07e78b7a8da4" alt="Ndivhuwo Machiba" class="member-photo">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             <div class="content">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Hello <strong>Ndivhuwo Machiba</strong>,</p>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <p>We wanted to remind you that your birthday is coming up <strong>today</strong>!</p>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="highlight-box">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                     <p>Your special day is on:</p>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                     <span class="date-highlight">Saturday, July 19, 2025</span>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                     <p>You will be turning:</p>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                     <span class="age-highlight">40 years</span>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <p>We hope you have a wonderful celebration filled with joy and blessings!</p>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="scripture">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                     "For I know the plans I have for you," declares the LORD, "plans to prosper you and not to harm you, plans to give you hope and a future." - Jeremiah 29:11

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <p>May this new year of your life be filled with God's grace and favor!</p>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                             </div>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             <div class="footer">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Blessings,<br><strong>Freedom Assembly Church</strong></p>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                                 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="social-icons">

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                     <a href="#">&#x1F4F1;</a> <!-- Phone -->

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                     <a href="#">&#x2709;</a> <!-- Email -->

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                     <a href="#">&#x1F310;</a> <!-- Globe -->

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: <img src="http://localhost/campaign/church/track.php?id=email_687b34afebd147.52397876&mid=29" alt="" width="1" height="1" style="display:none;width:1px;height:1px;"></body>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: --b2=_an8IUl75SHWwVCMmuBi5E0Ykqla2X5ouVvahlNOc

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/png; name=

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_270f749bb1b36f80c2ba07e78b7a8da4>

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: iVBORw0KGgoAAAANSUhEUgAAAHcAAAA/CAYAAADaMleLAAAgAElEQVR4nO19CZhVxdF29XbOufvM

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: MAMCCqLiggQXiIiguGGUiIlGMO5EI4oLGtRPY/RjjEs0RHEJGhA3oqKDYlCCUWJQICpK3ACjiIDs

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: yyx3Pfvp/p/qey+iH26BqCR/Pc/xcmfmntt9qrvqrbeqWgb/Xz4lSim66667mq5hpAcNG9ZhwUsv

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ZXfUJ0S+A2P4TkjjI3elH5v8VH/H9Q7klB1gF4uHEICXLzj+xNMbGxvljjin/1rl4g4ddv1FcWbT

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 9LLlq/quXLfhjGwhfyiltDYVT7DIc0udO3Qc+c6f//LId2C4/5LwHXDM2yyXNDamdz/04J8Ug3Cw

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: pGo/4LxLICOjfaedSDFfAM/zgBP61sBBh895589/2WHnSb8DY/jGpKmpydh30BH9n3xhxrMtrn1H

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: q1v6Sch59zbHNakVI0GkgFIOKghVp4YO8wZ02m3NjjxfbZaVUvqVEKK+9RH9G2R4Y6P1yqtzv+94

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: pWOy+cIplBvdqcEBuICi60DXrl1x7rB62QpIGhZEhZJ7aP8BR02/555XduR5a7P8ozOH7S6FmQaA

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: N7/9IW1fOeqss9rN/NsLwzc2bxidydS1Z1aCRZGCMASgSkJNTR0EQQT5fB6EaUDgB2AZorlD+/Zv

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 7+hz12a55JNdFr7/wfWnXnppr29/SNtP+h533M4L3n3z1xvbWm+q7dSpkxSCF12PMGEAKAKcC7f7

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 7t1ztm1DNpuFKIogHrdAymgFdOwY7Ojz18oteCUjkPLwv/z1+SmHnnRS/29/WNsm6Ga6Dxiw2/IN

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: G6dQZvy8pq7WROUhUEokEtV7tx100EF/Wbx4MUXl1mZqgBEKhUJBCmHOnzBiRLiDTn+z0MbGRpor

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: lohIxKgPcu/VG9ZeMnTo0B2a3PjBqafu3OYUb4+IOiSiYLiuC8l4AmKGqRXs+z507bbrpoULF3Zz

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: XTchpQT8G3wlUkWB6yz7T8AftHHMGEU5Z2iuRDJJWx37pFdWrLi/749+1KMKtHYkubCxMfnmB+/d

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Ekg4nsXjlKIJBoAwDDVoQqVJpRZ5rre8tbW1p2VZVDAODIi+lJQbM+nkoh1t3lsTCoQo23dUwSmB

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: pxTweFzYYXDGkmUfjTv67LPrvntD/nwZ2tTE5rwy94dOKAdLQZkTBRARAGHG9GfQ/HLO7Z07d3ps

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 7fq1dbFkguFuRYlCHwwhQIbBGkaNld/F+X1d0T43U5ORTJiKcQPytgPEshhY5tFv/nPx07secciP

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: RowYIb7rE0Er8+rYW85ZuX79eCuTrOGxOISEgGQEfweRH4FlWaqurm5+c0trf0MYBxSLRVS2NtXx

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: eBzy2RzUpFJv3ztmzOrvwJS2WbRy8wVHUcaUJABEcCi5DoRAaNH3B2zM5sf99f3FgxobG7/TbNYP

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: fzasgxtG5zPTamf7AZR8FwKQAIRqFGwYBgRBUCrZpQWu5w4KleRWLKZ9rSUMUGEEBqOKKLX48MMP

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: j74DU9pm0cqNUSE5YwpXuB+EYFgxIEKAiMUIi8W62b5/34MvzLxq5E2NvZqamr5zYAt37Ucr1g+I

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: QHb3VQSKUaCMgzAsCGSEIAl9aRQzzUkyijKMMYo7FhWLSsedi6++5ztdunZ99T+FzKGV/0QyCENU

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: biwWAwkKgiiEEBTuYGgp5ju5IK97/Iknm258eNIZp40cWfvtD/0TuX7cuNrmbPY0KYwkMw0Qhglo

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: hVBpVAEwwoAT9gLj5tN+EPSjgvMgDME0TYiiEBLJGBQLOahNpzf23Kfb8u/KvLZVtHINi4eM0RDN

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: F2MMEGOEUsf5+orX1IAdhiZLJfdasuLj+178xxsPDD7vvD0xjPq2ETV+/4y/vdArkPJoJjjFhYmL

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: FHerjCLglAFVyhGcTiZRcLCM5J74e7y4YBApCRFaKy6UkmoJQMr+NuezPaW8cwl4nHEXV3kUlmN3

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: NFsYOiggICmDguuCK6VG0z4lx89949WnJ780+9GDfnz8UaeOGFH/bU3g+okTY2vXbRopYlYy1BZH

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: aR9LKQWDMCARKi94RyqVLRSLF3LOTQyL8PeoWJQgCHCuUSqZfLMTgP9tzWV7i1Zu4ISlKAiLgnOk

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 3jA6AsYJRKC0eSu5LsTSGdQ4UNPCXcytTKbHxlL+p4uXL39y5muvPPy9Y47ojaDrm97J06ZM2c0O

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: vD4RBYLKYqK8KJFtokSBi+ifwNMykMeFYdiVUkooq+zYyiLAy+QilGH4YWNj4w7PTFVFK9fKmCXH

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: KeU5L09U71ilNGODZs6IWRCEEbiBD3nXAWIYYEcRSMYBYlaGJq1jP25uefrhl//6bLdD+10yZPjw

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: vRcvXmx8ExMoRX5PYvB6hVOhXLsVHfqEIXBFFJVqrmFYs6Mo7GtZ1mbkjK9VQbRcKpVswWjzNzHm

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: b0q0co888JCsjKKspt8wNpTVVV1WsmvbwAwThBUDKxEHNwpAcgoBVcBiJrS5JaossUshcI9tLRVu

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: f37u35oG/3z4tfsfe+yu/855NM6ezcMw6M6FiBPO9MLUTBQ6EymBgvJjhvEAA6illHZHpVYXLf6t

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 9s2E6M8opVoNwXbo/O1nRceuPzj4YOf3Tzy6CTlXzHGGMgJKGJjCAC8MwIzFIAx9oExA4DtgWTGQ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: CFgAwAtCyNTUgYsMlxNCMh5nSUP0bHPtvV0ph+xz5OHjd8nUvvaTY49dNmLECGd7hhmrnnkmHQI9

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: zA18JokCxbj2n8gjQxgB8eVaw2DvRyAHMM4z6GZQqY7jQDxRjnFNgahRQeh6hZ277L5hIczeXsPD

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 4gDW0NAQu+GeezKE0gQJw4SkVNAwNEKNdQiVCOMAImEYSgaBL5Xy27Vv710+cqTthaEHsZibchyv

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: d+/eESHka9Vyaf/YqBSd1K/vdRvsYiNLJMANAkina6BUsEEIBoqjEj3gFP0ZgGBMk++IRLVIBYwR

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: IJVdgaYRfyalVEKRgPvB0jg13ujSZefHexx77IsTzz9/u6TTBgwd2nvh0g9fAEvUhehHKz8XCkD6

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: AXAJT5o1NReXctk5IPieOhLQupQ6RKKEAJURoP/wC6U/X3/r2J+MGjzY25YxIeY46/rrG954bd6A

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ku31K9qlXRhlHRljdYSQGqWUKaUUhBC0mogTcLFLQkjAGAkIIU4gIzuKwnwoZdEAkqNh2FKXrm2L

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: x6wP99hzz1X779Pz/ZWUbsTM1Rdtls3gZ+BPTz50wZIPXgyEIYRlgeMHAJ4sMzvKAysRA0aIRtGt

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 6zdAPJna7Jvxwh1R9dFVk4fvcQGkseKhLaeYkq0dGxp+n7Lifzp1+PAPRg8b5mzLg+x22GHnbsy1

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 3cdTSRIxArIyTeX5AH5oJ5k5lHGezIbuFGJwWvXHqFydKNBKluDl87I+U3Plir/PH/d1LQsqc/Dg

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: wSk3Ht/FjmDvlWtWHqpM84RssdCJmkIwxkgURQS/e8vnVUligMSQjHPtGiIZ6GfHNOBToKJIqUiC

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ySiGayoIgkhKKTnnhbhptiWTyYXpeOIDTvl7CUNsgkC67TKZnO849hmnnZbbrNwRjY3xyU8+/nZo

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: mN1FIq5jXQoMosAHwpDlCCGZTEL33ffQie1sSyu0tLQgXwtU8DKIUWWfrX2fjPTPMOMSOQ6kYnGI

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: XB8szkLfcZcJoqbutftet7w0dWrxX1EsPtQO3+/zO1tFo6llQUi1sdAPKijZwBRMSxnWSM/1bo8Y

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: PU0JRnBsaJpVRX8cFHCcqO+3DjjggMOeve/hxV9LqaNGGWv++c8D1re2XsxMYz8vCrv5URhn3CAY

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: M2jcIrhWIDBa/Zy2GlAua9Lvqz8HIj+9YYAAYWUcgS4kZlqI6sGvMGqcc5XP5xUTNIhbViA4d4WU

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: OQtIwc3mN20ukJs4ZozToX2Hl0PflYHv6XAoioLyTRiD0A8gdDx4b+Ei2H3XbnoQqFi8qoOpDhQD

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ZxRZ+Te3TGgrFQAMDoUw4AGlexb84Jr3li555cBjjhnyr4RPU6dOxaimCy4k/V1oNRAQooIZazZN

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Y1JNMpmASHZjhBJW3dXomzU5I0GiC6NECcHnW3U7feVkwYQJE0TPow/vs+itBU8vWrlsdonK0wsQ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 9nQgSpCYQXCeyJQhyCM4PnRtaFlomRSKcCHiayXU1G6sMgd8hhjGocXD9+j+RMyCRCYNlDP9GQzl

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: HNeFvGuTWG2GkphpFpWfLIZOfXMpv3vBc3pxYapPqh8JUSyK/rZTQ/scmoJqDFhybJBBCOlEUgMP

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Rim88/bbcMABB2R79er1UWtLSykMQ4V/v6WSq2Y5xAsJhWQSSjLS6FqaHGjCIi5RPddkN43b58jD

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: r76ssfHrImsjDIOYVmjFlDFKQEYh7pSFIlRvqojWM847cYx5tQcqm0OF5hAVXL5PKCifN7R37y+1

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: IBMmTIgPGT78+zc/9ODt67O5h5rt0g/MuhpLxSwIOQOFNViIvimBUuCDrxR4UQQBAjZZvtC6oOuq

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Xvicqrlmg5vAqdBJjAgxA6GQiMUh35YDXP+UG8CFCbXt66G+Ywe9gIquDU7og6QEnMCXwjBXxWLx

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Cfvtt+/ln8r0SMJne8XcO4LQwzGbYsawnihE0l1TeTgQ5GtRkX+dNSuTzqQ3fK9nr8c+WL70JM5Y

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: O61UqAAsQoGw8oZ07RJYySQESoJCBkxFYAoBhAvi+P7ubmvLjU0zpg/Z57DDzj7lyCM/+ioV/gXT

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 5GEQGjwegwhDG1Fe/Wj6mYLXNr4xfxPvd2g/RulOqMyqKrWNwB2rtC3ESRcNLhYNGzbsczNBaFn2

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: HDhw7xvuu++itlJhOI+ZcSOZJDT0dZICmTF0SUIIrSgMMBUluuAOKoUCn/haCgQtWwUgkOpFKtau

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: HIYqjVdwwQLxe/f5vh9LxOP4RDds2ABrV6+Eom3r+xu4qPBzQSSpF7518slDh99/zZjFywhRn6pb

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: /u2FF27ikryXNGPasuZybcANsdm84EMUhqH/nUyliOt6eyxbsayLQch4Qdk7DEggwwhw51MNWBCN

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: KuBWTK/aWCKuFcxMC0qBB5hMB8sElkzQrOcetKmQe3zavDmjJzQ1Zb5MuSGlkRA8QvehINIuBMp8

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ssykMq/C0KHED9yBWGyOu1QrAD7BSsg900gBi+Sms088+XMrL8647LKOPQcfe+aGYn6yLegIUZNO

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: RJyTousBZwaEodRRAroDVbkwHMN54uVFZaqzWumBz4UqCuiU8TmRclQBruu4nuetMyxzYZcuXV/v

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: se/3Xt1r7x4fduu2u+OXXP7R+x+Stxb8A1YuXwGO50HMNBG/APECxZwgWwfGI3323HfkGdcMfB8q

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: oPBTyh06dKisz6T+2rx+Ha5+MA0BVWID410NChgF23OrTA8Lw/AYqeBCIORmpdRjKgi9CNEqxpmR

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: BAgizfGGtg3VxIQfBmAlU9r3eCpC3wHJdu2YFOLANS0tN9x4222XT548OfF5Dxyl1nFCw7BsBBra

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 1EaykpNl6+o71rzVu7bWBEpPiJSkEUETWEbyaBQR0CCQYpEGVC9CLrdia7sVOfNnX5x1/wdrVj8Q

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: xkSfogxEgMl/w9AmEdOJCOBQYRge4iNFAIk71Khw8/iAGbp2Vb5Q+/hc0NXh5fv+RkLoczXpmqsa

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: GtpfbhrGtDXr1wfvLlrYe9GiRXsu+WhpzbJly2LFYoFqnwxlq4dgUNoeMNfP1/P4r08/6ZQLX3li

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 2htHkCM206efMssYBpxyyYh3l69e3SYI1EZAwQ08zJho1IZjQ8WgOdBgQGoFkyAI2glGzzPN5Fiu

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: YL3v+eeSULbjnJMIJARBCJYZA64o+KEEysuhElZ+IBkiEgnIew4gnwCMmoFjX3LzIw87J1500cSn

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: x49v2Zpy33vvvTAMghZM2yE1alkm2MWSisdi81JmTc7N579nl0r1VialF1DIoByuISnDOGBanvrB

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: +p5779v0WTeAgGmf/of3afbzl4YmG2TELBZhWIrWS5vRMqLVr1El5OMcOKuwXti1EFVZMgokiEBQ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: VC0JozBypIwKgvIV8VRiDhPmOwSiyPa805ubN/WKomgXxhgzLUvDUj8IgFsG5At5SCaSEOcC3EIJ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: OKVS+tHCvbt1Hzflzjuf6Natm/vZZ/R/2kmOPHvEyq6dOz1u53Jh4Dk6v6v9FH4VLUPzMigpw/dQ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: SSxRIaWSfVQpm7s/bsWLdZmaKzmjr/ieF6Gp5PjHfgilfAFMyvXqdorFsp+hBIIoAlxuUjAQiRiJ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: DFazoS3b+PbixfeOb2pKbk25qJDdunVdYheKilXQfGC7eUHYszeeeaazYtmygbFkIo6+kJoCOCbj

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: fRfiVgxU4IN0fTsdi//2hEMOeXXL+2LLyVNz/n7m+mLLMy6Rw0gsxpHQrAIgnePGpEPlPQouMNyR

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: nutCgOSOBDAxhyz1boDI8QISyRcSpjWmvrbuonZ19dekksl5VJE+ge+NzReLD/qe92NCyG5cCMF4

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: OSZH96c4hYLnQKpdBlzf0fiFSxm4udzzA77f9+zXnnnm4a0pdqvKPb9Pn4ADf1QosjpuCAg8t4x8

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: K6GNqihk8wXleI6UEUFnP/BHEUpziVjipyYXf5Z+gHQMMKWgXSajTQWaJcu0tI9E/6StBhIMjAGW

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: yGCKUXJurG1r/fGEx/7489ubmmJbG3yH9g3vCk5DjqFF6EMmlVofM+OvXz1pUlyGsmfRdRgCnmoG

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: CL/Ptx2wCIcENz8YfurpD4waNWozI9U4YUL8ylt+e+ncBfN/Y2TS7cCySKD9Kfpn0H5S+8rKYkfQ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: hA8frRnjXNdEJwwLCC7Wkh2B7TazIHymobZmcDKRuChwo5dz2dyB2ZbWG3PZ7OWFYvFox7Z3Cfww

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: iTfFEKeaaq0uIPwuYjHwy8sfDMqUSdm039962/kzHnjgna09l89VLsox++//Zl0mNU36oYphCk2V

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: QQOtfKDKrlR3HvpgLgSuYOL6XkOhVLw1UmqQaVk3cc4fZwpshPZ2rgAcTZkfgu96yEPrwLyadtNx

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: XRhoWI8+jVmGWLz0w1/efufvTsPCgM+OM24mlpqMt2FaD3dk4Idv9u7f/2NZjDJu6O+m0SqjGuEj

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: u1aTTEHKjIGbL/oNqfS0xlGj8psVO25czR9+f88VLpVjzFSifc5xiBMEgN0JRBLtT7cERfh+y+eA

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 32EXipHvullO2NxEPHFbOpH4WW0y2RhE0a6e447xI++ZIAgui5TchVLKkb3S5hwVyj7JxqFiyxtK

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ldOvWFVSstEKOEGh8OJhffvccM6Pf7zqixT7ucodN26cc0CvA+8WBDbgTemWE1LlUAevKo2Gg8Od

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 4csKIwNqj6JTGu8F3klJxkYZQlwdF2Y2bpgK0XTMsqA2mYbWjc0aeHE0P5UUHIZfGKxjkR41BFjJ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: REPetW96Z9OaPT47zpqMt9EwjLmmMJRTssPdd97lsaljxgSt+Q3tiZK7Ec4IhiGpRByEUlBszUJY

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: cqKkYT15YO/e929W7F13pf/Y9HhjZNCrHCnjkSHAiMeAM6HjzfJGJRoQ4fwRZRNEyWEZMCLhT6Vc

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: wTm/wkqZfRPJ5E8NIWYGUg4ret6ztuff7Uby9AhIOyoMgnNEYgfnh34caDXFiopVm1k+JDIQrjEv

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: VMzxcj26drvkwYfGn/zU+Pu+EpP2uS2cgtLmpBWfR4FI3G3oN7Xv3EKxm/ljVC5WQUi0XQQpIhKC

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: irkyGm4Lej5IOd2KWSMLudzLoJTv2Q44tg0GKyNNVLjOIyuokA2fsFz4VBWjHea+Pn/sKRdeuM+W

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Y2wP7W3LNBcIykKqoNihY8eluNRrUqm0GTNTuA6RBi20ZSHGDUgKC3zPeX9Av35jHho7dj3e439u

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: vTV1z6SJozcV8z/zKIk7WOnMMcFAwPdDMAyrPKeKoEvBiyIwjFTRiODtGGHXJ2LmsEQ8OSXO4ntF

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: YXBNvlR6OG+Xzih4TmcQzJKcUowOsIDPCXzwKhcSECho4nX16RbWQKPjIFQyV1jXMZa646ZzL5gy

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: rM+g3FdR7Bcqd+o99xSplLdJL1ixOaxBknvLeK2ym3WKEPOpnG32FRzpN8Hbu0Fwve05//OLa897

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: tuseewy17dLdBKRHZAQ1mQwErqcXi84woQ/GMEoqsLDITcqyTxYMgczxf531wh+vGDt2c4iEoCpS

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 8JaSMmcKA3eOPr8im8+3LxVLFj6wdDoNNekMuCVblXLZ5Uf2P+ziqffcsxTK4Q57df4rp0SCXs7T

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 8ZQNEaQa6qDkOZp4wNShXSyVwVRlIauyYm0B9NkU4/1imcyAVKb2D8RRHQotG+c0t2yYWvBKI8Ek

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: XVjaIjyTADv0tVVD/4/uprpr8T2QT5RadU9QwTESFesGH40cesZxq/7+xo1Dhgz5WvVdX9h8PeSU

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 09+qTSWnRwHWzkWYPdicKkMF6xWmQ6QymtQIusKhVuqTCOOcF3132FNTZp25n2Xljzl04E0mYfcZ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: QF2nkIeYZeh7VbnhzYDC87XiU8mMDpkiQqhIJfZ/Z/78g7ccY9ISy4J8Pmtxvn7ooEEaNWZqa+MY

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: oiVMAwotLaA8D9y2bFtDuuYX1/7853/fPL9zzz7o9YWLrvYoSeZ8bKeJQTZf1OgXF63vOYCgkmmy

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: QwELJVZ2bBBArzGkef6Gzp3/yUxT+S25q6IoeEAIszsXpol5MTeMSIA0Lsa8VkyTQVAJJXHHYnUp

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: WiedAapwyvrZYvwd4oWVJLJgKTW+azr9ISHka5f/fKFy7x41yuu37/63W0IstBLW5hIcgwkdx0U4

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: eKkAY0DkVKuX3Hxzqak+nkk0vLbwrbtf+HjZo/v06FEzZPDxV3apbz88yOUXqMAPMNWFRD7WP+Hk

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: VSghJpCBMUAFEUQhASIs8AxOF65ecWnjvfe2r47x+vMuXrZrfafnVd5Z2qFnT51CLDqlGp3e8wKw

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: pJKqZM8+4Zhjj1y1YMH0Pn36aHg+6JRTjpn7+oLp8YZ2uwUYKiUS4AdlUkIQBr5tQxLDQLQcRcev

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: IWJmO9O6oF+P3gftn0qMT2REx5plKyZ5zS0fuFReEQnWoDgniPgVpSCYAE4MYCHFUAgSwtQWCUM2

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: zg3dwY99wrghMPkSBL6OJJCwZJ4b1Qlj5olHH3VM61vv3Dl69Oh/KTX6pccm/OCCCzYkDPFktqXV

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: re6uYiVGxf2LA93yVpsRdRV84XvBESjxUIbHP/b4Yxf26tGDvD1zZtPoyy7/mXT9vwkCMs65Cn1P

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: m0LkaLP5PFhmHDw30CQIUpYBo6TFzh8x7bmnj6uODznh/fbc48GEYPOP22MPrThLmDEZBTTIF3zq

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: erOOOfSIC564++7NYcNxl1xivrfkw595APWYCtS4SJZTbLggEER2qG2HqFqGttsSF9b9h/bpf945

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Rx89CWKQ+yhUQ9ev3/gQs4yz7NDfOaSUBoTqBAkudLyT2uLR4oLBNClKKpXSJhfNPs5TL8ZCHjKJ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: OISeq0qtraFFyAfHH3v0FX/8ze/mwzZUrnxp98CMiRPlD44/4a3mXAsTnPcu2SUjnkppc4ylLZjn

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: RQxUnQ5TW1BtFVrcc8pVHJZlcdf3+7355lv7rGlre/W3o0YtveXu8bPn/e0lo5DN7Z5MpuKOa2tT

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Fk8mQKL/VpH236VSCeKIpAkxshtbeoz+xeWPznvxRW2G3/7HPzYo33//4IMP1qYrVlfX03OddMK0

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: xtXWmr+eM3X65rBh8vPPJ56YPPkOj6pTsIXeQ5bNNLXZNRQFGkR6l9m5wrq4ZU1NJ8SF62PxyW6h

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Vc19990ha9avvyFvly6z0onOIUgSVlOIGGvT6qv65CKYjGdgIKunInB9V783TUNHCFgzbXEBFmIO

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: L2j9/n77/fKMYade85vLrtjm4vivdOAJgqv99/re7eD5f4qZlsR6KgRNtl0sk+KyzJ/yLTnULSRh

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: WnoC+UIJXBmSDdmWHz721FOnNTY1GaOHDVtz6YgLrqkVsclRyY0wzMAcsu06UPRsDTxc3aiV0P5Q

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: cBOR5c7F0Nuv+g1YW7QlGdGrR4/pXes7nL7mjX+M/2D2gk/Rlzf/7pY+G+38aS5TVsjKmCGyPRCh

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: AiNUkBaWCkrO6r69+5zzyIQJl64+6rhFHQzDbG1rvXLTpk33gmDH0ZhhlTxXJwXQlUAF6UIl6f6p

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: kLHiZzGSwEWgQRMueNuBwHGhNp6AOKGqec2afMf6dndccMKP773q3HPXbqti4eueQ/W9QYMGrm7b

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 9EjOs3c2kimIJZK6LVLXCKsyg1MVHTURqJD0WOzggZGMa8gfBR4EJcdrH0vddP5ZZz1w7ciRa+6e

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: MqXTDbfcepMdBceLTLI+Mlg5T6kImFSA57jlqo8oAkvJ0AzldateX3Dr1ymLuWLsFYkHnph1Z1vo

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: nRPLpAj6PMxcWUDBy5egNpEqha43+4iBR9w15c7bZukO/aOOOmBDIXdyKfAvS6RTMbQgVjymTarr

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: e5tjfaiSO9Uqiy1GhSATSQ6kcnXmKoz0gpdeAEGpJAWoFft/r8eNv772+icH7L134evo5IvkazV1

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 3XDVVWvnv7f444iQfslkItWWzRKkETFtRcrNvrA5c1pRrv6BlGDoSkpfh03YyxP5Pncdt9/iRYvr

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: Ro8477mrLrkkd92v73pp3fpV7zW3NA/0Aj8REkXwbzH9iiE0mjVdwuP7JCaM4l577f38Yw8+uFVe

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 9bOCDNfkZ58/y6bRxejMMeZEUCgIBREoSBuxdTvV1v/PPbf99pYrz/0Zdvrxky699MA169Y+pIQY

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: IgW1ENUizYjhGc4D8Ue1XLY6d81kKag8j/Irfoay8o7VgEr7dk+5+YKXNMWMvbrscvXLTdOe7drQ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: sE3FeZ+Vr3UO1fnnnx/86szhf6oxzd9sXLrcrk+ngERlWh15T+2D8cJQqNJnhIpG0h7hvylMXQOE

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: qS4jFgeRjFlZzzn5kRdnn3fLhAmZq88flps2/pk/xxT8Lk7ppgQmv4NyHIymz4zHIMCwgXFS8oO9

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 7/j973f6qmNfvGZjr035wq/cUNYRo8yoGQlqgqgAAAvdSURBVJRDnIrIK9rvZJKp6/5w45jJR/ft

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 2/LCu+/Gzx0z5vJVK1c9rJTqGQSBERcWCGC6qSwZT4IKJKTjKfBsr0JPli+qyBYp+PJuRqCIqFui

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 9VIKTEJUUCx81KWh/a9Hjxj5s79P//Pcr1u2+lXka7djzpgxQ47+1f8u/nDpkjCfy+5HGY1T7XMq

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: F60kFyo7F6cY4W4VWNjlA6MMglD35kCEq1wws1QsDtywYVNp7ZIPXrnzzuvDGa+99ua6j1b8Y9Wa

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: NQcSCQ1o+BhyxBgmaQKMQeh58Q7pzEsr33//gy8b812PPJK+54H7xiba1x0SMU3gYk0pmJIou7Xt

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 9ctHXvbzi8bfMat/587hMWeemXj4iSnX5kqlq/0o3MlKJAm2g0a6FIZpK4RECxYLIi7AnQvqExtc

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: bZ6rXmWyR4GFLaVAwc7nsrVW/MHDBxx8yaiTT3nu/LPP/rc1nv3LfT1jx45NjH304Qtdom7k8biB

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: LEyEhV7IwvByzyuucLfCTbNKNYKu1NC2qpJZQnYqDMGIYMmAA3sP/tPEiR9B5WzGbgP6jmjOFe4Q

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: qYwZYXGd52gAg6U0ZqRk7927X/33qVPHftlYd+vfv08h8ppsGXSTFeKAYcFw0X13n932GD3/mWd0

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: JfrQoUONl1auvM6OvFGUsbSnD0pJgeeWqymEpiKlzrFieIeMHFZF6DEpJGDKjd5+pfpCKx4XheNC

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 0jCiXHPr8s47NUw49MgfTHqosfHffhrsv3w84JVXXlm69pwRd8UpvynbvHE10oUWUmoVgIEPEPO3

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: GONVRfPRmJ/TZTusnB/Faoa4hVVqu788f/4TR5955g+ggoBHn3LGY52SNfdJ183b+TwkYpa2AAjK

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: mCHo8lWr9vyyceIiISb7Ud61O+HRCBjmUCeQMclm7t1l11Nemz79Zfy7u5qaGuasWTXeVcHlThSk

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: AwoQT6d0KRCGYrFkCnJ2EXxZ5tBRsRiLG5ZZLnBTUvtTBJdYecIpAd9xMbGQSwr+QoqJK04a8qOj

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: n7j97ju/CcXC9ji19a6ZM83npk8dMmfB/EkRFxnNl1Y6EVSg9FkTSCXKT5mrSrqwwtXihVUYGBrE

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: GFtw89XXDrlo2DBN7N9++6S6cVMm3O1Z7NR8GBFsRgPGIMkMECX/L5eeeMIPv6ig7vIbb+x6+8P3

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: v5GoyzQg4+UWS0ra7sJfXXnV2b8aMUKfEodga9ILf7myEAbX+1SZmE9GRGzni5o2dMNyWh7dAS4u

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: UglxsHgQARQWD8ZNU79iiIP0pe85PgX+Tm0iMXHgUUc1PTJmTOGb7tjf5oM9sf1i5h8eeNoC+kuL

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: kEUxwrygUISMGdflLDplWOGiUaJq96CsFNExVuZyhaHLXnk83uPWceN+umDBAk3fjB7989afnnji

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: PYVNzZuwQyARj5ePQVB64TR80RzwHjNmzTorXVtTjzuqlMurJDM+GHjQwedfc955mrHCyoum2bPP

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: bm5tGc0EN6udAYVcvgII0ckT4DEBShBwijlQtFzTXe5aQM45UjyKQub6LTEF7xme9/ROiczp55xw

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: wtGr5sx54NHGxvy3cRTDdjm1FRuXb57w0KR+++x7SkLR2xqsZMFty+ocqoF+NSqXs6IyuQ7iQSf/

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: qyEEFl5jvIwNZ8BZLOuWLrvmjjsOr95/cL9+8zvUZBqjYqkFC8NS8QSUCkUo2bYJXbt+7kEsD8+Y

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 0aHg2oNUGGEtD8Q5b96prt0NMx95ZH71Yf9x1qzua1o2/TJTV9uAY0BMgH+LKUKMr7F2GJkkRLxI

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 6mfq6jTvDBirF/LgtbX67RPxebTo/O+u7evP6Nau/uTxt407a9mcOU/dXVbqt3YQ93ZvlJ49ezb/

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: xS03Dft47fqzuWn2VpzVISuFfhILx6ptFTprVAlx0CzrszhkCMVsDhKUQX0y/eaJxw4+/bZrrnkf

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: /x77fc+85sqL3vpw6S2punYGxpLcDRf98vQrDxo9eus9R4cNG3bY0pUrnwRBGvySvX6vXXe9buIN

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: N0/ed999fSQorvvtb/d8dPq0u/KeN8iRIUkm0+B4LjAqyqnGcvpNKgqhEMwDKb3Q89pkJNepIFjX

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: uaHDG/vs3X1u9z33XTjuXyT3/52y3Y8fOuKII/DglMev+c1vZk2bNeuQZjt3MaX0UF9FBgl9IiQD

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: xoQO+aNquMSo7oXBmBl7igxCoLmQ6/XIjGkX3nXXXVcitYgKGdHYOOHj9RuGRVL19fDEO+AB5+s/

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: d2e0FYt9/DCoFYoW2tWkr68qFn938x137PTUn2eMb85nBxLDJJh082xHeY4jiaKY7Mru3LnLeqeQ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: ezVG6eJONbXLWnK5Urq+LvuLyy/flKyryx7fu/d2bUnd3vJvOVuqYoo2AcD0S+666y9PNj16tnSD

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: EdIP9gXGTCKAYKU8Nm9FlTgYfR0lvAxQGMP2E+5LOWTBunXXYxsw3ndiY6PduX/fJ1py+QNMYZjJ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: mNnc2tq61XbQEY0j4rNeev8MyxC8lG175aGJk6ZVFYvy0rx5Ry35cEnv2p3at0agVlsKVqcsa0l9

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: PLnEMoylQ08+eTFs2JBtHDMGg3K1JYt/6vPP/3u1sp3kGzm/Ak1108svd5n/6qt9bN870vXsg1qL

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: hQ6EixpqCCMiiklUbaWVERGpznf6Qdi5rv7cRc8++8fqDul7/PHdl61aMZEADKxLZn7/z3nzLt3a

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 7hl40glHvL9y5XSqYNNO8dQ5P5ozZ25jxf9hU/S9j00+vKWtEE/VpdpMxdddfPHF2VrGcmh5voln

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 8k3IN37MUKWKkfIOHTo88fST38/Zpf1c3zsgUNDVC4POQpgxx3EMLN9FOGNROnfsL2849cyfHLuu

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: eo9Bpw/r//7i92fUJtNnvTtv3rOf/Q5U3tgH77t1+erVZ3Vo1+6sxbPnPr8tedEdVb7xI/8qMSle

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: eP7Emialnu24cmH6/nsfyKxesb62JZ9PJSyrXT6b38l33Q5uoRCLm+Gn6pa7d+/xzqolyx494IAe

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 7707b97/+Y4/zZ6d3rhmbaeaZOqJnRs6vr74v1Cx33lBRLu13l382e8mTKjHAretzeHtt9/u3OuQ

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: vhciwfLf/gz/4wR9/Jc1kv03yDb7XPShPXr0IO8BsI5tbaq2tlZit+B/6v/pZEeSL1Qumr+XVqww

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: X54xo27ua6/VOGGYAA61MW50/2D5R3sKxtt5UVgvwzARoYnUh1IwPJ8h70ZBThCytnPnndd4jvth

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: fW1tq18seBBA8cZf/SpbTKXyg7t3367J6a0Jkh9hGIogCBR0AoC1AIjM2vm+MnbdlcDatdAixObn

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: gAliJLUNzokfhqqaMNaf30L8Dh22eL8KGtwG+dYee4TDCPlax/kuWLAgnkwmWZaxzWMIsUTkM1Ib

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: RQpPMNjyp+u3GPNnBTsst6pcVOroceNqm5qmnOBT6B9FsGfMsjq7QVhr28UYZUIYJme6+D4KdNjC

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: DIZsBDhuSVdFmhaeWRWpSAbSMowwCvzAZGbJ5LQlsL3VnPHlFmeLjjjiqFmFXr2WTv2CzvZtkcHD

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: fjLszUXv/cSMWxKPMJAqUoZhKddxpBsFhDFBDMGp73t4coZ+HliArvlwzvEEGd1FSLD2Flk23btD

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: kTvVZw3p4yZASRXJsEunTvN+MfP5h7+qgmfPnm1dds0vb3b8oHMESmAMFip9pg0WPejuJPweQ1vB

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: SJcCRPoEhEp7YXkESm9S7GbUw6Ggz0Fimg/81CoSdzY17dFl4CEjck7pcNt39zZjCRM7s/C4g1Cf

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: zmJAMp0B2ynpu2LJaYgdCZgJwiMBhAGCG+D4LlDOiRAGiwCYH4WmJJDE1hCPqH1U6CkHRPT4M9M3

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: xZ5/7oWDTj7xKVJy57w2c+Z2y57gIj1gQN+uIcgTCSU8Ukyf+4gneaHJwPMssfjOx6MHRPxTGtEP

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: UVGlsJgcz2bGA9j0MQtY7xPqY44y6TTJZ7MKO9wj34cVHy8zks8992jl418qmzZtCop2qU/J8w8W

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: MUtEWPQgmPLDSHc64vkKOmesVVrujy6f5YGH5ZYPS0Ep/78ZCC45ffSS5ByUDOH/AcRNdcEflr82

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: AAAAAElFTkSuQmCC

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: --b2=_an8IUl75SHWwVCMmuBi5E0Ykqla2X5ouVvahlNOc--

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: --b1=_an8IUl75SHWwVCMmuBi5E0Ykqla2X5ouVvahlNOc--

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:01:22] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbdF4Kt4z8fg6k

[2025-07-19 08:01:22] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:01:23] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:01:23] Email sent <NAME_EMAIL>
[2025-07-19 08:01:23] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:01:23] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:01:23] Global sendEmail called for Admin <<EMAIL>>
[2025-07-19 08:01:23] Initial email body length: 1534 characters
[2025-07-19 08:01:23] Email type: Regular
[2025-07-19 08:01:23] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:01:23] Set Reply-To address: <EMAIL>
[2025-07-19 08:01:23] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:01:23] Set HTML body (1534 chars) and plain text alternative (794 chars)
[2025-07-19 08:01:23] Attempting to send email to: <EMAIL> with subject: Birthday Email Report: 1 sent, 0 failed
[2025-07-19 08:01:23] Email content validation: Body length=1534, HTML=Yes
[2025-07-19 08:01:23] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:01:23] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:01:24] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:01:24] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:01:24] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:01:24] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:24] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:01:24] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:24] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:01:24] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:01:24] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:01:24] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:01:24] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:01:24] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:01:25] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:01:23 +0200

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: To: Admin <<EMAIL>>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Email Report: 1 sent, 0 failed

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_p8GuJucURLWNKIcUxmScmkx74d3RfLsJvLiD3jG8nB4"

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: --b1=_p8GuJucURLWNKIcUxmScmkx74d3RfLsJvLiD3jG8nB4

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=us-ascii

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; } table { border-collapse: collapse; width: 100%; margin-bottom: 20px; } th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } th { background-color: #f2f2f2; font-weight: bold; } tr:nth-child(even) { background-color: #f9f9f9; } h2, h3 { color: #333; margin-top: 20px; } .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; } .success { color: #28a745; } .error { color: #dc3545; } Birthday Email Report This is an automated report of birthday emails sent by the system. Total Sent: 1 Total Failed: 0 Date/Time: 2025-07-19 08:01:23 Sent Emails: <EMAIL> Templatebirthday_reminderFailed Emails: 0No emails failed to send.

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: --b1=_p8GuJucURLWNKIcUxmScmkx74d3RfLsJvLiD3jG8nB4

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=us-ascii

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:         <html>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:         <head>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:             <style>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 th { background-color: #f2f2f2; font-weight: bold; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 tr:nth-child(even) { background-color: #f9f9f9; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 h2, h3 { color: #333; margin-top: 20px; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 .success { color: #28a745; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 .error { color: #dc3545; }

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:             </style>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:         </head>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:         <body>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:             <h2>Birthday Email Report</h2>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:             <div class="summary">

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p>This is an automated report of birthday emails sent by the system.</p>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Sent:</strong> <span class="success">1</span></p>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Failed:</strong> <span class="error">0</span></p>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Date/Time:</strong> 2025-07-19 08:01:23</p>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER:             </div><h3>Sent Emails: 1</h3><table border="1" cellpadding="5" style="border-collapse: collapse;"><tr><th>Member</th><th>Email</th><th>Template</th><th>Type</th></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_reminder</td></tr></table><h3>Failed Emails: 0</h3><p>No emails failed to send.</p></body></html>

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: --b1=_p8GuJucURLWNKIcUxmScmkx74d3RfLsJvLiD3jG8nB4--

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:01:25] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbdJ2WY9z5ZBl4

[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:01:25] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:01:25] Email sent <NAME_EMAIL>
[2025-07-19 08:01:25] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:01:25] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:01:25] Global sendEmail called for Sandra Stern <<EMAIL>>
[2025-07-19 08:01:25] Initial email body length: 5078 characters
[2025-07-19 08:01:25] Birthday notification detected via explicit flag
[2025-07-19 08:01:25] Birthday member data extracted: Array
(
    [name] => Ndivhuwo Machiba
    [email] => <EMAIL>
    [image_path] => uploads/685dc5657df2f.png
    [photo_url] => http://localhost/campaign/church/uploads/685dc5657df2f.png
)

[2025-07-19 08:01:25] Email type: Birthday Notification
[2025-07-19 08:01:25] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:01:25] Set Reply-To address: <EMAIL>
[2025-07-19 08:01:25] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:01:25] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:01:25] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:01:25] SYSTEMATIC FIX: Found 2 images referenced in HTML
[2025-07-19 08:01:25] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:25] SYSTEMATIC FIX: Referenced image 2: http://localhost/campaign/church/track.php?id=email_687b34b5e7c600.73163539&mid=30
[2025-07-19 08:01:25] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:01:25] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:25] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_2ca86ca199e1731fecd0ceb5caf040f8, mime=image/png
[2025-07-19 08:01:25] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:01:25] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/685dc5657df2f.png as CID: birthday_image_2ca86ca199e1731fecd0ceb5caf040f8
[2025-07-19 08:01:25] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:01:25] SYSTEMATIC FIX: WARNING - 1 original URLs still present in HTML!
[2025-07-19 08:01:25] SYSTEMATIC FIX: Remaining URL: http://localhost/campaign/church/track.php?id=email_687b34b5e7c600.73163539&mid=30
[2025-07-19 08:01:25] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:01:25] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:01:25]   - Email type: Birthday Notification
[2025-07-19 08:01:25]   - Images found in HTML: 2
[2025-07-19 08:01:25]   - Images successfully embedded: 1
[2025-07-19 08:01:25]   - Processed image URLs: 1
[2025-07-19 08:01:25]   - Processed image paths: 1
[2025-07-19 08:01:25] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:01:25]   - URL: http://localhost/campaign/church/uploads/685dc5657df2f.png -> CID: birthday_image_2ca86ca199e1731fecd0ceb5caf040f8
[2025-07-19 08:01:25]   - Embedded images: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:25] Set HTML body (5071 chars) and plain text alternative (1782 chars)
[2025-07-19 08:01:25] Attempting to send email to: <EMAIL> with subject: Birthday Celebration! Ndivhuwo
[2025-07-19 08:01:25] Email content validation: Body length=5071, HTML=Yes
[2025-07-19 08:01:26] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:01:26] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:01:26] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:01:26] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:01:27] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:01:27] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:27] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:01:27] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:27] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:01:27] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:01:27] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:01:27] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:01:28] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:01:28] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:01:25 +0200

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: To: Sandra Stern <<EMAIL>>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Celebration! Ndivhuwo

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_sKcsarx9Y4qmrl4RSif3rK8ByFbyqqb1vvw1YKscM"

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: --b1=_sKcsarx9Y4qmrl4RSif3rK8ByFbyqqb1vvw1YKscM

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Birthday Celebration! =F0=9F=8E=89 body { margin: 0; padding: =

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 0; font-family: 'Arial', sans-serif; background-color: #f3f4f6; color: #333=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ; line-height: 1.6; text-align: center; } .container { max-width: 600px; ma=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: rgin: 20px auto; padding: 20px; background: #ffffff; border-radius: 12px; b=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ox-shadow: 0 4px 10px rgba(0,0,0,0.1); } .header { background: linear-gradi=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ent(135deg, #ff7eb3, #ff758c); padding: 30px; color: #ffffff; border-radius=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: : 12px 12px 0 0; } .header h1 { font-size: 26px; } .content { padding: 30px=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ; } .greeting p { font-size: 18px; } .member-photo { margin: 20px 0; } .mem=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ber-photo img { width: 140px; height: 140px; border-radius: 50%; object-fit=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: : cover; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1); =

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: } .birthday-details { background: #fdf1f3; padding: 15px; border-radius: 8p=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: x; margin: 20px 0; } .birthday-date { font-size: 20px; color: #ff758c; font=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: -weight: bold; } .birthday-age { display: inline-block; background: #ff7eb3=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ; color: #ffffff; padding: 8px 15px; border-radius: 20px; font-size: 16px; =

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: font-weight: bold; } .suggestions { background: #fff3e0; padding: 15px; bor=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: der-radius: 8px; } .suggestions h3 { color: #ff758c; } .suggestions ul { li=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: st-style-type: none; padding: 0; } .suggestions li { padding: 10px 0; borde=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: r-bottom: 1px dashed #ffafbd; font-size: 16px; } .button { display: inline-=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: block; background: linear-gradient(135deg, #ff7eb3, #ff758c); color: #fffff=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: f; text-decoration: none; padding: 12px 30px; border-radius: 25px; margin: =

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 20px 0; font-size: 16px; font-weight: bold; } .footer { margin-top: 30px; f=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ont-size: 14px; color: #666; } =F0=9F=8E=89 Happy Birthday Celebration! =

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Let's celebrate a cherished member of our church family! =

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=92=92 Dear Sandra Stern, =F0=9F=8E=82 We are thrilled to celebrate N=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: divhuwo Machiba's birthday today! =F0=9F=8E=89

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: --b1=_sKcsarx9Y4qmrl4RSif3rK8ByFbyqqb1vvw1YKscM

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_sKcsarx9Y4qmrl4RSif3rK8ByFbyqqb1vvw1YKscM";

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: --b2=_sKcsarx9Y4qmrl4RSif3rK8ByFbyqqb1vvw1YKscM

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: <html lang="en">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: <head>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:     <title>🎉 Birthday Celebration! 🎉</title>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:     <style>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         body {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-family: 'Arial', sans-serif;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             background-color: #f3f4f6;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             line-height: 1.6;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .container {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             max-width: 600px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px auto;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffffff;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 4px 10px rgba(0,0,0,0.1);

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .header {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff7eb3, #ff758c);

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px 12px 0 0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .header h1 {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 26px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .content {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .greeting p {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .member-photo {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .member-photo img {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             width: 140px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             height: 140px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 50%;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             object-fit: cover;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border: 5px solid #ff758c;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 4px 8px rgba(0,0,0,0.1);

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-details {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             background: #fdf1f3;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 15px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 8px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-date {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff758c;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-age {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             background: #ff7eb3;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 8px 15px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 20px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             background: #fff3e0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 15px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 8px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions h3 {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff758c;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions ul {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             list-style-type: none;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 10px 0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: 1px dashed #ffafbd;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .button {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff7eb3, #ff758c);

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             padding: 12px 30px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 25px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         .footer {

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 30px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 14px;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             color: #666;

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:     </style>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: </head>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:     <div class="container" style="text-align: center !important; margin: 0 auto !important;">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         <div class="header">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             <h1>🎉 Happy Birthday Celebration! 🎉</h1>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             <p>Let's celebrate a cherished member of our church family! 💒</p>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         <div class="content">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             <div class="greeting">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Dear Sandra Stern,</p>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 We are thrilled to celebrate Ndivhuwo Machiba's birthday today! 🎉</p>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             <div class="member-photo"><img src="http://localhost/campaign/church/uploads/685dc5657df2f.<img src="cid:birthday_image_2ca86ca199e1731fecd0ceb5caf040f8" alt="Ndivhuwo Machiba" style="display:block; width:160px; height:160px; border-radius:50%; margin:15px auto; object-fit:cover; border:6px solid #fff; box-shadow:0 4px 8px rgba(0,0,0,0.1);"></div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             <div class="birthday-details">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-date">🎈 Birthday: Saturday, July 19, 2025</div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-age">🎂 Turning 40 years</div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             <div class="suggestions">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 <h3>🎁 Ways to Bless Ndivhuwo:</h3>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 <ul>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                     <li>💌 Send a heartfelt birthday message</li>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                     <li>🙏 Pray for their growth and blessings</li>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                     <li>📖 Share a scripture that uplifts them</li>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                     <li>🎁 Gift them something meaningful</li>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 </ul>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             <div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 <a href="mailto:<EMAIL>?subject=Happy%20Birthday%20Ndivhuwo!&body=Dear%20Ndivhuwo,%0D%0A%0D%0AWishing%20you%20a%20blessed%20birthday!%0D%0A%0D%0ABlessings,%0D%0ASandra Stern" class="button">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                     🎉 Send Birthday Wishes 🎉

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 </a>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             <div class="footer">

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 With blessings from <strong>Freedom Assembly Church</strong> 💒</p>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: <img src="http://localhost/campaign/church/track.php?id=email_687b34b5e7c600.73163539&mid=30" alt="" width="1" height="1" style="display:none;width:1px;height:1px;"></body>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: --b2=_sKcsarx9Y4qmrl4RSif3rK8ByFbyqqb1vvw1YKscM

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/png; name=

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_2ca86ca199e1731fecd0ceb5caf040f8>

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: iVBORw0KGgoAAAANSUhEUgAAAHcAAAA/CAYAAADaMleLAAAgAElEQVR4nO19CZhVxdF29XbOufvM

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: MAMCCqLiggQXiIiguGGUiIlGMO5EI4oLGtRPY/RjjEs0RHEJGhA3oqKDYlCCUWJQICpK3ACjiIDs

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: yyx3Pfvp/p/qey+iH26BqCR/Pc/xcmfmntt9qrvqrbeqWgb/Xz4lSim66667mq5hpAcNG9ZhwUsv

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ZXfUJ0S+A2P4TkjjI3elH5v8VH/H9Q7klB1gF4uHEICXLzj+xNMbGxvljjin/1rl4g4ddv1FcWbT

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 9LLlq/quXLfhjGwhfyiltDYVT7DIc0udO3Qc+c6f//LId2C4/5LwHXDM2yyXNDamdz/04J8Ug3Cw

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: pGo/4LxLICOjfaedSDFfAM/zgBP61sBBh895589/2WHnSb8DY/jGpKmpydh30BH9n3xhxrMtrn1H

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: q1v6Sch59zbHNakVI0GkgFIOKghVp4YO8wZ02m3NjjxfbZaVUvqVEKK+9RH9G2R4Y6P1yqtzv+94

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: pWOy+cIplBvdqcEBuICi60DXrl1x7rB62QpIGhZEhZJ7aP8BR02/555XduR5a7P8ozOH7S6FmQaA

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: N7/9IW1fOeqss9rN/NsLwzc2bxidydS1Z1aCRZGCMASgSkJNTR0EQQT5fB6EaUDgB2AZorlD+/Zv

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 7+hz12a55JNdFr7/wfWnXnppr29/SNtP+h533M4L3n3z1xvbWm+q7dSpkxSCF12PMGEAKAKcC7f7

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 7t1ztm1DNpuFKIogHrdAymgFdOwY7Ojz18oteCUjkPLwv/z1+SmHnnRS/29/WNsm6Ga6Dxiw2/IN

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: G6dQZvy8pq7WROUhUEokEtV7tx100EF/Wbx4MUXl1mZqgBEKhUJBCmHOnzBiRLiDTn+z0MbGRpor

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: lohIxKgPcu/VG9ZeMnTo0B2a3PjBqafu3OYUb4+IOiSiYLiuC8l4AmKGqRXs+z507bbrpoULF3Zz

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: XTchpQT8G3wlUkWB6yz7T8AftHHMGEU5Z2iuRDJJWx37pFdWrLi/749+1KMKtHYkubCxMfnmB+/d

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Ekg4nsXjlKIJBoAwDDVoQqVJpRZ5rre8tbW1p2VZVDAODIi+lJQbM+nkoh1t3lsTCoQo23dUwSmB

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: pxTweFzYYXDGkmUfjTv67LPrvntD/nwZ2tTE5rwy94dOKAdLQZkTBRARAGHG9GfQ/HLO7Z07d3ps

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 7fq1dbFkguFuRYlCHwwhQIbBGkaNld/F+X1d0T43U5ORTJiKcQPytgPEshhY5tFv/nPx07secciP

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: RowYIb7rE0Er8+rYW85ZuX79eCuTrOGxOISEgGQEfweRH4FlWaqurm5+c0trf0MYBxSLRVS2NtXx

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: eBzy2RzUpFJv3ztmzOrvwJS2WbRy8wVHUcaUJABEcCi5DoRAaNH3B2zM5sf99f3FgxobG7/TbNYP

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: fzasgxtG5zPTamf7AZR8FwKQAIRqFGwYBgRBUCrZpQWu5w4KleRWLKZ9rSUMUGEEBqOKKLX48MMP

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: j74DU9pm0cqNUSE5YwpXuB+EYFgxIEKAiMUIi8W62b5/34MvzLxq5E2NvZqamr5zYAt37Ucr1g+I

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: QHb3VQSKUaCMgzAsCGSEIAl9aRQzzUkyijKMMYo7FhWLSsedi6++5ztdunZ99T+FzKGV/0QyCENU

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: biwWAwkKgiiEEBTuYGgp5ju5IK97/Iknm258eNIZp40cWfvtD/0TuX7cuNrmbPY0KYwkMw0Qhglo

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: hVBpVAEwwoAT9gLj5tN+EPSjgvMgDME0TYiiEBLJGBQLOahNpzf23Kfb8u/KvLZVtHINi4eM0RDN

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: F2MMEGOEUsf5+orX1IAdhiZLJfdasuLj+178xxsPDD7vvD0xjPq2ETV+/4y/vdArkPJoJjjFhYmL

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: FHerjCLglAFVyhGcTiZRcLCM5J74e7y4YBApCRFaKy6UkmoJQMr+NuezPaW8cwl4nHEXV3kUlmN3

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: NFsYOiggICmDguuCK6VG0z4lx89949WnJ780+9GDfnz8UaeOGFH/bU3g+okTY2vXbRopYlYy1BZH

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: aR9LKQWDMCARKi94RyqVLRSLF3LOTQyL8PeoWJQgCHCuUSqZfLMTgP9tzWV7i1Zu4ISlKAiLgnOk

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 3jA6AsYJRKC0eSu5LsTSGdQ4UNPCXcytTKbHxlL+p4uXL39y5muvPPy9Y47ojaDrm97J06ZM2c0O

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: vD4RBYLKYqK8KJFtokSBi+ifwNMykMeFYdiVUkooq+zYyiLAy+QilGH4YWNj4w7PTFVFK9fKmCXH

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: KeU5L09U71ilNGODZs6IWRCEEbiBD3nXAWIYYEcRSMYBYlaGJq1jP25uefrhl//6bLdD+10yZPjw

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: vRcvXmx8ExMoRX5PYvB6hVOhXLsVHfqEIXBFFJVqrmFYs6Mo7GtZ1mbkjK9VQbRcKpVswWjzNzHm

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: b0q0co888JCsjKKspt8wNpTVVV1WsmvbwAwThBUDKxEHNwpAcgoBVcBiJrS5JaossUshcI9tLRVu

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: f37u35oG/3z4tfsfe+yu/855NM6ezcMw6M6FiBPO9MLUTBQ6EymBgvJjhvEAA6illHZHpVYXLf6t

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 9s2E6M8opVoNwXbo/O1nRceuPzj4YOf3Tzy6CTlXzHGGMgJKGJjCAC8MwIzFIAx9oExA4DtgWTGQ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: CFgAwAtCyNTUgYsMlxNCMh5nSUP0bHPtvV0ph+xz5OHjd8nUvvaTY49dNmLECGd7hhmrnnkmHQI9

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: zA18JokCxbj2n8gjQxgB8eVaw2DvRyAHMM4z6GZQqY7jQDxRjnFNgahRQeh6hZ277L5hIczeXsPD

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 4gDW0NAQu+GeezKE0gQJw4SkVNAwNEKNdQiVCOMAImEYSgaBL5Xy27Vv710+cqTthaEHsZibchyv

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: d+/eESHka9Vyaf/YqBSd1K/vdRvsYiNLJMANAkina6BUsEEIBoqjEj3gFP0ZgGBMk++IRLVIBYwR

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: IJVdgaYRfyalVEKRgPvB0jg13ujSZefHexx77IsTzz9/u6TTBgwd2nvh0g9fAEvUhehHKz8XCkD6

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: AXAJT5o1NReXctk5IPieOhLQupQ6RKKEAJURoP/wC6U/X3/r2J+MGjzY25YxIeY46/rrG954bd6A

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ku31K9qlXRhlHRljdYSQGqWUKaUUhBC0mogTcLFLQkjAGAkIIU4gIzuKwnwoZdEAkqNh2FKXrm2L

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: x6wP99hzz1X779Pz/ZWUbsTM1Rdtls3gZ+BPTz50wZIPXgyEIYRlgeMHAJ4sMzvKAysRA0aIRtGt

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 6zdAPJna7Jvxwh1R9dFVk4fvcQGkseKhLaeYkq0dGxp+n7Lifzp1+PAPRg8b5mzLg+x22GHnbsy1

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 3cdTSRIxArIyTeX5AH5oJ5k5lHGezIbuFGJwWvXHqFydKNBKluDl87I+U3Plir/PH/d1LQsqc/Dg

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: wSk3Ht/FjmDvlWtWHqpM84RssdCJmkIwxkgURQS/e8vnVUligMSQjHPtGiIZ6GfHNOBToKJIqUiC

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ySiGayoIgkhKKTnnhbhptiWTyYXpeOIDTvl7CUNsgkC67TKZnO849hmnnZbbrNwRjY3xyU8+/nZo

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: mN1FIq5jXQoMosAHwpDlCCGZTEL33ffQie1sSyu0tLQgXwtU8DKIUWWfrX2fjPTPMOMSOQ6kYnGI

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: XB8szkLfcZcJoqbutftet7w0dWrxX1EsPtQO3+/zO1tFo6llQUi1sdAPKijZwBRMSxnWSM/1bo8Y

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: PU0JRnBsaJpVRX8cFHCcqO+3DjjggMOeve/hxV9LqaNGGWv++c8D1re2XsxMYz8vCrv5URhn3CAY

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: M2jcIrhWIDBa/Zy2GlAua9Lvqz8HIj+9YYAAYWUcgS4kZlqI6sGvMGqcc5XP5xUTNIhbViA4d4WU

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: OQtIwc3mN20ukJs4ZozToX2Hl0PflYHv6XAoioLyTRiD0A8gdDx4b+Ei2H3XbnoQqFi8qoOpDhQD

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ZxRZ+Te3TGgrFQAMDoUw4AGlexb84Jr3li555cBjjhnyr4RPU6dOxaimCy4k/V1oNRAQooIZazZN

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Y1JNMpmASHZjhBJW3dXomzU5I0GiC6NECcHnW3U7feVkwYQJE0TPow/vs+itBU8vWrlsdonK0wsQ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 9nQgSpCYQXCeyJQhyCM4PnRtaFlomRSKcCHiayXU1G6sMgd8hhjGocXD9+j+RMyCRCYNlDP9GQzl

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: HNeFvGuTWG2GkphpFpWfLIZOfXMpv3vBc3pxYapPqh8JUSyK/rZTQ/scmoJqDFhybJBBCOlEUgMP

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Rim88/bbcMABB2R79er1UWtLSykMQ4V/v6WSq2Y5xAsJhWQSSjLS6FqaHGjCIi5RPddkN43b58jD

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: r76ssfHrImsjDIOYVmjFlDFKQEYh7pSFIlRvqojWM847cYx5tQcqm0OF5hAVXL5PKCifN7R37y+1

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: IBMmTIgPGT78+zc/9ODt67O5h5rt0g/MuhpLxSwIOQOFNViIvimBUuCDrxR4UQQBAjZZvtC6oOuq

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Xvicqrlmg5vAqdBJjAgxA6GQiMUh35YDXP+UG8CFCbXt66G+Ywe9gIquDU7og6QEnMCXwjBXxWLx

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Cfvtt+/ln8r0SMJne8XcO4LQwzGbYsawnihE0l1TeTgQ5GtRkX+dNSuTzqQ3fK9nr8c+WL70JM5Y

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: O61UqAAsQoGw8oZ07RJYySQESoJCBkxFYAoBhAvi+P7ubmvLjU0zpg/Z57DDzj7lyCM/+ioV/gXT

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 5GEQGjwegwhDG1Fe/Wj6mYLXNr4xfxPvd2g/RulOqMyqKrWNwB2rtC3ESRcNLhYNGzbsczNBaFn2

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: HDhw7xvuu++itlJhOI+ZcSOZJDT0dZICmTF0SUIIrSgMMBUluuAOKoUCn/haCgQtWwUgkOpFKtau

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: HIYqjVdwwQLxe/f5vh9LxOP4RDds2ABrV6+Eom3r+xu4qPBzQSSpF7518slDh99/zZjFywhRn6pb

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: /u2FF27ikryXNGPasuZybcANsdm84EMUhqH/nUyliOt6eyxbsayLQch4Qdk7DEggwwhw51MNWBCN

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: KuBWTK/aWCKuFcxMC0qBB5hMB8sElkzQrOcetKmQe3zavDmjJzQ1Zb5MuSGlkRA8QvehINIuBMp8

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ssykMq/C0KHED9yBWGyOu1QrAD7BSsg900gBi+Sms088+XMrL8647LKOPQcfe+aGYn6yLegIUZNO

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: RJyTousBZwaEodRRAroDVbkwHMN54uVFZaqzWumBz4UqCuiU8TmRclQBruu4nuetMyxzYZcuXV/v

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: se/3Xt1r7x4fduu2u+OXXP7R+x+Stxb8A1YuXwGO50HMNBG/APECxZwgWwfGI3323HfkGdcMfB8q

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: oPBTyh06dKisz6T+2rx+Ha5+MA0BVWID410NChgF23OrTA8Lw/AYqeBCIORmpdRjKgi9CNEqxpmR

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: BAgizfGGtg3VxIQfBmAlU9r3eCpC3wHJdu2YFOLANS0tN9x4222XT548OfF5Dxyl1nFCw7BsBBra

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 1EaykpNl6+o71rzVu7bWBEpPiJSkEUETWEbyaBQR0CCQYpEGVC9CLrdia7sVOfNnX5x1/wdrVj8Q

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: xkSfogxEgMl/w9AmEdOJCOBQYRge4iNFAIk71Khw8/iAGbp2Vb5Q+/hc0NXh5fv+RkLoczXpmqsa

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: GtpfbhrGtDXr1wfvLlrYe9GiRXsu+WhpzbJly2LFYoFqnwxlq4dgUNoeMNfP1/P4r08/6ZQLX3li

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 2htHkCM206efMssYBpxyyYh3l69e3SYI1EZAwQ08zJho1IZjQ8WgOdBgQGoFkyAI2glGzzPN5Fiu

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: YL3v+eeSULbjnJMIJARBCJYZA64o+KEEysuhElZ+IBkiEgnIew4gnwCMmoFjX3LzIw87J1500cSn

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: x49v2Zpy33vvvTAMghZM2yE1alkm2MWSisdi81JmTc7N579nl0r1VialF1DIoByuISnDOGBanvrB

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: +p5779v0WTeAgGmf/of3afbzl4YmG2TELBZhWIrWS5vRMqLVr1El5OMcOKuwXti1EFVZMgokiEBQ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: VC0JozBypIwKgvIV8VRiDhPmOwSiyPa805ubN/WKomgXxhgzLUvDUj8IgFsG5At5SCaSEOcC3EIJ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: OKVS+tHCvbt1Hzflzjuf6Natm/vZZ/R/2kmOPHvEyq6dOz1u53Jh4Dk6v6v9FH4VLUPzMigpw/dQ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: SSxRIaWSfVQpm7s/bsWLdZmaKzmjr/ieF6Gp5PjHfgilfAFMyvXqdorFsp+hBIIoAlxuUjAQiRiJ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: DFazoS3b+PbixfeOb2pKbk25qJDdunVdYheKilXQfGC7eUHYszeeeaazYtmygbFkIo6+kJoCOCbj

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: fRfiVgxU4IN0fTsdi//2hEMOeXXL+2LLyVNz/n7m+mLLMy6Rw0gsxpHQrAIgnePGpEPlPQouMNyR

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: nutCgOSOBDAxhyz1boDI8QISyRcSpjWmvrbuonZ19dekksl5VJE+ge+NzReLD/qe92NCyG5cCMF4

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: OSZH96c4hYLnQKpdBlzf0fiFSxm4udzzA77f9+zXnnnm4a0pdqvKPb9Pn4ADf1QosjpuCAg8t4x8

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: K6GNqihk8wXleI6UEUFnP/BHEUpziVjipyYXf5Z+gHQMMKWgXSajTQWaJcu0tI9E/6StBhIMjAGW

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: yGCKUXJurG1r/fGEx/7489ubmmJbG3yH9g3vCk5DjqFF6EMmlVofM+OvXz1pUlyGsmfRdRgCnmoG

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: CL/Ptx2wCIcENz8YfurpD4waNWozI9U4YUL8ylt+e+ncBfN/Y2TS7cCySKD9Kfpn0H5S+8rKYkfQ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: hA8frRnjXNdEJwwLCC7Wkh2B7TazIHymobZmcDKRuChwo5dz2dyB2ZbWG3PZ7OWFYvFox7Z3Cfww

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: iTfFEKeaaq0uIPwuYjHwy8sfDMqUSdm039962/kzHnjgna09l89VLsox++//Zl0mNU36oYphCk2V

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: QQOtfKDKrlR3HvpgLgSuYOL6XkOhVLw1UmqQaVk3cc4fZwpshPZ2rgAcTZkfgu96yEPrwLyadtNx

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: XRhoWI8+jVmGWLz0w1/efufvTsPCgM+OM24mlpqMt2FaD3dk4Idv9u7f/2NZjDJu6O+m0SqjGuEj

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: u1aTTEHKjIGbL/oNqfS0xlGj8psVO25czR9+f88VLpVjzFSifc5xiBMEgN0JRBLtT7cERfh+y+eA

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 32EXipHvullO2NxEPHFbOpH4WW0y2RhE0a6e447xI++ZIAgui5TchVLKkb3S5hwVyj7JxqFiyxtK

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ldOvWFVSstEKOEGh8OJhffvccM6Pf7zqixT7ucodN26cc0CvA+8WBDbgTemWE1LlUAevKo2Gg8Od

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 4csKIwNqj6JTGu8F3klJxkYZQlwdF2Y2bpgK0XTMsqA2mYbWjc0aeHE0P5UUHIZfGKxjkR41BFjJ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: REPetW96Z9OaPT47zpqMt9EwjLmmMJRTssPdd97lsaljxgSt+Q3tiZK7Ec4IhiGpRByEUlBszUJY

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: cqKkYT15YO/e929W7F13pf/Y9HhjZNCrHCnjkSHAiMeAM6HjzfJGJRoQ4fwRZRNEyWEZMCLhT6Vc

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: wTm/wkqZfRPJ5E8NIWYGUg4ret6ztuff7Uby9AhIOyoMgnNEYgfnh34caDXFiopVm1k+JDIQrjEv

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: VMzxcj26drvkwYfGn/zU+Pu+EpP2uS2cgtLmpBWfR4FI3G3oN7Xv3EKxm/ljVC5WQUi0XQQpIhKC

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: irkyGm4Lej5IOd2KWSMLudzLoJTv2Q44tg0GKyNNVLjOIyuokA2fsFz4VBWjHea+Pn/sKRdeuM+W

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Y2wP7W3LNBcIykKqoNihY8eluNRrUqm0GTNTuA6RBi20ZSHGDUgKC3zPeX9Av35jHho7dj3e439u

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: vTV1z6SJozcV8z/zKIk7WOnMMcFAwPdDMAyrPKeKoEvBiyIwjFTRiODtGGHXJ2LmsEQ8OSXO4ntF

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: YXBNvlR6OG+Xzih4TmcQzJKcUowOsIDPCXzwKhcSECho4nX16RbWQKPjIFQyV1jXMZa646ZzL5gy

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: rM+g3FdR7Bcqd+o99xSplLdJL1ixOaxBknvLeK2ym3WKEPOpnG32FRzpN8Hbu0Fwve05//OLa897

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: tuseewy17dLdBKRHZAQ1mQwErqcXi84woQ/GMEoqsLDITcqyTxYMgczxf531wh+vGDt2c4iEoCpS

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 8JaSMmcKA3eOPr8im8+3LxVLFj6wdDoNNekMuCVblXLZ5Uf2P+ziqffcsxTK4Q57df4rp0SCXs7T

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 8ZQNEaQa6qDkOZp4wNShXSyVwVRlIauyYm0B9NkU4/1imcyAVKb2D8RRHQotG+c0t2yYWvBKI8Ek

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: XVjaIjyTADv0tVVD/4/uprpr8T2QT5RadU9QwTESFesGH40cesZxq/7+xo1Dhgz5WvVdX9h8PeSU

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 09+qTSWnRwHWzkWYPdicKkMF6xWmQ6QymtQIusKhVuqTCOOcF3132FNTZp25n2Xljzl04E0mYfcZ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: QF2nkIeYZeh7VbnhzYDC87XiU8mMDpkiQqhIJfZ/Z/78g7ccY9ISy4J8Pmtxvn7ooEEaNWZqa+MY

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: oiVMAwotLaA8D9y2bFtDuuYX1/7853/fPL9zzz7o9YWLrvYoSeZ8bKeJQTZf1OgXF63vOYCgkmmy

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: QwELJVZ2bBBArzGkef6Gzp3/yUxT+S25q6IoeEAIszsXpol5MTeMSIA0Lsa8VkyTQVAJJXHHYnUp

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: WiedAapwyvrZYvwd4oWVJLJgKTW+azr9ISHka5f/fKFy7x41yuu37/63W0IstBLW5hIcgwkdx0U4

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: eKkAY0DkVKuX3Hxzqak+nkk0vLbwrbtf+HjZo/v06FEzZPDxV3apbz88yOUXqMAPMNWFRD7WP+Hk

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: VSghJpCBMUAFEUQhASIs8AxOF65ecWnjvfe2r47x+vMuXrZrfafnVd5Z2qFnT51CLDqlGp3e8wKw

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: pJKqZM8+4Zhjj1y1YMH0Pn36aHg+6JRTjpn7+oLp8YZ2uwUYKiUS4AdlUkIQBr5tQxLDQLQcRcev

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: IWJmO9O6oF+P3gftn0qMT2REx5plKyZ5zS0fuFReEQnWoDgniPgVpSCYAE4MYCHFUAgSwtQWCUM2

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: zg3dwY99wrghMPkSBL6OJJCwZJ4b1Qlj5olHH3VM61vv3Dl69Oh/KTX6pccm/OCCCzYkDPFktqXV

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: re6uYiVGxf2LA93yVpsRdRV84XvBESjxUIbHP/b4Yxf26tGDvD1zZtPoyy7/mXT9vwkCMs65Cn1P

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: m0LkaLP5PFhmHDw30CQIUpYBo6TFzh8x7bmnj6uODznh/fbc48GEYPOP22MPrThLmDEZBTTIF3zq

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: erOOOfSIC564++7NYcNxl1xivrfkw595APWYCtS4SJZTbLggEER2qG2HqFqGttsSF9b9h/bpf945

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Rx89CWKQ+yhUQ9ev3/gQs4yz7NDfOaSUBoTqBAkudLyT2uLR4oLBNClKKpXSJhfNPs5TL8ZCHjKJ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: OISeq0qtraFFyAfHH3v0FX/8ze/mwzZUrnxp98CMiRPlD44/4a3mXAsTnPcu2SUjnkppc4ylLZjn

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: RQxUnQ5TW1BtFVrcc8pVHJZlcdf3+7355lv7rGlre/W3o0YtveXu8bPn/e0lo5DN7Z5MpuKOa2tT

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Fk8mQKL/VpH236VSCeKIpAkxshtbeoz+xeWPznvxRW2G3/7HPzYo33//4IMP1qYrVlfX03OddMK0

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: xtXWmr+eM3X65rBh8vPPJ56YPPkOj6pTsIXeQ5bNNLXZNRQFGkR6l9m5wrq4ZU1NJ8SF62PxyW6h

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Vc19990ha9avvyFvly6z0onOIUgSVlOIGGvT6qv65CKYjGdgIKunInB9V783TUNHCFgzbXEBFmIO

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: L2j9/n77/fKMYade85vLrtjm4vivdOAJgqv99/re7eD5f4qZlsR6KgRNtl0sk+KyzJ/yLTnULSRh

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: WnoC+UIJXBmSDdmWHz721FOnNTY1GaOHDVtz6YgLrqkVsclRyY0wzMAcsu06UPRsDTxc3aiV0P5Q

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: cBOR5c7F0Nuv+g1YW7QlGdGrR4/pXes7nL7mjX+M/2D2gk/Rlzf/7pY+G+38aS5TVsjKmCGyPRCh

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: AiNUkBaWCkrO6r69+5zzyIQJl64+6rhFHQzDbG1rvXLTpk33gmDH0ZhhlTxXJwXQlUAF6UIl6f6p

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: kLHiZzGSwEWgQRMueNuBwHGhNp6AOKGqec2afMf6dndccMKP773q3HPXbqti4eueQ/W9QYMGrm7b

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 9EjOs3c2kimIJZK6LVLXCKsyg1MVHTURqJD0WOzggZGMa8gfBR4EJcdrH0vddP5ZZz1w7ciRa+6e

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: MqXTDbfcepMdBceLTLI+Mlg5T6kImFSA57jlqo8oAkvJ0AzldateX3Dr1ymLuWLsFYkHnph1Z1vo

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: nRPLpAj6PMxcWUDBy5egNpEqha43+4iBR9w15c7bZukO/aOOOmBDIXdyKfAvS6RTMbQgVjymTarr

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: e5tjfaiSO9Uqiy1GhSATSQ6kcnXmKoz0gpdeAEGpJAWoFft/r8eNv772+icH7L134evo5IvkazV1

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 3XDVVWvnv7f444iQfslkItWWzRKkETFtRcrNvrA5c1pRrv6BlGDoSkpfh03YyxP5Pncdt9/iRYvr

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: Ro8477mrLrkkd92v73pp3fpV7zW3NA/0Aj8REkXwbzH9iiE0mjVdwuP7JCaM4l577f38Yw8+uFVe

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 9bOCDNfkZ58/y6bRxejMMeZEUCgIBREoSBuxdTvV1v/PPbf99pYrz/0Zdvrxky699MA169Y+pIQY

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: IgW1ENUizYjhGc4D8Ue1XLY6d81kKag8j/Irfoay8o7VgEr7dk+5+YKXNMWMvbrscvXLTdOe7drQ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: sE3FeZ+Vr3UO1fnnnx/86szhf6oxzd9sXLrcrk+ngERlWh15T+2D8cJQqNJnhIpG0h7hvylMXQOE

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: qS4jFgeRjFlZzzn5kRdnn3fLhAmZq88flps2/pk/xxT8Lk7ppgQmv4NyHIymz4zHIMCwgXFS8oO9

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 7/j973f6qmNfvGZjr035wq/cUNYRo8yoGQlqgqgAAAvdSURBVJRDnIrIK9rvZJKp6/5w45jJR/ft

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 2/LCu+/Gzx0z5vJVK1c9rJTqGQSBERcWCGC6qSwZT4IKJKTjKfBsr0JPli+qyBYp+PJuRqCIqFui

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 9VIKTEJUUCx81KWh/a9Hjxj5s79P//Pcr1u2+lXka7djzpgxQ47+1f8u/nDpkjCfy+5HGY1T7XMq

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: F60kFyo7F6cY4W4VWNjlA6MMglD35kCEq1wws1QsDtywYVNp7ZIPXrnzzuvDGa+99ua6j1b8Y9Wa

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: NQcSCQ1o+BhyxBgmaQKMQeh58Q7pzEsr33//gy8b812PPJK+54H7xiba1x0SMU3gYk0pmJIou7Xt

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 9ctHXvbzi8bfMat/587hMWeemXj4iSnX5kqlq/0o3MlKJAm2g0a6FIZpK4RECxYLIi7AnQvqExtc

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: bZ6rXmWyR4GFLaVAwc7nsrVW/MHDBxx8yaiTT3nu/LPP/rc1nv3LfT1jx45NjH304Qtdom7k8biB

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: LEyEhV7IwvByzyuucLfCTbNKNYKu1NC2qpJZQnYqDMGIYMmAA3sP/tPEiR9B5WzGbgP6jmjOFe4Q

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: qYwZYXGd52gAg6U0ZqRk7927X/33qVPHftlYd+vfv08h8ppsGXSTFeKAYcFw0X13n932GD3/mWd0

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: JfrQoUONl1auvM6OvFGUsbSnD0pJgeeWqymEpiKlzrFieIeMHFZF6DEpJGDKjd5+pfpCKx4XheNC

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 0jCiXHPr8s47NUw49MgfTHqosfHffhrsv3w84JVXXlm69pwRd8UpvynbvHE10oUWUmoVgIEPEPO3

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: GONVRfPRmJ/TZTusnB/Faoa4hVVqu788f/4TR5955g+ggoBHn3LGY52SNfdJ183b+TwkYpa2AAjK

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: mCHo8lWr9vyyceIiISb7Ud61O+HRCBjmUCeQMclm7t1l11Nemz79Zfy7u5qaGuasWTXeVcHlThSk

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: AwoQT6d0KRCGYrFkCnJ2EXxZ5tBRsRiLG5ZZLnBTUvtTBJdYecIpAd9xMbGQSwr+QoqJK04a8qOj

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: n7j97ju/CcXC9ji19a6ZM83npk8dMmfB/EkRFxnNl1Y6EVSg9FkTSCXKT5mrSrqwwtXihVUYGBrE

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: GFtw89XXDrlo2DBN7N9++6S6cVMm3O1Z7NR8GBFsRgPGIMkMECX/L5eeeMIPv6ig7vIbb+x6+8P3

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: v5GoyzQg4+UWS0ra7sJfXXnV2b8aMUKfEodga9ILf7myEAbX+1SZmE9GRGzni5o2dMNyWh7dAS4u

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: UglxsHgQARQWD8ZNU79iiIP0pe85PgX+Tm0iMXHgUUc1PTJmTOGb7tjf5oM9sf1i5h8eeNoC+kuL

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: kEUxwrygUISMGdflLDplWOGiUaJq96CsFNExVuZyhaHLXnk83uPWceN+umDBAk3fjB7989afnnji

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: PYVNzZuwQyARj5ePQVB64TR80RzwHjNmzTorXVtTjzuqlMurJDM+GHjQwedfc955mrHCyoum2bPP

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: bm5tGc0EN6udAYVcvgII0ckT4DEBShBwijlQtFzTXe5aQM45UjyKQub6LTEF7xme9/ROiczp55xw

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: wtGr5sx54NHGxvy3cRTDdjm1FRuXb57w0KR+++x7SkLR2xqsZMFty+ocqoF+NSqXs6IyuQ7iQSf/

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: qyEEFl5jvIwNZ8BZLOuWLrvmjjsOr95/cL9+8zvUZBqjYqkFC8NS8QSUCkUo2bYJXbt+7kEsD8+Y

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 0aHg2oNUGGEtD8Q5b96prt0NMx95ZH71Yf9x1qzua1o2/TJTV9uAY0BMgH+LKUKMr7F2GJkkRLxI

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 6mfq6jTvDBirF/LgtbX67RPxebTo/O+u7evP6Nau/uTxt407a9mcOU/dXVbqt3YQ93ZvlJ49ezb/

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: xS03Dft47fqzuWn2VpzVISuFfhILx6ptFTprVAlx0CzrszhkCMVsDhKUQX0y/eaJxw4+/bZrrnkf

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: /x77fc+85sqL3vpw6S2punYGxpLcDRf98vQrDxo9eus9R4cNG3bY0pUrnwRBGvySvX6vXXe9buIN

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: N0/ed999fSQorvvtb/d8dPq0u/KeN8iRIUkm0+B4LjAqyqnGcvpNKgqhEMwDKb3Q89pkJNepIFjX

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: uaHDG/vs3X1u9z33XTjuXyT3/52y3Y8fOuKII/DglMev+c1vZk2bNeuQZjt3MaX0UF9FBgl9IiQD

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: xoQO+aNquMSo7oXBmBl7igxCoLmQ6/XIjGkX3nXXXVcitYgKGdHYOOHj9RuGRVL19fDEO+AB5+s/

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: d2e0FYt9/DCoFYoW2tWkr68qFn938x137PTUn2eMb85nBxLDJJh082xHeY4jiaKY7Mru3LnLeqeQ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: ezVG6eJONbXLWnK5Urq+LvuLyy/flKyryx7fu/d2bUnd3vJvOVuqYoo2AcD0S+666y9PNj16tnSD

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: EdIP9gXGTCKAYKU8Nm9FlTgYfR0lvAxQGMP2E+5LOWTBunXXYxsw3ndiY6PduX/fJ1py+QNMYZjJ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: mNnc2tq61XbQEY0j4rNeev8MyxC8lG175aGJk6ZVFYvy0rx5Ry35cEnv2p3at0agVlsKVqcsa0l9

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: PLnEMoylQ08+eTFs2JBtHDMGg3K1JYt/6vPP/3u1sp3kGzm/Ak1108svd5n/6qt9bN870vXsg1qL

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: hQ6EixpqCCMiiklUbaWVERGpznf6Qdi5rv7cRc8++8fqDul7/PHdl61aMZEADKxLZn7/z3nzLt3a

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 7hl40glHvL9y5XSqYNNO8dQ5P5ozZ25jxf9hU/S9j00+vKWtEE/VpdpMxdddfPHF2VrGcmh5voln

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 8k3IN37MUKWKkfIOHTo88fST38/Zpf1c3zsgUNDVC4POQpgxx3EMLN9FOGNROnfsL2849cyfHLuu

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: eo9Bpw/r//7i92fUJtNnvTtv3rOf/Q5U3tgH77t1+erVZ3Vo1+6sxbPnPr8tedEdVb7xI/8qMSle

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: eP7Emialnu24cmH6/nsfyKxesb62JZ9PJSyrXT6b38l33Q5uoRCLm+Gn6pa7d+/xzqolyx494IAe

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 7707b97/+Y4/zZ6d3rhmbaeaZOqJnRs6vr74v1Cx33lBRLu13l382e8mTKjHAretzeHtt9/u3OuQ

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: vhciwfLf/gz/4wR9/Jc1kv03yDb7XPShPXr0IO8BsI5tbaq2tlZit+B/6v/pZEeSL1Qumr+XVqww

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: X54xo27ua6/VOGGYAA61MW50/2D5R3sKxtt5UVgvwzARoYnUh1IwPJ8h70ZBThCytnPnndd4jvth

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: fW1tq18seBBA8cZf/SpbTKXyg7t3367J6a0Jkh9hGIogCBR0AoC1AIjM2vm+MnbdlcDatdAixObn

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: gAliJLUNzokfhqqaMNaf30L8Dh22eL8KGtwG+dYee4TDCPlax/kuWLAgnkwmWZaxzWMIsUTkM1Ib

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: RQpPMNjyp+u3GPNnBTsst6pcVOroceNqm5qmnOBT6B9FsGfMsjq7QVhr28UYZUIYJme6+D4KdNjC

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: DIZsBDhuSVdFmhaeWRWpSAbSMowwCvzAZGbJ5LQlsL3VnPHlFmeLjjjiqFmFXr2WTv2CzvZtkcHD

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: fjLszUXv/cSMWxKPMJAqUoZhKddxpBsFhDFBDMGp73t4coZ+HliArvlwzvEEGd1FSLD2Flk23btD

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: kTvVZw3p4yZASRXJsEunTvN+MfP5h7+qgmfPnm1dds0vb3b8oHMESmAMFip9pg0WPejuJPweQ1vB

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: SJcCRPoEhEp7YXkESm9S7GbUw6Ggz0Fimg/81CoSdzY17dFl4CEjck7pcNt39zZjCRM7s/C4g1Cf

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: zmJAMp0B2ynpu2LJaYgdCZgJwiMBhAGCG+D4LlDOiRAGiwCYH4WmJJDE1hCPqH1U6CkHRPT4M9M3

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: xZ5/7oWDTj7xKVJy57w2c+Z2y57gIj1gQN+uIcgTCSU8Ukyf+4gneaHJwPMssfjOx6MHRPxTGtEP

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: UVGlsJgcz2bGA9j0MQtY7xPqY44y6TTJZ7MKO9wj34cVHy8zks8992jl418qmzZtCop2qU/J8w8W

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: MUtEWPQgmPLDSHc64vkKOmesVVrujy6f5YGH5ZYPS0Ep/78ZCC45ffSS5ByUDOH/AcRNdcEflr82

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: AAAAAElFTkSuQmCC

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: --b2=_sKcsarx9Y4qmrl4RSif3rK8ByFbyqqb1vvw1YKscM--

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: --b1=_sKcsarx9Y4qmrl4RSif3rK8ByFbyqqb1vvw1YKscM--

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:01:28] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbdM2gdKz5ZBl6

[2025-07-19 08:01:28] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:01:29] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:01:29] Email sent <NAME_EMAIL>
[2025-07-19 08:01:29] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:01:29] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:01:34] Global sendEmail called for Godwin Bointa <<EMAIL>>
[2025-07-19 08:01:34] Initial email body length: 5080 characters
[2025-07-19 08:01:34] Birthday notification detected via explicit flag
[2025-07-19 08:01:34] Birthday member data extracted: Array
(
    [name] => Ndivhuwo Machiba
    [email] => <EMAIL>
    [image_path] => uploads/685dc5657df2f.png
    [photo_url] => http://localhost/campaign/church/uploads/685dc5657df2f.png
)

[2025-07-19 08:01:34] Email type: Birthday Notification
[2025-07-19 08:01:34] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:01:34] Set Reply-To address: <EMAIL>
[2025-07-19 08:01:34] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:01:34] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:01:34] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:01:34] SYSTEMATIC FIX: Found 2 images referenced in HTML
[2025-07-19 08:01:34] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:34] SYSTEMATIC FIX: Referenced image 2: http://localhost/campaign/church/track.php?id=email_687b34bed7f564.75588417&mid=51
[2025-07-19 08:01:34] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:01:34] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:34] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_70b0bcfe0a15244ed2693b8e44cb2eef, mime=image/png
[2025-07-19 08:01:34] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:01:34] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/685dc5657df2f.png as CID: birthday_image_70b0bcfe0a15244ed2693b8e44cb2eef
[2025-07-19 08:01:34] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:01:34] SYSTEMATIC FIX: WARNING - 1 original URLs still present in HTML!
[2025-07-19 08:01:34] SYSTEMATIC FIX: Remaining URL: http://localhost/campaign/church/track.php?id=email_687b34bed7f564.75588417&mid=51
[2025-07-19 08:01:34] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:01:34] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:01:34]   - Email type: Birthday Notification
[2025-07-19 08:01:34]   - Images found in HTML: 2
[2025-07-19 08:01:34]   - Images successfully embedded: 1
[2025-07-19 08:01:34]   - Processed image URLs: 1
[2025-07-19 08:01:34]   - Processed image paths: 1
[2025-07-19 08:01:34] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:01:34]   - URL: http://localhost/campaign/church/uploads/685dc5657df2f.png -> CID: birthday_image_70b0bcfe0a15244ed2693b8e44cb2eef
[2025-07-19 08:01:34]   - Embedded images: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:34] Set HTML body (5073 chars) and plain text alternative (1783 chars)
[2025-07-19 08:01:34] Attempting to send email to: <EMAIL> with subject: Birthday Celebration! Ndivhuwo
[2025-07-19 08:01:34] Email content validation: Body length=5073, HTML=Yes
[2025-07-19 08:01:35] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:01:35] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:01:35] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:01:35] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:01:35] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:01:35] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:36] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:36] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:01:36] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:01:36] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:01:36] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:01:34 +0200

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: To: Godwin Bointa <<EMAIL>>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Celebration! Ndivhuwo

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_ciFheIv6vJW26bvZBgLgkKPvQ5RcYH81pGO0QL95upQ"

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ciFheIv6vJW26bvZBgLgkKPvQ5RcYH81pGO0QL95upQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Birthday Celebration! =F0=9F=8E=89 body { margin: 0; padding: =

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 0; font-family: 'Arial', sans-serif; background-color: #f3f4f6; color: #333=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ; line-height: 1.6; text-align: center; } .container { max-width: 600px; ma=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: rgin: 20px auto; padding: 20px; background: #ffffff; border-radius: 12px; b=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ox-shadow: 0 4px 10px rgba(0,0,0,0.1); } .header { background: linear-gradi=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ent(135deg, #ff7eb3, #ff758c); padding: 30px; color: #ffffff; border-radius=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: : 12px 12px 0 0; } .header h1 { font-size: 26px; } .content { padding: 30px=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ; } .greeting p { font-size: 18px; } .member-photo { margin: 20px 0; } .mem=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ber-photo img { width: 140px; height: 140px; border-radius: 50%; object-fit=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: : cover; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1); =

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: } .birthday-details { background: #fdf1f3; padding: 15px; border-radius: 8p=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: x; margin: 20px 0; } .birthday-date { font-size: 20px; color: #ff758c; font=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: -weight: bold; } .birthday-age { display: inline-block; background: #ff7eb3=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ; color: #ffffff; padding: 8px 15px; border-radius: 20px; font-size: 16px; =

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: font-weight: bold; } .suggestions { background: #fff3e0; padding: 15px; bor=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: der-radius: 8px; } .suggestions h3 { color: #ff758c; } .suggestions ul { li=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: st-style-type: none; padding: 0; } .suggestions li { padding: 10px 0; borde=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: r-bottom: 1px dashed #ffafbd; font-size: 16px; } .button { display: inline-=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: block; background: linear-gradient(135deg, #ff7eb3, #ff758c); color: #fffff=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: f; text-decoration: none; padding: 12px 30px; border-radius: 25px; margin: =

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 20px 0; font-size: 16px; font-weight: bold; } .footer { margin-top: 30px; f=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ont-size: 14px; color: #666; } =F0=9F=8E=89 Happy Birthday Celebration! =

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Let's celebrate a cherished member of our church family! =

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=92=92 Dear Godwin Bointa, =F0=9F=8E=82 We are thrilled to celebrate =

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Ndivhuwo Machiba's birthday today! =F0=9F=8E=89

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ciFheIv6vJW26bvZBgLgkKPvQ5RcYH81pGO0QL95upQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_ciFheIv6vJW26bvZBgLgkKPvQ5RcYH81pGO0QL95upQ";

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: --b2=_ciFheIv6vJW26bvZBgLgkKPvQ5RcYH81pGO0QL95upQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: <html lang="en">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: <head>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:     <title>🎉 Birthday Celebration! 🎉</title>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:     <style>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         body {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-family: 'Arial', sans-serif;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             background-color: #f3f4f6;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             line-height: 1.6;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .container {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             max-width: 600px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px auto;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffffff;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 4px 10px rgba(0,0,0,0.1);

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .header {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff7eb3, #ff758c);

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px 12px 0 0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .header h1 {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 26px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .content {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .greeting p {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .member-photo {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .member-photo img {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             width: 140px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             height: 140px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 50%;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             object-fit: cover;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border: 5px solid #ff758c;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 4px 8px rgba(0,0,0,0.1);

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-details {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             background: #fdf1f3;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 15px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 8px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-date {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff758c;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-age {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             background: #ff7eb3;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 8px 15px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 20px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             background: #fff3e0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 15px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 8px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions h3 {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff758c;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions ul {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             list-style-type: none;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 10px 0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: 1px dashed #ffafbd;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .button {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff7eb3, #ff758c);

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             padding: 12px 30px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 25px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         .footer {

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 30px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 14px;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             color: #666;

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:     </style>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: </head>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:     <div class="container" style="text-align: center !important; margin: 0 auto !important;">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         <div class="header">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             <h1>🎉 Happy Birthday Celebration! 🎉</h1>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             <p>Let's celebrate a cherished member of our church family! 💒</p>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         <div class="content">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             <div class="greeting">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Dear Godwin Bointa,</p>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 We are thrilled to celebrate Ndivhuwo Machiba's birthday today! 🎉</p>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             <div class="member-photo"><img src="http://localhost/campaign/church/uploads/685dc5657df2f.<img src="cid:birthday_image_70b0bcfe0a15244ed2693b8e44cb2eef" alt="Ndivhuwo Machiba" style="display:block; width:160px; height:160px; border-radius:50%; margin:15px auto; object-fit:cover; border:6px solid #fff; box-shadow:0 4px 8px rgba(0,0,0,0.1);"></div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             <div class="birthday-details">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-date">🎈 Birthday: Saturday, July 19, 2025</div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-age">🎂 Turning 40 years</div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             <div class="suggestions">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 <h3>🎁 Ways to Bless Ndivhuwo:</h3>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 <ul>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                     <li>💌 Send a heartfelt birthday message</li>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                     <li>🙏 Pray for their growth and blessings</li>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                     <li>📖 Share a scripture that uplifts them</li>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                     <li>🎁 Gift them something meaningful</li>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 </ul>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             <div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 <a href="mailto:<EMAIL>?subject=Happy%20Birthday%20Ndivhuwo!&body=Dear%20Ndivhuwo,%0D%0A%0D%0AWishing%20you%20a%20blessed%20birthday!%0D%0A%0D%0ABlessings,%0D%0AGodwin Bointa" class="button">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                     🎉 Send Birthday Wishes 🎉

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 </a>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             <div class="footer">

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 With blessings from <strong>Freedom Assembly Church</strong> 💒</p>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: <img src="http://localhost/campaign/church/track.php?id=email_687b34bed7f564.75588417&mid=51" alt="" width="1" height="1" style="display:none;width:1px;height:1px;"></body>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: --b2=_ciFheIv6vJW26bvZBgLgkKPvQ5RcYH81pGO0QL95upQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/png; name=

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_70b0bcfe0a15244ed2693b8e44cb2eef>

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: iVBORw0KGgoAAAANSUhEUgAAAHcAAAA/CAYAAADaMleLAAAgAElEQVR4nO19CZhVxdF29XbOufvM

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: MAMCCqLiggQXiIiguGGUiIlGMO5EI4oLGtRPY/RjjEs0RHEJGhA3oqKDYlCCUWJQICpK3ACjiIDs

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: yyx3Pfvp/p/qey+iH26BqCR/Pc/xcmfmntt9qrvqrbeqWgb/Xz4lSim66667mq5hpAcNG9ZhwUsv

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ZXfUJ0S+A2P4TkjjI3elH5v8VH/H9Q7klB1gF4uHEICXLzj+xNMbGxvljjin/1rl4g4ddv1FcWbT

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 9LLlq/quXLfhjGwhfyiltDYVT7DIc0udO3Qc+c6f//LId2C4/5LwHXDM2yyXNDamdz/04J8Ug3Cw

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: pGo/4LxLICOjfaedSDFfAM/zgBP61sBBh895589/2WHnSb8DY/jGpKmpydh30BH9n3xhxrMtrn1H

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: q1v6Sch59zbHNakVI0GkgFIOKghVp4YO8wZ02m3NjjxfbZaVUvqVEKK+9RH9G2R4Y6P1yqtzv+94

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: pWOy+cIplBvdqcEBuICi60DXrl1x7rB62QpIGhZEhZJ7aP8BR02/555XduR5a7P8ozOH7S6FmQaA

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: N7/9IW1fOeqss9rN/NsLwzc2bxidydS1Z1aCRZGCMASgSkJNTR0EQQT5fB6EaUDgB2AZorlD+/Zv

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 7+hz12a55JNdFr7/wfWnXnppr29/SNtP+h533M4L3n3z1xvbWm+q7dSpkxSCF12PMGEAKAKcC7f7

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 7t1ztm1DNpuFKIogHrdAymgFdOwY7Ojz18oteCUjkPLwv/z1+SmHnnRS/29/WNsm6Ga6Dxiw2/IN

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: G6dQZvy8pq7WROUhUEokEtV7tx100EF/Wbx4MUXl1mZqgBEKhUJBCmHOnzBiRLiDTn+z0MbGRpor

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: lohIxKgPcu/VG9ZeMnTo0B2a3PjBqafu3OYUb4+IOiSiYLiuC8l4AmKGqRXs+z507bbrpoULF3Zz

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: XTchpQT8G3wlUkWB6yz7T8AftHHMGEU5Z2iuRDJJWx37pFdWrLi/749+1KMKtHYkubCxMfnmB+/d

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Ekg4nsXjlKIJBoAwDDVoQqVJpRZ5rre8tbW1p2VZVDAODIi+lJQbM+nkoh1t3lsTCoQo23dUwSmB

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: pxTweFzYYXDGkmUfjTv67LPrvntD/nwZ2tTE5rwy94dOKAdLQZkTBRARAGHG9GfQ/HLO7Z07d3ps

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 7fq1dbFkguFuRYlCHwwhQIbBGkaNld/F+X1d0T43U5ORTJiKcQPytgPEshhY5tFv/nPx07secciP

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: RowYIb7rE0Er8+rYW85ZuX79eCuTrOGxOISEgGQEfweRH4FlWaqurm5+c0trf0MYBxSLRVS2NtXx

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: eBzy2RzUpFJv3ztmzOrvwJS2WbRy8wVHUcaUJABEcCi5DoRAaNH3B2zM5sf99f3FgxobG7/TbNYP

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: fzasgxtG5zPTamf7AZR8FwKQAIRqFGwYBgRBUCrZpQWu5w4KleRWLKZ9rSUMUGEEBqOKKLX48MMP

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: j74DU9pm0cqNUSE5YwpXuB+EYFgxIEKAiMUIi8W62b5/34MvzLxq5E2NvZqamr5zYAt37Ucr1g+I

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: QHb3VQSKUaCMgzAsCGSEIAl9aRQzzUkyijKMMYo7FhWLSsedi6++5ztdunZ99T+FzKGV/0QyCENU

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: biwWAwkKgiiEEBTuYGgp5ju5IK97/Iknm258eNIZp40cWfvtD/0TuX7cuNrmbPY0KYwkMw0Qhglo

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: hVBpVAEwwoAT9gLj5tN+EPSjgvMgDME0TYiiEBLJGBQLOahNpzf23Kfb8u/KvLZVtHINi4eM0RDN

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: F2MMEGOEUsf5+orX1IAdhiZLJfdasuLj+178xxsPDD7vvD0xjPq2ETV+/4y/vdArkPJoJjjFhYmL

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: FHerjCLglAFVyhGcTiZRcLCM5J74e7y4YBApCRFaKy6UkmoJQMr+NuezPaW8cwl4nHEXV3kUlmN3

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: NFsYOiggICmDguuCK6VG0z4lx89949WnJ780+9GDfnz8UaeOGFH/bU3g+okTY2vXbRopYlYy1BZH

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: aR9LKQWDMCARKi94RyqVLRSLF3LOTQyL8PeoWJQgCHCuUSqZfLMTgP9tzWV7i1Zu4ISlKAiLgnOk

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 3jA6AsYJRKC0eSu5LsTSGdQ4UNPCXcytTKbHxlL+p4uXL39y5muvPPy9Y47ojaDrm97J06ZM2c0O

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: vD4RBYLKYqK8KJFtokSBi+ifwNMykMeFYdiVUkooq+zYyiLAy+QilGH4YWNj4w7PTFVFK9fKmCXH

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: KeU5L09U71ilNGODZs6IWRCEEbiBD3nXAWIYYEcRSMYBYlaGJq1jP25uefrhl//6bLdD+10yZPjw

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: vRcvXmx8ExMoRX5PYvB6hVOhXLsVHfqEIXBFFJVqrmFYs6Mo7GtZ1mbkjK9VQbRcKpVswWjzNzHm

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: b0q0co888JCsjKKspt8wNpTVVV1WsmvbwAwThBUDKxEHNwpAcgoBVcBiJrS5JaossUshcI9tLRVu

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: f37u35oG/3z4tfsfe+yu/855NM6ezcMw6M6FiBPO9MLUTBQ6EymBgvJjhvEAA6illHZHpVYXLf6t

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 9s2E6M8opVoNwXbo/O1nRceuPzj4YOf3Tzy6CTlXzHGGMgJKGJjCAC8MwIzFIAx9oExA4DtgWTGQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: CFgAwAtCyNTUgYsMlxNCMh5nSUP0bHPtvV0ph+xz5OHjd8nUvvaTY49dNmLECGd7hhmrnnkmHQI9

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: zA18JokCxbj2n8gjQxgB8eVaw2DvRyAHMM4z6GZQqY7jQDxRjnFNgahRQeh6hZ277L5hIczeXsPD

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 4gDW0NAQu+GeezKE0gQJw4SkVNAwNEKNdQiVCOMAImEYSgaBL5Xy27Vv710+cqTthaEHsZibchyv

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: d+/eESHka9Vyaf/YqBSd1K/vdRvsYiNLJMANAkina6BUsEEIBoqjEj3gFP0ZgGBMk++IRLVIBYwR

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: IJVdgaYRfyalVEKRgPvB0jg13ujSZefHexx77IsTzz9/u6TTBgwd2nvh0g9fAEvUhehHKz8XCkD6

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: AXAJT5o1NReXctk5IPieOhLQupQ6RKKEAJURoP/wC6U/X3/r2J+MGjzY25YxIeY46/rrG954bd6A

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ku31K9qlXRhlHRljdYSQGqWUKaUUhBC0mogTcLFLQkjAGAkIIU4gIzuKwnwoZdEAkqNh2FKXrm2L

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: x6wP99hzz1X779Pz/ZWUbsTM1Rdtls3gZ+BPTz50wZIPXgyEIYRlgeMHAJ4sMzvKAysRA0aIRtGt

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 6zdAPJna7Jvxwh1R9dFVk4fvcQGkseKhLaeYkq0dGxp+n7Lifzp1+PAPRg8b5mzLg+x22GHnbsy1

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 3cdTSRIxArIyTeX5AH5oJ5k5lHGezIbuFGJwWvXHqFydKNBKluDl87I+U3Plir/PH/d1LQsqc/Dg

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: wSk3Ht/FjmDvlWtWHqpM84RssdCJmkIwxkgURQS/e8vnVUligMSQjHPtGiIZ6GfHNOBToKJIqUiC

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ySiGayoIgkhKKTnnhbhptiWTyYXpeOIDTvl7CUNsgkC67TKZnO849hmnnZbbrNwRjY3xyU8+/nZo

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: mN1FIq5jXQoMosAHwpDlCCGZTEL33ffQie1sSyu0tLQgXwtU8DKIUWWfrX2fjPTPMOMSOQ6kYnGI

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: XB8szkLfcZcJoqbutftet7w0dWrxX1EsPtQO3+/zO1tFo6llQUi1sdAPKijZwBRMSxnWSM/1bo8Y

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: PU0JRnBsaJpVRX8cFHCcqO+3DjjggMOeve/hxV9LqaNGGWv++c8D1re2XsxMYz8vCrv5URhn3CAY

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: M2jcIrhWIDBa/Zy2GlAua9Lvqz8HIj+9YYAAYWUcgS4kZlqI6sGvMGqcc5XP5xUTNIhbViA4d4WU

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: OQtIwc3mN20ukJs4ZozToX2Hl0PflYHv6XAoioLyTRiD0A8gdDx4b+Ei2H3XbnoQqFi8qoOpDhQD

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ZxRZ+Te3TGgrFQAMDoUw4AGlexb84Jr3li555cBjjhnyr4RPU6dOxaimCy4k/V1oNRAQooIZazZN

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Y1JNMpmASHZjhBJW3dXomzU5I0GiC6NECcHnW3U7feVkwYQJE0TPow/vs+itBU8vWrlsdonK0wsQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 9nQgSpCYQXCeyJQhyCM4PnRtaFlomRSKcCHiayXU1G6sMgd8hhjGocXD9+j+RMyCRCYNlDP9GQzl

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: HNeFvGuTWG2GkphpFpWfLIZOfXMpv3vBc3pxYapPqh8JUSyK/rZTQ/scmoJqDFhybJBBCOlEUgMP

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Rim88/bbcMABB2R79er1UWtLSykMQ4V/v6WSq2Y5xAsJhWQSSjLS6FqaHGjCIi5RPddkN43b58jD

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: r76ssfHrImsjDIOYVmjFlDFKQEYh7pSFIlRvqojWM847cYx5tQcqm0OF5hAVXL5PKCifN7R37y+1

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: IBMmTIgPGT78+zc/9ODt67O5h5rt0g/MuhpLxSwIOQOFNViIvimBUuCDrxR4UQQBAjZZvtC6oOuq

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Xvicqrlmg5vAqdBJjAgxA6GQiMUh35YDXP+UG8CFCbXt66G+Ywe9gIquDU7og6QEnMCXwjBXxWLx

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Cfvtt+/ln8r0SMJne8XcO4LQwzGbYsawnihE0l1TeTgQ5GtRkX+dNSuTzqQ3fK9nr8c+WL70JM5Y

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: O61UqAAsQoGw8oZ07RJYySQESoJCBkxFYAoBhAvi+P7ubmvLjU0zpg/Z57DDzj7lyCM/+ioV/gXT

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 5GEQGjwegwhDG1Fe/Wj6mYLXNr4xfxPvd2g/RulOqMyqKrWNwB2rtC3ESRcNLhYNGzbsczNBaFn2

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: HDhw7xvuu++itlJhOI+ZcSOZJDT0dZICmTF0SUIIrSgMMBUluuAOKoUCn/haCgQtWwUgkOpFKtau

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: HIYqjVdwwQLxe/f5vh9LxOP4RDds2ABrV6+Eom3r+xu4qPBzQSSpF7518slDh99/zZjFywhRn6pb

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: /u2FF27ikryXNGPasuZybcANsdm84EMUhqH/nUyliOt6eyxbsayLQch4Qdk7DEggwwhw51MNWBCN

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: KuBWTK/aWCKuFcxMC0qBB5hMB8sElkzQrOcetKmQe3zavDmjJzQ1Zb5MuSGlkRA8QvehINIuBMp8

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ssykMq/C0KHED9yBWGyOu1QrAD7BSsg900gBi+Sms088+XMrL8647LKOPQcfe+aGYn6yLegIUZNO

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: RJyTousBZwaEodRRAroDVbkwHMN54uVFZaqzWumBz4UqCuiU8TmRclQBruu4nuetMyxzYZcuXV/v

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: se/3Xt1r7x4fduu2u+OXXP7R+x+Stxb8A1YuXwGO50HMNBG/APECxZwgWwfGI3323HfkGdcMfB8q

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: oPBTyh06dKisz6T+2rx+Ha5+MA0BVWID410NChgF23OrTA8Lw/AYqeBCIORmpdRjKgi9CNEqxpmR

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: BAgizfGGtg3VxIQfBmAlU9r3eCpC3wHJdu2YFOLANS0tN9x4222XT548OfF5Dxyl1nFCw7BsBBra

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 1EaykpNl6+o71rzVu7bWBEpPiJSkEUETWEbyaBQR0CCQYpEGVC9CLrdia7sVOfNnX5x1/wdrVj8Q

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: xkSfogxEgMl/w9AmEdOJCOBQYRge4iNFAIk71Khw8/iAGbp2Vb5Q+/hc0NXh5fv+RkLoczXpmqsa

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: GtpfbhrGtDXr1wfvLlrYe9GiRXsu+WhpzbJly2LFYoFqnwxlq4dgUNoeMNfP1/P4r08/6ZQLX3li

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 2htHkCM206efMssYBpxyyYh3l69e3SYI1EZAwQ08zJho1IZjQ8WgOdBgQGoFkyAI2glGzzPN5Fiu

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: YL3v+eeSULbjnJMIJARBCJYZA64o+KEEysuhElZ+IBkiEgnIew4gnwCMmoFjX3LzIw87J1500cSn

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: x49v2Zpy33vvvTAMghZM2yE1alkm2MWSisdi81JmTc7N579nl0r1VialF1DIoByuISnDOGBanvrB

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: +p5779v0WTeAgGmf/of3afbzl4YmG2TELBZhWIrWS5vRMqLVr1El5OMcOKuwXti1EFVZMgokiEBQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: VC0JozBypIwKgvIV8VRiDhPmOwSiyPa805ubN/WKomgXxhgzLUvDUj8IgFsG5At5SCaSEOcC3EIJ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: OKVS+tHCvbt1Hzflzjuf6Natm/vZZ/R/2kmOPHvEyq6dOz1u53Jh4Dk6v6v9FH4VLUPzMigpw/dQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: SSxRIaWSfVQpm7s/bsWLdZmaKzmjr/ieF6Gp5PjHfgilfAFMyvXqdorFsp+hBIIoAlxuUjAQiRiJ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: DFazoS3b+PbixfeOb2pKbk25qJDdunVdYheKilXQfGC7eUHYszeeeaazYtmygbFkIo6+kJoCOCbj

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: fRfiVgxU4IN0fTsdi//2hEMOeXXL+2LLyVNz/n7m+mLLMy6Rw0gsxpHQrAIgnePGpEPlPQouMNyR

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: nutCgOSOBDAxhyz1boDI8QISyRcSpjWmvrbuonZ19dekksl5VJE+ge+NzReLD/qe92NCyG5cCMF4

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: OSZH96c4hYLnQKpdBlzf0fiFSxm4udzzA77f9+zXnnnm4a0pdqvKPb9Pn4ADf1QosjpuCAg8t4x8

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: K6GNqihk8wXleI6UEUFnP/BHEUpziVjipyYXf5Z+gHQMMKWgXSajTQWaJcu0tI9E/6StBhIMjAGW

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: yGCKUXJurG1r/fGEx/7489ubmmJbG3yH9g3vCk5DjqFF6EMmlVofM+OvXz1pUlyGsmfRdRgCnmoG

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: CL/Ptx2wCIcENz8YfurpD4waNWozI9U4YUL8ylt+e+ncBfN/Y2TS7cCySKD9Kfpn0H5S+8rKYkfQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: hA8frRnjXNdEJwwLCC7Wkh2B7TazIHymobZmcDKRuChwo5dz2dyB2ZbWG3PZ7OWFYvFox7Z3Cfww

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: iTfFEKeaaq0uIPwuYjHwy8sfDMqUSdm039962/kzHnjgna09l89VLsox++//Zl0mNU36oYphCk2V

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: QQOtfKDKrlR3HvpgLgSuYOL6XkOhVLw1UmqQaVk3cc4fZwpshPZ2rgAcTZkfgu96yEPrwLyadtNx

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: XRhoWI8+jVmGWLz0w1/efufvTsPCgM+OM24mlpqMt2FaD3dk4Idv9u7f/2NZjDJu6O+m0SqjGuEj

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: u1aTTEHKjIGbL/oNqfS0xlGj8psVO25czR9+f88VLpVjzFSifc5xiBMEgN0JRBLtT7cERfh+y+eA

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 32EXipHvullO2NxEPHFbOpH4WW0y2RhE0a6e447xI++ZIAgui5TchVLKkb3S5hwVyj7JxqFiyxtK

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ldOvWFVSstEKOEGh8OJhffvccM6Pf7zqixT7ucodN26cc0CvA+8WBDbgTemWE1LlUAevKo2Gg8Od

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 4csKIwNqj6JTGu8F3klJxkYZQlwdF2Y2bpgK0XTMsqA2mYbWjc0aeHE0P5UUHIZfGKxjkR41BFjJ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: REPetW96Z9OaPT47zpqMt9EwjLmmMJRTssPdd97lsaljxgSt+Q3tiZK7Ec4IhiGpRByEUlBszUJY

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: cqKkYT15YO/e929W7F13pf/Y9HhjZNCrHCnjkSHAiMeAM6HjzfJGJRoQ4fwRZRNEyWEZMCLhT6Vc

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: wTm/wkqZfRPJ5E8NIWYGUg4ret6ztuff7Uby9AhIOyoMgnNEYgfnh34caDXFiopVm1k+JDIQrjEv

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: VMzxcj26drvkwYfGn/zU+Pu+EpP2uS2cgtLmpBWfR4FI3G3oN7Xv3EKxm/ljVC5WQUi0XQQpIhKC

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: irkyGm4Lej5IOd2KWSMLudzLoJTv2Q44tg0GKyNNVLjOIyuokA2fsFz4VBWjHea+Pn/sKRdeuM+W

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Y2wP7W3LNBcIykKqoNihY8eluNRrUqm0GTNTuA6RBi20ZSHGDUgKC3zPeX9Av35jHho7dj3e439u

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: vTV1z6SJozcV8z/zKIk7WOnMMcFAwPdDMAyrPKeKoEvBiyIwjFTRiODtGGHXJ2LmsEQ8OSXO4ntF

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: YXBNvlR6OG+Xzih4TmcQzJKcUowOsIDPCXzwKhcSECho4nX16RbWQKPjIFQyV1jXMZa646ZzL5gy

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: rM+g3FdR7Bcqd+o99xSplLdJL1ixOaxBknvLeK2ym3WKEPOpnG32FRzpN8Hbu0Fwve05//OLa897

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: tuseewy17dLdBKRHZAQ1mQwErqcXi84woQ/GMEoqsLDITcqyTxYMgczxf531wh+vGDt2c4iEoCpS

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 8JaSMmcKA3eOPr8im8+3LxVLFj6wdDoNNekMuCVblXLZ5Uf2P+ziqffcsxTK4Q57df4rp0SCXs7T

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 8ZQNEaQa6qDkOZp4wNShXSyVwVRlIauyYm0B9NkU4/1imcyAVKb2D8RRHQotG+c0t2yYWvBKI8Ek

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: XVjaIjyTADv0tVVD/4/uprpr8T2QT5RadU9QwTESFesGH40cesZxq/7+xo1Dhgz5WvVdX9h8PeSU

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 09+qTSWnRwHWzkWYPdicKkMF6xWmQ6QymtQIusKhVuqTCOOcF3132FNTZp25n2Xljzl04E0mYfcZ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: QF2nkIeYZeh7VbnhzYDC87XiU8mMDpkiQqhIJfZ/Z/78g7ccY9ISy4J8Pmtxvn7ooEEaNWZqa+MY

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: oiVMAwotLaA8D9y2bFtDuuYX1/7853/fPL9zzz7o9YWLrvYoSeZ8bKeJQTZf1OgXF63vOYCgkmmy

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: QwELJVZ2bBBArzGkef6Gzp3/yUxT+S25q6IoeEAIszsXpol5MTeMSIA0Lsa8VkyTQVAJJXHHYnUp

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: WiedAapwyvrZYvwd4oWVJLJgKTW+azr9ISHka5f/fKFy7x41yuu37/63W0IstBLW5hIcgwkdx0U4

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: eKkAY0DkVKuX3Hxzqak+nkk0vLbwrbtf+HjZo/v06FEzZPDxV3apbz88yOUXqMAPMNWFRD7WP+Hk

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: VSghJpCBMUAFEUQhASIs8AxOF65ecWnjvfe2r47x+vMuXrZrfafnVd5Z2qFnT51CLDqlGp3e8wKw

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: pJKqZM8+4Zhjj1y1YMH0Pn36aHg+6JRTjpn7+oLp8YZ2uwUYKiUS4AdlUkIQBr5tQxLDQLQcRcev

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: IWJmO9O6oF+P3gftn0qMT2REx5plKyZ5zS0fuFReEQnWoDgniPgVpSCYAE4MYCHFUAgSwtQWCUM2

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: zg3dwY99wrghMPkSBL6OJJCwZJ4b1Qlj5olHH3VM61vv3Dl69Oh/KTX6pccm/OCCCzYkDPFktqXV

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: re6uYiVGxf2LA93yVpsRdRV84XvBESjxUIbHP/b4Yxf26tGDvD1zZtPoyy7/mXT9vwkCMs65Cn1P

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: m0LkaLP5PFhmHDw30CQIUpYBo6TFzh8x7bmnj6uODznh/fbc48GEYPOP22MPrThLmDEZBTTIF3zq

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: erOOOfSIC564++7NYcNxl1xivrfkw595APWYCtS4SJZTbLggEER2qG2HqFqGttsSF9b9h/bpf945

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Rx89CWKQ+yhUQ9ev3/gQs4yz7NDfOaSUBoTqBAkudLyT2uLR4oLBNClKKpXSJhfNPs5TL8ZCHjKJ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: OISeq0qtraFFyAfHH3v0FX/8ze/mwzZUrnxp98CMiRPlD44/4a3mXAsTnPcu2SUjnkppc4ylLZjn

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: RQxUnQ5TW1BtFVrcc8pVHJZlcdf3+7355lv7rGlre/W3o0YtveXu8bPn/e0lo5DN7Z5MpuKOa2tT

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Fk8mQKL/VpH236VSCeKIpAkxshtbeoz+xeWPznvxRW2G3/7HPzYo33//4IMP1qYrVlfX03OddMK0

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: xtXWmr+eM3X65rBh8vPPJ56YPPkOj6pTsIXeQ5bNNLXZNRQFGkR6l9m5wrq4ZU1NJ8SF62PxyW6h

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Vc19990ha9avvyFvly6z0onOIUgSVlOIGGvT6qv65CKYjGdgIKunInB9V783TUNHCFgzbXEBFmIO

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: L2j9/n77/fKMYade85vLrtjm4vivdOAJgqv99/re7eD5f4qZlsR6KgRNtl0sk+KyzJ/yLTnULSRh

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: WnoC+UIJXBmSDdmWHz721FOnNTY1GaOHDVtz6YgLrqkVsclRyY0wzMAcsu06UPRsDTxc3aiV0P5Q

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: cBOR5c7F0Nuv+g1YW7QlGdGrR4/pXes7nL7mjX+M/2D2gk/Rlzf/7pY+G+38aS5TVsjKmCGyPRCh

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: AiNUkBaWCkrO6r69+5zzyIQJl64+6rhFHQzDbG1rvXLTpk33gmDH0ZhhlTxXJwXQlUAF6UIl6f6p

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: kLHiZzGSwEWgQRMueNuBwHGhNp6AOKGqec2afMf6dndccMKP773q3HPXbqti4eueQ/W9QYMGrm7b

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 9EjOs3c2kimIJZK6LVLXCKsyg1MVHTURqJD0WOzggZGMa8gfBR4EJcdrH0vddP5ZZz1w7ciRa+6e

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: MqXTDbfcepMdBceLTLI+Mlg5T6kImFSA57jlqo8oAkvJ0AzldateX3Dr1ymLuWLsFYkHnph1Z1vo

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: nRPLpAj6PMxcWUDBy5egNpEqha43+4iBR9w15c7bZukO/aOOOmBDIXdyKfAvS6RTMbQgVjymTarr

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: e5tjfaiSO9Uqiy1GhSATSQ6kcnXmKoz0gpdeAEGpJAWoFft/r8eNv772+icH7L134evo5IvkazV1

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 3XDVVWvnv7f444iQfslkItWWzRKkETFtRcrNvrA5c1pRrv6BlGDoSkpfh03YyxP5Pncdt9/iRYvr

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: Ro8477mrLrkkd92v73pp3fpV7zW3NA/0Aj8REkXwbzH9iiE0mjVdwuP7JCaM4l577f38Yw8+uFVe

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 9bOCDNfkZ58/y6bRxejMMeZEUCgIBREoSBuxdTvV1v/PPbf99pYrz/0Zdvrxky699MA169Y+pIQY

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: IgW1ENUizYjhGc4D8Ue1XLY6d81kKag8j/Irfoay8o7VgEr7dk+5+YKXNMWMvbrscvXLTdOe7drQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: sE3FeZ+Vr3UO1fnnnx/86szhf6oxzd9sXLrcrk+ngERlWh15T+2D8cJQqNJnhIpG0h7hvylMXQOE

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: qS4jFgeRjFlZzzn5kRdnn3fLhAmZq88flps2/pk/xxT8Lk7ppgQmv4NyHIymz4zHIMCwgXFS8oO9

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 7/j973f6qmNfvGZjr035wq/cUNYRo8yoGQlqgqgAAAvdSURBVJRDnIrIK9rvZJKp6/5w45jJR/ft

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 2/LCu+/Gzx0z5vJVK1c9rJTqGQSBERcWCGC6qSwZT4IKJKTjKfBsr0JPli+qyBYp+PJuRqCIqFui

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 9VIKTEJUUCx81KWh/a9Hjxj5s79P//Pcr1u2+lXka7djzpgxQ47+1f8u/nDpkjCfy+5HGY1T7XMq

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: F60kFyo7F6cY4W4VWNjlA6MMglD35kCEq1wws1QsDtywYVNp7ZIPXrnzzuvDGa+99ua6j1b8Y9Wa

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: NQcSCQ1o+BhyxBgmaQKMQeh58Q7pzEsr33//gy8b812PPJK+54H7xiba1x0SMU3gYk0pmJIou7Xt

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 9ctHXvbzi8bfMat/587hMWeemXj4iSnX5kqlq/0o3MlKJAm2g0a6FIZpK4RECxYLIi7AnQvqExtc

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: bZ6rXmWyR4GFLaVAwc7nsrVW/MHDBxx8yaiTT3nu/LPP/rc1nv3LfT1jx45NjH304Qtdom7k8biB

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: LEyEhV7IwvByzyuucLfCTbNKNYKu1NC2qpJZQnYqDMGIYMmAA3sP/tPEiR9B5WzGbgP6jmjOFe4Q

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: qYwZYXGd52gAg6U0ZqRk7927X/33qVPHftlYd+vfv08h8ppsGXSTFeKAYcFw0X13n932GD3/mWd0

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: JfrQoUONl1auvM6OvFGUsbSnD0pJgeeWqymEpiKlzrFieIeMHFZF6DEpJGDKjd5+pfpCKx4XheNC

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 0jCiXHPr8s47NUw49MgfTHqosfHffhrsv3w84JVXXlm69pwRd8UpvynbvHE10oUWUmoVgIEPEPO3

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: GONVRfPRmJ/TZTusnB/Faoa4hVVqu788f/4TR5955g+ggoBHn3LGY52SNfdJ183b+TwkYpa2AAjK

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: mCHo8lWr9vyyceIiISb7Ud61O+HRCBjmUCeQMclm7t1l11Nemz79Zfy7u5qaGuasWTXeVcHlThSk

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: AwoQT6d0KRCGYrFkCnJ2EXxZ5tBRsRiLG5ZZLnBTUvtTBJdYecIpAd9xMbGQSwr+QoqJK04a8qOj

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: n7j97ju/CcXC9ji19a6ZM83npk8dMmfB/EkRFxnNl1Y6EVSg9FkTSCXKT5mrSrqwwtXihVUYGBrE

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: GFtw89XXDrlo2DBN7N9++6S6cVMm3O1Z7NR8GBFsRgPGIMkMECX/L5eeeMIPv6ig7vIbb+x6+8P3

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: v5GoyzQg4+UWS0ra7sJfXXnV2b8aMUKfEodga9ILf7myEAbX+1SZmE9GRGzni5o2dMNyWh7dAS4u

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: UglxsHgQARQWD8ZNU79iiIP0pe85PgX+Tm0iMXHgUUc1PTJmTOGb7tjf5oM9sf1i5h8eeNoC+kuL

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: kEUxwrygUISMGdflLDplWOGiUaJq96CsFNExVuZyhaHLXnk83uPWceN+umDBAk3fjB7989afnnji

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: PYVNzZuwQyARj5ePQVB64TR80RzwHjNmzTorXVtTjzuqlMurJDM+GHjQwedfc955mrHCyoum2bPP

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: bm5tGc0EN6udAYVcvgII0ckT4DEBShBwijlQtFzTXe5aQM45UjyKQub6LTEF7xme9/ROiczp55xw

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: wtGr5sx54NHGxvy3cRTDdjm1FRuXb57w0KR+++x7SkLR2xqsZMFty+ocqoF+NSqXs6IyuQ7iQSf/

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: qyEEFl5jvIwNZ8BZLOuWLrvmjjsOr95/cL9+8zvUZBqjYqkFC8NS8QSUCkUo2bYJXbt+7kEsD8+Y

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 0aHg2oNUGGEtD8Q5b96prt0NMx95ZH71Yf9x1qzua1o2/TJTV9uAY0BMgH+LKUKMr7F2GJkkRLxI

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 6mfq6jTvDBirF/LgtbX67RPxebTo/O+u7evP6Nau/uTxt407a9mcOU/dXVbqt3YQ93ZvlJ49ezb/

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: xS03Dft47fqzuWn2VpzVISuFfhILx6ptFTprVAlx0CzrszhkCMVsDhKUQX0y/eaJxw4+/bZrrnkf

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: /x77fc+85sqL3vpw6S2punYGxpLcDRf98vQrDxo9eus9R4cNG3bY0pUrnwRBGvySvX6vXXe9buIN

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: N0/ed999fSQorvvtb/d8dPq0u/KeN8iRIUkm0+B4LjAqyqnGcvpNKgqhEMwDKb3Q89pkJNepIFjX

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: uaHDG/vs3X1u9z33XTjuXyT3/52y3Y8fOuKII/DglMev+c1vZk2bNeuQZjt3MaX0UF9FBgl9IiQD

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: xoQO+aNquMSo7oXBmBl7igxCoLmQ6/XIjGkX3nXXXVcitYgKGdHYOOHj9RuGRVL19fDEO+AB5+s/

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: d2e0FYt9/DCoFYoW2tWkr68qFn938x137PTUn2eMb85nBxLDJJh082xHeY4jiaKY7Mru3LnLeqeQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: ezVG6eJONbXLWnK5Urq+LvuLyy/flKyryx7fu/d2bUnd3vJvOVuqYoo2AcD0S+666y9PNj16tnSD

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: EdIP9gXGTCKAYKU8Nm9FlTgYfR0lvAxQGMP2E+5LOWTBunXXYxsw3ndiY6PduX/fJ1py+QNMYZjJ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: mNnc2tq61XbQEY0j4rNeev8MyxC8lG175aGJk6ZVFYvy0rx5Ry35cEnv2p3at0agVlsKVqcsa0l9

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: PLnEMoylQ08+eTFs2JBtHDMGg3K1JYt/6vPP/3u1sp3kGzm/Ak1108svd5n/6qt9bN870vXsg1qL

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: hQ6EixpqCCMiiklUbaWVERGpznf6Qdi5rv7cRc8++8fqDul7/PHdl61aMZEADKxLZn7/z3nzLt3a

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 7hl40glHvL9y5XSqYNNO8dQ5P5ozZ25jxf9hU/S9j00+vKWtEE/VpdpMxdddfPHF2VrGcmh5voln

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 8k3IN37MUKWKkfIOHTo88fST38/Zpf1c3zsgUNDVC4POQpgxx3EMLN9FOGNROnfsL2849cyfHLuu

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: eo9Bpw/r//7i92fUJtNnvTtv3rOf/Q5U3tgH77t1+erVZ3Vo1+6sxbPnPr8tedEdVb7xI/8qMSle

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: eP7Emialnu24cmH6/nsfyKxesb62JZ9PJSyrXT6b38l33Q5uoRCLm+Gn6pa7d+/xzqolyx494IAe

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 7707b97/+Y4/zZ6d3rhmbaeaZOqJnRs6vr74v1Cx33lBRLu13l382e8mTKjHAretzeHtt9/u3OuQ

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: vhciwfLf/gz/4wR9/Jc1kv03yDb7XPShPXr0IO8BsI5tbaq2tlZit+B/6v/pZEeSL1Qumr+XVqww

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: X54xo27ua6/VOGGYAA61MW50/2D5R3sKxtt5UVgvwzARoYnUh1IwPJ8h70ZBThCytnPnndd4jvth

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: fW1tq18seBBA8cZf/SpbTKXyg7t3367J6a0Jkh9hGIogCBR0AoC1AIjM2vm+MnbdlcDatdAixObn

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: gAliJLUNzokfhqqaMNaf30L8Dh22eL8KGtwG+dYee4TDCPlax/kuWLAgnkwmWZaxzWMIsUTkM1Ib

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: RQpPMNjyp+u3GPNnBTsst6pcVOroceNqm5qmnOBT6B9FsGfMsjq7QVhr28UYZUIYJme6+D4KdNjC

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: DIZsBDhuSVdFmhaeWRWpSAbSMowwCvzAZGbJ5LQlsL3VnPHlFmeLjjjiqFmFXr2WTv2CzvZtkcHD

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: fjLszUXv/cSMWxKPMJAqUoZhKddxpBsFhDFBDMGp73t4coZ+HliArvlwzvEEGd1FSLD2Flk23btD

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: kTvVZw3p4yZASRXJsEunTvN+MfP5h7+qgmfPnm1dds0vb3b8oHMESmAMFip9pg0WPejuJPweQ1vB

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: SJcCRPoEhEp7YXkESm9S7GbUw6Ggz0Fimg/81CoSdzY17dFl4CEjck7pcNt39zZjCRM7s/C4g1Cf

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: zmJAMp0B2ynpu2LJaYgdCZgJwiMBhAGCG+D4LlDOiRAGiwCYH4WmJJDE1hCPqH1U6CkHRPT4M9M3

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: xZ5/7oWDTj7xKVJy57w2c+Z2y57gIj1gQN+uIcgTCSU8Ukyf+4gneaHJwPMssfjOx6MHRPxTGtEP

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: UVGlsJgcz2bGA9j0MQtY7xPqY44y6TTJZ7MKO9wj34cVHy8zks8992jl418qmzZtCop2qU/J8w8W

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: MUtEWPQgmPLDSHc64vkKOmesVVrujy6f5YGH5ZYPS0Ep/78ZCC45ffSS5ByUDOH/AcRNdcEflr82

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: AAAAAElFTkSuQmCC

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: --b2=_ciFheIv6vJW26bvZBgLgkKPvQ5RcYH81pGO0QL95upQ--

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ciFheIv6vJW26bvZBgLgkKPvQ5RcYH81pGO0QL95upQ--

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:36] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:01:37] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbdX082dz5Z5ml

[2025-07-19 08:01:37] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:01:37] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:01:37] Email sent <NAME_EMAIL>
[2025-07-19 08:01:37] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:01:37] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:01:44] Global sendEmail called for Jennifer Godson <<EMAIL>>
[2025-07-19 08:01:44] Initial email body length: 5084 characters
[2025-07-19 08:01:44] Birthday notification detected via explicit flag
[2025-07-19 08:01:44] Birthday member data extracted: Array
(
    [name] => Ndivhuwo Machiba
    [email] => <EMAIL>
    [image_path] => uploads/685dc5657df2f.png
    [photo_url] => http://localhost/campaign/church/uploads/685dc5657df2f.png
)

[2025-07-19 08:01:44] Email type: Birthday Notification
[2025-07-19 08:01:44] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:01:44] Set Reply-To address: <EMAIL>
[2025-07-19 08:01:44] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:01:44] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:01:44] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:01:44] SYSTEMATIC FIX: Found 2 images referenced in HTML
[2025-07-19 08:01:44] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:44] SYSTEMATIC FIX: Referenced image 2: http://localhost/campaign/church/track.php?id=email_687b34c8a88d77.01380852&mid=52
[2025-07-19 08:01:44] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:01:44] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:44] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_d5acd49e7544115c8bd3da3658a599b5, mime=image/png
[2025-07-19 08:01:44] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:01:44] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/685dc5657df2f.png as CID: birthday_image_d5acd49e7544115c8bd3da3658a599b5
[2025-07-19 08:01:44] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:01:44] SYSTEMATIC FIX: WARNING - 1 original URLs still present in HTML!
[2025-07-19 08:01:44] SYSTEMATIC FIX: Remaining URL: http://localhost/campaign/church/track.php?id=email_687b34c8a88d77.01380852&mid=52
[2025-07-19 08:01:44] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:01:44] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:01:44]   - Email type: Birthday Notification
[2025-07-19 08:01:44]   - Images found in HTML: 2
[2025-07-19 08:01:44]   - Images successfully embedded: 1
[2025-07-19 08:01:44]   - Processed image URLs: 1
[2025-07-19 08:01:44]   - Processed image paths: 1
[2025-07-19 08:01:44] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:01:44]   - URL: http://localhost/campaign/church/uploads/685dc5657df2f.png -> CID: birthday_image_d5acd49e7544115c8bd3da3658a599b5
[2025-07-19 08:01:44]   - Embedded images: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:01:44] Set HTML body (5077 chars) and plain text alternative (1785 chars)
[2025-07-19 08:01:44] Attempting to send email to: <EMAIL> with subject: Birthday Celebration! Ndivhuwo
[2025-07-19 08:01:44] Email content validation: Body length=5077, HTML=Yes
[2025-07-19 08:01:45] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:01:45] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:01:45] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:01:45] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:01:45] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:01:45] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:45] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:01:45] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:46] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:01:46] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:01:46] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:01:46] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:01:44 +0200

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: To: Jennifer Godson <<EMAIL>>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Celebration! Ndivhuwo

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_iL46DtX8gxwLJ4AbbSmz8kpOFIVK09WbLBoJA8MOLA"

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: --b1=_iL46DtX8gxwLJ4AbbSmz8kpOFIVK09WbLBoJA8MOLA

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Birthday Celebration! =F0=9F=8E=89 body { margin: 0; padding: =

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 0; font-family: 'Arial', sans-serif; background-color: #f3f4f6; color: #333=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ; line-height: 1.6; text-align: center; } .container { max-width: 600px; ma=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: rgin: 20px auto; padding: 20px; background: #ffffff; border-radius: 12px; b=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ox-shadow: 0 4px 10px rgba(0,0,0,0.1); } .header { background: linear-gradi=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ent(135deg, #ff7eb3, #ff758c); padding: 30px; color: #ffffff; border-radius=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: : 12px 12px 0 0; } .header h1 { font-size: 26px; } .content { padding: 30px=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ; } .greeting p { font-size: 18px; } .member-photo { margin: 20px 0; } .mem=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ber-photo img { width: 140px; height: 140px; border-radius: 50%; object-fit=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: : cover; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1); =

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: } .birthday-details { background: #fdf1f3; padding: 15px; border-radius: 8p=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: x; margin: 20px 0; } .birthday-date { font-size: 20px; color: #ff758c; font=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: -weight: bold; } .birthday-age { display: inline-block; background: #ff7eb3=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ; color: #ffffff; padding: 8px 15px; border-radius: 20px; font-size: 16px; =

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: font-weight: bold; } .suggestions { background: #fff3e0; padding: 15px; bor=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: der-radius: 8px; } .suggestions h3 { color: #ff758c; } .suggestions ul { li=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: st-style-type: none; padding: 0; } .suggestions li { padding: 10px 0; borde=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: r-bottom: 1px dashed #ffafbd; font-size: 16px; } .button { display: inline-=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: block; background: linear-gradient(135deg, #ff7eb3, #ff758c); color: #fffff=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: f; text-decoration: none; padding: 12px 30px; border-radius: 25px; margin: =

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 20px 0; font-size: 16px; font-weight: bold; } .footer { margin-top: 30px; f=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ont-size: 14px; color: #666; } =F0=9F=8E=89 Happy Birthday Celebration! =

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Let's celebrate a cherished member of our church family! =

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=92=92 Dear Jennifer Godson, =F0=9F=8E=82 We are thrilled to celebrat=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: e Ndivhuwo Machiba's birthday today! =F0=9F=8E=89

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: --b1=_iL46DtX8gxwLJ4AbbSmz8kpOFIVK09WbLBoJA8MOLA

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_iL46DtX8gxwLJ4AbbSmz8kpOFIVK09WbLBoJA8MOLA";

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: --b2=_iL46DtX8gxwLJ4AbbSmz8kpOFIVK09WbLBoJA8MOLA

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: <html lang="en">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: <head>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:     <title>🎉 Birthday Celebration! 🎉</title>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:     <style>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         body {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-family: 'Arial', sans-serif;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             background-color: #f3f4f6;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             line-height: 1.6;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .container {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             max-width: 600px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px auto;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffffff;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 4px 10px rgba(0,0,0,0.1);

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .header {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff7eb3, #ff758c);

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px 12px 0 0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .header h1 {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 26px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .content {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .greeting p {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .member-photo {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .member-photo img {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             width: 140px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             height: 140px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 50%;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             object-fit: cover;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border: 5px solid #ff758c;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 4px 8px rgba(0,0,0,0.1);

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-details {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             background: #fdf1f3;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 15px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 8px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-date {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff758c;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-age {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             background: #ff7eb3;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 8px 15px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 20px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             background: #fff3e0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 15px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 8px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions h3 {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff758c;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions ul {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             list-style-type: none;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 10px 0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: 1px dashed #ffafbd;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .button {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff7eb3, #ff758c);

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             padding: 12px 30px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 25px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         .footer {

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 30px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 14px;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             color: #666;

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:     </style>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: </head>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:     <div class="container" style="text-align: center !important; margin: 0 auto !important;">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         <div class="header">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             <h1>🎉 Happy Birthday Celebration! 🎉</h1>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             <p>Let's celebrate a cherished member of our church family! 💒</p>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         <div class="content">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             <div class="greeting">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Dear Jennifer Godson,</p>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 We are thrilled to celebrate Ndivhuwo Machiba's birthday today! 🎉</p>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             <div class="member-photo"><img src="http://localhost/campaign/church/uploads/685dc5657df2f.<img src="cid:birthday_image_d5acd49e7544115c8bd3da3658a599b5" alt="Ndivhuwo Machiba" style="display:block; width:160px; height:160px; border-radius:50%; margin:15px auto; object-fit:cover; border:6px solid #fff; box-shadow:0 4px 8px rgba(0,0,0,0.1);"></div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             <div class="birthday-details">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-date">🎈 Birthday: Saturday, July 19, 2025</div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-age">🎂 Turning 40 years</div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             <div class="suggestions">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 <h3>🎁 Ways to Bless Ndivhuwo:</h3>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 <ul>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                     <li>💌 Send a heartfelt birthday message</li>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                     <li>🙏 Pray for their growth and blessings</li>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                     <li>📖 Share a scripture that uplifts them</li>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                     <li>🎁 Gift them something meaningful</li>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 </ul>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             <div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 <a href="mailto:<EMAIL>?subject=Happy%20Birthday%20Ndivhuwo!&body=Dear%20Ndivhuwo,%0D%0A%0D%0AWishing%20you%20a%20blessed%20birthday!%0D%0A%0D%0ABlessings,%0D%0AJennifer Godson" class="button">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                     🎉 Send Birthday Wishes 🎉

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 </a>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             <div class="footer">

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 With blessings from <strong>Freedom Assembly Church</strong> 💒</p>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: <img src="http://localhost/campaign/church/track.php?id=email_687b34c8a88d77.01380852&mid=52" alt="" width="1" height="1" style="display:none;width:1px;height:1px;"></body>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: --b2=_iL46DtX8gxwLJ4AbbSmz8kpOFIVK09WbLBoJA8MOLA

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/png; name=

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_d5acd49e7544115c8bd3da3658a599b5>

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: iVBORw0KGgoAAAANSUhEUgAAAHcAAAA/CAYAAADaMleLAAAgAElEQVR4nO19CZhVxdF29XbOufvM

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: MAMCCqLiggQXiIiguGGUiIlGMO5EI4oLGtRPY/RjjEs0RHEJGhA3oqKDYlCCUWJQICpK3ACjiIDs

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: yyx3Pfvp/p/qey+iH26BqCR/Pc/xcmfmntt9qrvqrbeqWgb/Xz4lSim66667mq5hpAcNG9ZhwUsv

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ZXfUJ0S+A2P4TkjjI3elH5v8VH/H9Q7klB1gF4uHEICXLzj+xNMbGxvljjin/1rl4g4ddv1FcWbT

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 9LLlq/quXLfhjGwhfyiltDYVT7DIc0udO3Qc+c6f//LId2C4/5LwHXDM2yyXNDamdz/04J8Ug3Cw

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: pGo/4LxLICOjfaedSDFfAM/zgBP61sBBh895589/2WHnSb8DY/jGpKmpydh30BH9n3xhxrMtrn1H

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: q1v6Sch59zbHNakVI0GkgFIOKghVp4YO8wZ02m3NjjxfbZaVUvqVEKK+9RH9G2R4Y6P1yqtzv+94

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: pWOy+cIplBvdqcEBuICi60DXrl1x7rB62QpIGhZEhZJ7aP8BR02/555XduR5a7P8ozOH7S6FmQaA

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: N7/9IW1fOeqss9rN/NsLwzc2bxidydS1Z1aCRZGCMASgSkJNTR0EQQT5fB6EaUDgB2AZorlD+/Zv

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 7+hz12a55JNdFr7/wfWnXnppr29/SNtP+h533M4L3n3z1xvbWm+q7dSpkxSCF12PMGEAKAKcC7f7

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 7t1ztm1DNpuFKIogHrdAymgFdOwY7Ojz18oteCUjkPLwv/z1+SmHnnRS/29/WNsm6Ga6Dxiw2/IN

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: G6dQZvy8pq7WROUhUEokEtV7tx100EF/Wbx4MUXl1mZqgBEKhUJBCmHOnzBiRLiDTn+z0MbGRpor

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: lohIxKgPcu/VG9ZeMnTo0B2a3PjBqafu3OYUb4+IOiSiYLiuC8l4AmKGqRXs+z507bbrpoULF3Zz

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: XTchpQT8G3wlUkWB6yz7T8AftHHMGEU5Z2iuRDJJWx37pFdWrLi/749+1KMKtHYkubCxMfnmB+/d

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Ekg4nsXjlKIJBoAwDDVoQqVJpRZ5rre8tbW1p2VZVDAODIi+lJQbM+nkoh1t3lsTCoQo23dUwSmB

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: pxTweFzYYXDGkmUfjTv67LPrvntD/nwZ2tTE5rwy94dOKAdLQZkTBRARAGHG9GfQ/HLO7Z07d3ps

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 7fq1dbFkguFuRYlCHwwhQIbBGkaNld/F+X1d0T43U5ORTJiKcQPytgPEshhY5tFv/nPx07secciP

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: RowYIb7rE0Er8+rYW85ZuX79eCuTrOGxOISEgGQEfweRH4FlWaqurm5+c0trf0MYBxSLRVS2NtXx

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: eBzy2RzUpFJv3ztmzOrvwJS2WbRy8wVHUcaUJABEcCi5DoRAaNH3B2zM5sf99f3FgxobG7/TbNYP

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: fzasgxtG5zPTamf7AZR8FwKQAIRqFGwYBgRBUCrZpQWu5w4KleRWLKZ9rSUMUGEEBqOKKLX48MMP

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: j74DU9pm0cqNUSE5YwpXuB+EYFgxIEKAiMUIi8W62b5/34MvzLxq5E2NvZqamr5zYAt37Ucr1g+I

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: QHb3VQSKUaCMgzAsCGSEIAl9aRQzzUkyijKMMYo7FhWLSsedi6++5ztdunZ99T+FzKGV/0QyCENU

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: biwWAwkKgiiEEBTuYGgp5ju5IK97/Iknm258eNIZp40cWfvtD/0TuX7cuNrmbPY0KYwkMw0Qhglo

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: hVBpVAEwwoAT9gLj5tN+EPSjgvMgDME0TYiiEBLJGBQLOahNpzf23Kfb8u/KvLZVtHINi4eM0RDN

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: F2MMEGOEUsf5+orX1IAdhiZLJfdasuLj+178xxsPDD7vvD0xjPq2ETV+/4y/vdArkPJoJjjFhYmL

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: FHerjCLglAFVyhGcTiZRcLCM5J74e7y4YBApCRFaKy6UkmoJQMr+NuezPaW8cwl4nHEXV3kUlmN3

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: NFsYOiggICmDguuCK6VG0z4lx89949WnJ780+9GDfnz8UaeOGFH/bU3g+okTY2vXbRopYlYy1BZH

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: aR9LKQWDMCARKi94RyqVLRSLF3LOTQyL8PeoWJQgCHCuUSqZfLMTgP9tzWV7i1Zu4ISlKAiLgnOk

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 3jA6AsYJRKC0eSu5LsTSGdQ4UNPCXcytTKbHxlL+p4uXL39y5muvPPy9Y47ojaDrm97J06ZM2c0O

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: vD4RBYLKYqK8KJFtokSBi+ifwNMykMeFYdiVUkooq+zYyiLAy+QilGH4YWNj4w7PTFVFK9fKmCXH

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: KeU5L09U71ilNGODZs6IWRCEEbiBD3nXAWIYYEcRSMYBYlaGJq1jP25uefrhl//6bLdD+10yZPjw

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: vRcvXmx8ExMoRX5PYvB6hVOhXLsVHfqEIXBFFJVqrmFYs6Mo7GtZ1mbkjK9VQbRcKpVswWjzNzHm

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: b0q0co888JCsjKKspt8wNpTVVV1WsmvbwAwThBUDKxEHNwpAcgoBVcBiJrS5JaossUshcI9tLRVu

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: f37u35oG/3z4tfsfe+yu/855NM6ezcMw6M6FiBPO9MLUTBQ6EymBgvJjhvEAA6illHZHpVYXLf6t

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 9s2E6M8opVoNwXbo/O1nRceuPzj4YOf3Tzy6CTlXzHGGMgJKGJjCAC8MwIzFIAx9oExA4DtgWTGQ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: CFgAwAtCyNTUgYsMlxNCMh5nSUP0bHPtvV0ph+xz5OHjd8nUvvaTY49dNmLECGd7hhmrnnkmHQI9

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: zA18JokCxbj2n8gjQxgB8eVaw2DvRyAHMM4z6GZQqY7jQDxRjnFNgahRQeh6hZ277L5hIczeXsPD

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 4gDW0NAQu+GeezKE0gQJw4SkVNAwNEKNdQiVCOMAImEYSgaBL5Xy27Vv710+cqTthaEHsZibchyv

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: d+/eESHka9Vyaf/YqBSd1K/vdRvsYiNLJMANAkina6BUsEEIBoqjEj3gFP0ZgGBMk++IRLVIBYwR

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: IJVdgaYRfyalVEKRgPvB0jg13ujSZefHexx77IsTzz9/u6TTBgwd2nvh0g9fAEvUhehHKz8XCkD6

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: AXAJT5o1NReXctk5IPieOhLQupQ6RKKEAJURoP/wC6U/X3/r2J+MGjzY25YxIeY46/rrG954bd6A

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ku31K9qlXRhlHRljdYSQGqWUKaUUhBC0mogTcLFLQkjAGAkIIU4gIzuKwnwoZdEAkqNh2FKXrm2L

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: x6wP99hzz1X779Pz/ZWUbsTM1Rdtls3gZ+BPTz50wZIPXgyEIYRlgeMHAJ4sMzvKAysRA0aIRtGt

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 6zdAPJna7Jvxwh1R9dFVk4fvcQGkseKhLaeYkq0dGxp+n7Lifzp1+PAPRg8b5mzLg+x22GHnbsy1

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 3cdTSRIxArIyTeX5AH5oJ5k5lHGezIbuFGJwWvXHqFydKNBKluDl87I+U3Plir/PH/d1LQsqc/Dg

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: wSk3Ht/FjmDvlWtWHqpM84RssdCJmkIwxkgURQS/e8vnVUligMSQjHPtGiIZ6GfHNOBToKJIqUiC

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ySiGayoIgkhKKTnnhbhptiWTyYXpeOIDTvl7CUNsgkC67TKZnO849hmnnZbbrNwRjY3xyU8+/nZo

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: mN1FIq5jXQoMosAHwpDlCCGZTEL33ffQie1sSyu0tLQgXwtU8DKIUWWfrX2fjPTPMOMSOQ6kYnGI

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: XB8szkLfcZcJoqbutftet7w0dWrxX1EsPtQO3+/zO1tFo6llQUi1sdAPKijZwBRMSxnWSM/1bo8Y

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: PU0JRnBsaJpVRX8cFHCcqO+3DjjggMOeve/hxV9LqaNGGWv++c8D1re2XsxMYz8vCrv5URhn3CAY

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: M2jcIrhWIDBa/Zy2GlAua9Lvqz8HIj+9YYAAYWUcgS4kZlqI6sGvMGqcc5XP5xUTNIhbViA4d4WU

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: OQtIwc3mN20ukJs4ZozToX2Hl0PflYHv6XAoioLyTRiD0A8gdDx4b+Ei2H3XbnoQqFi8qoOpDhQD

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ZxRZ+Te3TGgrFQAMDoUw4AGlexb84Jr3li555cBjjhnyr4RPU6dOxaimCy4k/V1oNRAQooIZazZN

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Y1JNMpmASHZjhBJW3dXomzU5I0GiC6NECcHnW3U7feVkwYQJE0TPow/vs+itBU8vWrlsdonK0wsQ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 9nQgSpCYQXCeyJQhyCM4PnRtaFlomRSKcCHiayXU1G6sMgd8hhjGocXD9+j+RMyCRCYNlDP9GQzl

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: HNeFvGuTWG2GkphpFpWfLIZOfXMpv3vBc3pxYapPqh8JUSyK/rZTQ/scmoJqDFhybJBBCOlEUgMP

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Rim88/bbcMABB2R79er1UWtLSykMQ4V/v6WSq2Y5xAsJhWQSSjLS6FqaHGjCIi5RPddkN43b58jD

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: r76ssfHrImsjDIOYVmjFlDFKQEYh7pSFIlRvqojWM847cYx5tQcqm0OF5hAVXL5PKCifN7R37y+1

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: IBMmTIgPGT78+zc/9ODt67O5h5rt0g/MuhpLxSwIOQOFNViIvimBUuCDrxR4UQQBAjZZvtC6oOuq

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Xvicqrlmg5vAqdBJjAgxA6GQiMUh35YDXP+UG8CFCbXt66G+Ywe9gIquDU7og6QEnMCXwjBXxWLx

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Cfvtt+/ln8r0SMJne8XcO4LQwzGbYsawnihE0l1TeTgQ5GtRkX+dNSuTzqQ3fK9nr8c+WL70JM5Y

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: O61UqAAsQoGw8oZ07RJYySQESoJCBkxFYAoBhAvi+P7ubmvLjU0zpg/Z57DDzj7lyCM/+ioV/gXT

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 5GEQGjwegwhDG1Fe/Wj6mYLXNr4xfxPvd2g/RulOqMyqKrWNwB2rtC3ESRcNLhYNGzbsczNBaFn2

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: HDhw7xvuu++itlJhOI+ZcSOZJDT0dZICmTF0SUIIrSgMMBUluuAOKoUCn/haCgQtWwUgkOpFKtau

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: HIYqjVdwwQLxe/f5vh9LxOP4RDds2ABrV6+Eom3r+xu4qPBzQSSpF7518slDh99/zZjFywhRn6pb

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: /u2FF27ikryXNGPasuZybcANsdm84EMUhqH/nUyliOt6eyxbsayLQch4Qdk7DEggwwhw51MNWBCN

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: KuBWTK/aWCKuFcxMC0qBB5hMB8sElkzQrOcetKmQe3zavDmjJzQ1Zb5MuSGlkRA8QvehINIuBMp8

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ssykMq/C0KHED9yBWGyOu1QrAD7BSsg900gBi+Sms088+XMrL8647LKOPQcfe+aGYn6yLegIUZNO

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: RJyTousBZwaEodRRAroDVbkwHMN54uVFZaqzWumBz4UqCuiU8TmRclQBruu4nuetMyxzYZcuXV/v

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: se/3Xt1r7x4fduu2u+OXXP7R+x+Stxb8A1YuXwGO50HMNBG/APECxZwgWwfGI3323HfkGdcMfB8q

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: oPBTyh06dKisz6T+2rx+Ha5+MA0BVWID410NChgF23OrTA8Lw/AYqeBCIORmpdRjKgi9CNEqxpmR

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: BAgizfGGtg3VxIQfBmAlU9r3eCpC3wHJdu2YFOLANS0tN9x4222XT548OfF5Dxyl1nFCw7BsBBra

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 1EaykpNl6+o71rzVu7bWBEpPiJSkEUETWEbyaBQR0CCQYpEGVC9CLrdia7sVOfNnX5x1/wdrVj8Q

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: xkSfogxEgMl/w9AmEdOJCOBQYRge4iNFAIk71Khw8/iAGbp2Vb5Q+/hc0NXh5fv+RkLoczXpmqsa

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: GtpfbhrGtDXr1wfvLlrYe9GiRXsu+WhpzbJly2LFYoFqnwxlq4dgUNoeMNfP1/P4r08/6ZQLX3li

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 2htHkCM206efMssYBpxyyYh3l69e3SYI1EZAwQ08zJho1IZjQ8WgOdBgQGoFkyAI2glGzzPN5Fiu

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: YL3v+eeSULbjnJMIJARBCJYZA64o+KEEysuhElZ+IBkiEgnIew4gnwCMmoFjX3LzIw87J1500cSn

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: x49v2Zpy33vvvTAMghZM2yE1alkm2MWSisdi81JmTc7N579nl0r1VialF1DIoByuISnDOGBanvrB

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: +p5779v0WTeAgGmf/of3afbzl4YmG2TELBZhWIrWS5vRMqLVr1El5OMcOKuwXti1EFVZMgokiEBQ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: VC0JozBypIwKgvIV8VRiDhPmOwSiyPa805ubN/WKomgXxhgzLUvDUj8IgFsG5At5SCaSEOcC3EIJ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: OKVS+tHCvbt1Hzflzjuf6Natm/vZZ/R/2kmOPHvEyq6dOz1u53Jh4Dk6v6v9FH4VLUPzMigpw/dQ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: SSxRIaWSfVQpm7s/bsWLdZmaKzmjr/ieF6Gp5PjHfgilfAFMyvXqdorFsp+hBIIoAlxuUjAQiRiJ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: DFazoS3b+PbixfeOb2pKbk25qJDdunVdYheKilXQfGC7eUHYszeeeaazYtmygbFkIo6+kJoCOCbj

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: fRfiVgxU4IN0fTsdi//2hEMOeXXL+2LLyVNz/n7m+mLLMy6Rw0gsxpHQrAIgnePGpEPlPQouMNyR

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: nutCgOSOBDAxhyz1boDI8QISyRcSpjWmvrbuonZ19dekksl5VJE+ge+NzReLD/qe92NCyG5cCMF4

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: OSZH96c4hYLnQKpdBlzf0fiFSxm4udzzA77f9+zXnnnm4a0pdqvKPb9Pn4ADf1QosjpuCAg8t4x8

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: K6GNqihk8wXleI6UEUFnP/BHEUpziVjipyYXf5Z+gHQMMKWgXSajTQWaJcu0tI9E/6StBhIMjAGW

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: yGCKUXJurG1r/fGEx/7489ubmmJbG3yH9g3vCk5DjqFF6EMmlVofM+OvXz1pUlyGsmfRdRgCnmoG

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: CL/Ptx2wCIcENz8YfurpD4waNWozI9U4YUL8ylt+e+ncBfN/Y2TS7cCySKD9Kfpn0H5S+8rKYkfQ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: hA8frRnjXNdEJwwLCC7Wkh2B7TazIHymobZmcDKRuChwo5dz2dyB2ZbWG3PZ7OWFYvFox7Z3Cfww

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: iTfFEKeaaq0uIPwuYjHwy8sfDMqUSdm039962/kzHnjgna09l89VLsox++//Zl0mNU36oYphCk2V

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: QQOtfKDKrlR3HvpgLgSuYOL6XkOhVLw1UmqQaVk3cc4fZwpshPZ2rgAcTZkfgu96yEPrwLyadtNx

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: XRhoWI8+jVmGWLz0w1/efufvTsPCgM+OM24mlpqMt2FaD3dk4Idv9u7f/2NZjDJu6O+m0SqjGuEj

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: u1aTTEHKjIGbL/oNqfS0xlGj8psVO25czR9+f88VLpVjzFSifc5xiBMEgN0JRBLtT7cERfh+y+eA

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 32EXipHvullO2NxEPHFbOpH4WW0y2RhE0a6e447xI++ZIAgui5TchVLKkb3S5hwVyj7JxqFiyxtK

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ldOvWFVSstEKOEGh8OJhffvccM6Pf7zqixT7ucodN26cc0CvA+8WBDbgTemWE1LlUAevKo2Gg8Od

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 4csKIwNqj6JTGu8F3klJxkYZQlwdF2Y2bpgK0XTMsqA2mYbWjc0aeHE0P5UUHIZfGKxjkR41BFjJ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: REPetW96Z9OaPT47zpqMt9EwjLmmMJRTssPdd97lsaljxgSt+Q3tiZK7Ec4IhiGpRByEUlBszUJY

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: cqKkYT15YO/e929W7F13pf/Y9HhjZNCrHCnjkSHAiMeAM6HjzfJGJRoQ4fwRZRNEyWEZMCLhT6Vc

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: wTm/wkqZfRPJ5E8NIWYGUg4ret6ztuff7Uby9AhIOyoMgnNEYgfnh34caDXFiopVm1k+JDIQrjEv

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: VMzxcj26drvkwYfGn/zU+Pu+EpP2uS2cgtLmpBWfR4FI3G3oN7Xv3EKxm/ljVC5WQUi0XQQpIhKC

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: irkyGm4Lej5IOd2KWSMLudzLoJTv2Q44tg0GKyNNVLjOIyuokA2fsFz4VBWjHea+Pn/sKRdeuM+W

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Y2wP7W3LNBcIykKqoNihY8eluNRrUqm0GTNTuA6RBi20ZSHGDUgKC3zPeX9Av35jHho7dj3e439u

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: vTV1z6SJozcV8z/zKIk7WOnMMcFAwPdDMAyrPKeKoEvBiyIwjFTRiODtGGHXJ2LmsEQ8OSXO4ntF

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: YXBNvlR6OG+Xzih4TmcQzJKcUowOsIDPCXzwKhcSECho4nX16RbWQKPjIFQyV1jXMZa646ZzL5gy

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: rM+g3FdR7Bcqd+o99xSplLdJL1ixOaxBknvLeK2ym3WKEPOpnG32FRzpN8Hbu0Fwve05//OLa897

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: tuseewy17dLdBKRHZAQ1mQwErqcXi84woQ/GMEoqsLDITcqyTxYMgczxf531wh+vGDt2c4iEoCpS

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 8JaSMmcKA3eOPr8im8+3LxVLFj6wdDoNNekMuCVblXLZ5Uf2P+ziqffcsxTK4Q57df4rp0SCXs7T

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 8ZQNEaQa6qDkOZp4wNShXSyVwVRlIauyYm0B9NkU4/1imcyAVKb2D8RRHQotG+c0t2yYWvBKI8Ek

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: XVjaIjyTADv0tVVD/4/uprpr8T2QT5RadU9QwTESFesGH40cesZxq/7+xo1Dhgz5WvVdX9h8PeSU

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 09+qTSWnRwHWzkWYPdicKkMF6xWmQ6QymtQIusKhVuqTCOOcF3132FNTZp25n2Xljzl04E0mYfcZ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: QF2nkIeYZeh7VbnhzYDC87XiU8mMDpkiQqhIJfZ/Z/78g7ccY9ISy4J8Pmtxvn7ooEEaNWZqa+MY

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: oiVMAwotLaA8D9y2bFtDuuYX1/7853/fPL9zzz7o9YWLrvYoSeZ8bKeJQTZf1OgXF63vOYCgkmmy

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: QwELJVZ2bBBArzGkef6Gzp3/yUxT+S25q6IoeEAIszsXpol5MTeMSIA0Lsa8VkyTQVAJJXHHYnUp

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: WiedAapwyvrZYvwd4oWVJLJgKTW+azr9ISHka5f/fKFy7x41yuu37/63W0IstBLW5hIcgwkdx0U4

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: eKkAY0DkVKuX3Hxzqak+nkk0vLbwrbtf+HjZo/v06FEzZPDxV3apbz88yOUXqMAPMNWFRD7WP+Hk

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: VSghJpCBMUAFEUQhASIs8AxOF65ecWnjvfe2r47x+vMuXrZrfafnVd5Z2qFnT51CLDqlGp3e8wKw

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: pJKqZM8+4Zhjj1y1YMH0Pn36aHg+6JRTjpn7+oLp8YZ2uwUYKiUS4AdlUkIQBr5tQxLDQLQcRcev

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: IWJmO9O6oF+P3gftn0qMT2REx5plKyZ5zS0fuFReEQnWoDgniPgVpSCYAE4MYCHFUAgSwtQWCUM2

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: zg3dwY99wrghMPkSBL6OJJCwZJ4b1Qlj5olHH3VM61vv3Dl69Oh/KTX6pccm/OCCCzYkDPFktqXV

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: re6uYiVGxf2LA93yVpsRdRV84XvBESjxUIbHP/b4Yxf26tGDvD1zZtPoyy7/mXT9vwkCMs65Cn1P

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: m0LkaLP5PFhmHDw30CQIUpYBo6TFzh8x7bmnj6uODznh/fbc48GEYPOP22MPrThLmDEZBTTIF3zq

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: erOOOfSIC564++7NYcNxl1xivrfkw595APWYCtS4SJZTbLggEER2qG2HqFqGttsSF9b9h/bpf945

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Rx89CWKQ+yhUQ9ev3/gQs4yz7NDfOaSUBoTqBAkudLyT2uLR4oLBNClKKpXSJhfNPs5TL8ZCHjKJ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: OISeq0qtraFFyAfHH3v0FX/8ze/mwzZUrnxp98CMiRPlD44/4a3mXAsTnPcu2SUjnkppc4ylLZjn

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: RQxUnQ5TW1BtFVrcc8pVHJZlcdf3+7355lv7rGlre/W3o0YtveXu8bPn/e0lo5DN7Z5MpuKOa2tT

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Fk8mQKL/VpH236VSCeKIpAkxshtbeoz+xeWPznvxRW2G3/7HPzYo33//4IMP1qYrVlfX03OddMK0

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: xtXWmr+eM3X65rBh8vPPJ56YPPkOj6pTsIXeQ5bNNLXZNRQFGkR6l9m5wrq4ZU1NJ8SF62PxyW6h

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Vc19990ha9avvyFvly6z0onOIUgSVlOIGGvT6qv65CKYjGdgIKunInB9V783TUNHCFgzbXEBFmIO

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: L2j9/n77/fKMYade85vLrtjm4vivdOAJgqv99/re7eD5f4qZlsR6KgRNtl0sk+KyzJ/yLTnULSRh

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: WnoC+UIJXBmSDdmWHz721FOnNTY1GaOHDVtz6YgLrqkVsclRyY0wzMAcsu06UPRsDTxc3aiV0P5Q

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: cBOR5c7F0Nuv+g1YW7QlGdGrR4/pXes7nL7mjX+M/2D2gk/Rlzf/7pY+G+38aS5TVsjKmCGyPRCh

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: AiNUkBaWCkrO6r69+5zzyIQJl64+6rhFHQzDbG1rvXLTpk33gmDH0ZhhlTxXJwXQlUAF6UIl6f6p

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: kLHiZzGSwEWgQRMueNuBwHGhNp6AOKGqec2afMf6dndccMKP773q3HPXbqti4eueQ/W9QYMGrm7b

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 9EjOs3c2kimIJZK6LVLXCKsyg1MVHTURqJD0WOzggZGMa8gfBR4EJcdrH0vddP5ZZz1w7ciRa+6e

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: MqXTDbfcepMdBceLTLI+Mlg5T6kImFSA57jlqo8oAkvJ0AzldateX3Dr1ymLuWLsFYkHnph1Z1vo

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: nRPLpAj6PMxcWUDBy5egNpEqha43+4iBR9w15c7bZukO/aOOOmBDIXdyKfAvS6RTMbQgVjymTarr

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: e5tjfaiSO9Uqiy1GhSATSQ6kcnXmKoz0gpdeAEGpJAWoFft/r8eNv772+icH7L134evo5IvkazV1

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 3XDVVWvnv7f444iQfslkItWWzRKkETFtRcrNvrA5c1pRrv6BlGDoSkpfh03YyxP5Pncdt9/iRYvr

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: Ro8477mrLrkkd92v73pp3fpV7zW3NA/0Aj8REkXwbzH9iiE0mjVdwuP7JCaM4l577f38Yw8+uFVe

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 9bOCDNfkZ58/y6bRxejMMeZEUCgIBREoSBuxdTvV1v/PPbf99pYrz/0Zdvrxky699MA169Y+pIQY

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: IgW1ENUizYjhGc4D8Ue1XLY6d81kKag8j/Irfoay8o7VgEr7dk+5+YKXNMWMvbrscvXLTdOe7drQ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: sE3FeZ+Vr3UO1fnnnx/86szhf6oxzd9sXLrcrk+ngERlWh15T+2D8cJQqNJnhIpG0h7hvylMXQOE

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: qS4jFgeRjFlZzzn5kRdnn3fLhAmZq88flps2/pk/xxT8Lk7ppgQmv4NyHIymz4zHIMCwgXFS8oO9

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 7/j973f6qmNfvGZjr035wq/cUNYRo8yoGQlqgqgAAAvdSURBVJRDnIrIK9rvZJKp6/5w45jJR/ft

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 2/LCu+/Gzx0z5vJVK1c9rJTqGQSBERcWCGC6qSwZT4IKJKTjKfBsr0JPli+qyBYp+PJuRqCIqFui

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 9VIKTEJUUCx81KWh/a9Hjxj5s79P//Pcr1u2+lXka7djzpgxQ47+1f8u/nDpkjCfy+5HGY1T7XMq

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: F60kFyo7F6cY4W4VWNjlA6MMglD35kCEq1wws1QsDtywYVNp7ZIPXrnzzuvDGa+99ua6j1b8Y9Wa

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: NQcSCQ1o+BhyxBgmaQKMQeh58Q7pzEsr33//gy8b812PPJK+54H7xiba1x0SMU3gYk0pmJIou7Xt

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 9ctHXvbzi8bfMat/587hMWeemXj4iSnX5kqlq/0o3MlKJAm2g0a6FIZpK4RECxYLIi7AnQvqExtc

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: bZ6rXmWyR4GFLaVAwc7nsrVW/MHDBxx8yaiTT3nu/LPP/rc1nv3LfT1jx45NjH304Qtdom7k8biB

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: LEyEhV7IwvByzyuucLfCTbNKNYKu1NC2qpJZQnYqDMGIYMmAA3sP/tPEiR9B5WzGbgP6jmjOFe4Q

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: qYwZYXGd52gAg6U0ZqRk7927X/33qVPHftlYd+vfv08h8ppsGXSTFeKAYcFw0X13n932GD3/mWd0

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: JfrQoUONl1auvM6OvFGUsbSnD0pJgeeWqymEpiKlzrFieIeMHFZF6DEpJGDKjd5+pfpCKx4XheNC

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 0jCiXHPr8s47NUw49MgfTHqosfHffhrsv3w84JVXXlm69pwRd8UpvynbvHE10oUWUmoVgIEPEPO3

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: GONVRfPRmJ/TZTusnB/Faoa4hVVqu788f/4TR5955g+ggoBHn3LGY52SNfdJ183b+TwkYpa2AAjK

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: mCHo8lWr9vyyceIiISb7Ud61O+HRCBjmUCeQMclm7t1l11Nemz79Zfy7u5qaGuasWTXeVcHlThSk

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: AwoQT6d0KRCGYrFkCnJ2EXxZ5tBRsRiLG5ZZLnBTUvtTBJdYecIpAd9xMbGQSwr+QoqJK04a8qOj

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: n7j97ju/CcXC9ji19a6ZM83npk8dMmfB/EkRFxnNl1Y6EVSg9FkTSCXKT5mrSrqwwtXihVUYGBrE

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: GFtw89XXDrlo2DBN7N9++6S6cVMm3O1Z7NR8GBFsRgPGIMkMECX/L5eeeMIPv6ig7vIbb+x6+8P3

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: v5GoyzQg4+UWS0ra7sJfXXnV2b8aMUKfEodga9ILf7myEAbX+1SZmE9GRGzni5o2dMNyWh7dAS4u

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: UglxsHgQARQWD8ZNU79iiIP0pe85PgX+Tm0iMXHgUUc1PTJmTOGb7tjf5oM9sf1i5h8eeNoC+kuL

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: kEUxwrygUISMGdflLDplWOGiUaJq96CsFNExVuZyhaHLXnk83uPWceN+umDBAk3fjB7989afnnji

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: PYVNzZuwQyARj5ePQVB64TR80RzwHjNmzTorXVtTjzuqlMurJDM+GHjQwedfc955mrHCyoum2bPP

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: bm5tGc0EN6udAYVcvgII0ckT4DEBShBwijlQtFzTXe5aQM45UjyKQub6LTEF7xme9/ROiczp55xw

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: wtGr5sx54NHGxvy3cRTDdjm1FRuXb57w0KR+++x7SkLR2xqsZMFty+ocqoF+NSqXs6IyuQ7iQSf/

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: qyEEFl5jvIwNZ8BZLOuWLrvmjjsOr95/cL9+8zvUZBqjYqkFC8NS8QSUCkUo2bYJXbt+7kEsD8+Y

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 0aHg2oNUGGEtD8Q5b96prt0NMx95ZH71Yf9x1qzua1o2/TJTV9uAY0BMgH+LKUKMr7F2GJkkRLxI

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 6mfq6jTvDBirF/LgtbX67RPxebTo/O+u7evP6Nau/uTxt407a9mcOU/dXVbqt3YQ93ZvlJ49ezb/

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: xS03Dft47fqzuWn2VpzVISuFfhILx6ptFTprVAlx0CzrszhkCMVsDhKUQX0y/eaJxw4+/bZrrnkf

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: /x77fc+85sqL3vpw6S2punYGxpLcDRf98vQrDxo9eus9R4cNG3bY0pUrnwRBGvySvX6vXXe9buIN

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: N0/ed999fSQorvvtb/d8dPq0u/KeN8iRIUkm0+B4LjAqyqnGcvpNKgqhEMwDKb3Q89pkJNepIFjX

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: uaHDG/vs3X1u9z33XTjuXyT3/52y3Y8fOuKII/DglMev+c1vZk2bNeuQZjt3MaX0UF9FBgl9IiQD

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: xoQO+aNquMSo7oXBmBl7igxCoLmQ6/XIjGkX3nXXXVcitYgKGdHYOOHj9RuGRVL19fDEO+AB5+s/

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: d2e0FYt9/DCoFYoW2tWkr68qFn938x137PTUn2eMb85nBxLDJJh082xHeY4jiaKY7Mru3LnLeqeQ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: ezVG6eJONbXLWnK5Urq+LvuLyy/flKyryx7fu/d2bUnd3vJvOVuqYoo2AcD0S+666y9PNj16tnSD

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: EdIP9gXGTCKAYKU8Nm9FlTgYfR0lvAxQGMP2E+5LOWTBunXXYxsw3ndiY6PduX/fJ1py+QNMYZjJ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: mNnc2tq61XbQEY0j4rNeev8MyxC8lG175aGJk6ZVFYvy0rx5Ry35cEnv2p3at0agVlsKVqcsa0l9

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: PLnEMoylQ08+eTFs2JBtHDMGg3K1JYt/6vPP/3u1sp3kGzm/Ak1108svd5n/6qt9bN870vXsg1qL

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: hQ6EixpqCCMiiklUbaWVERGpznf6Qdi5rv7cRc8++8fqDul7/PHdl61aMZEADKxLZn7/z3nzLt3a

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 7hl40glHvL9y5XSqYNNO8dQ5P5ozZ25jxf9hU/S9j00+vKWtEE/VpdpMxdddfPHF2VrGcmh5voln

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 8k3IN37MUKWKkfIOHTo88fST38/Zpf1c3zsgUNDVC4POQpgxx3EMLN9FOGNROnfsL2849cyfHLuu

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: eo9Bpw/r//7i92fUJtNnvTtv3rOf/Q5U3tgH77t1+erVZ3Vo1+6sxbPnPr8tedEdVb7xI/8qMSle

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: eP7Emialnu24cmH6/nsfyKxesb62JZ9PJSyrXT6b38l33Q5uoRCLm+Gn6pa7d+/xzqolyx494IAe

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 7707b97/+Y4/zZ6d3rhmbaeaZOqJnRs6vr74v1Cx33lBRLu13l382e8mTKjHAretzeHtt9/u3OuQ

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: vhciwfLf/gz/4wR9/Jc1kv03yDb7XPShPXr0IO8BsI5tbaq2tlZit+B/6v/pZEeSL1Qumr+XVqww

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: X54xo27ua6/VOGGYAA61MW50/2D5R3sKxtt5UVgvwzARoYnUh1IwPJ8h70ZBThCytnPnndd4jvth

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: fW1tq18seBBA8cZf/SpbTKXyg7t3367J6a0Jkh9hGIogCBR0AoC1AIjM2vm+MnbdlcDatdAixObn

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: gAliJLUNzokfhqqaMNaf30L8Dh22eL8KGtwG+dYee4TDCPlax/kuWLAgnkwmWZaxzWMIsUTkM1Ib

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: RQpPMNjyp+u3GPNnBTsst6pcVOroceNqm5qmnOBT6B9FsGfMsjq7QVhr28UYZUIYJme6+D4KdNjC

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: DIZsBDhuSVdFmhaeWRWpSAbSMowwCvzAZGbJ5LQlsL3VnPHlFmeLjjjiqFmFXr2WTv2CzvZtkcHD

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: fjLszUXv/cSMWxKPMJAqUoZhKddxpBsFhDFBDMGp73t4coZ+HliArvlwzvEEGd1FSLD2Flk23btD

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: kTvVZw3p4yZASRXJsEunTvN+MfP5h7+qgmfPnm1dds0vb3b8oHMESmAMFip9pg0WPejuJPweQ1vB

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: SJcCRPoEhEp7YXkESm9S7GbUw6Ggz0Fimg/81CoSdzY17dFl4CEjck7pcNt39zZjCRM7s/C4g1Cf

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: zmJAMp0B2ynpu2LJaYgdCZgJwiMBhAGCG+D4LlDOiRAGiwCYH4WmJJDE1hCPqH1U6CkHRPT4M9M3

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: xZ5/7oWDTj7xKVJy57w2c+Z2y57gIj1gQN+uIcgTCSU8Ukyf+4gneaHJwPMssfjOx6MHRPxTGtEP

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: UVGlsJgcz2bGA9j0MQtY7xPqY44y6TTJZ7MKO9wj34cVHy8zks8992jl418qmzZtCop2qU/J8w8W

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: MUtEWPQgmPLDSHc64vkKOmesVVrujy6f5YGH5ZYPS0Ep/78ZCC45ffSS5ByUDOH/AcRNdcEflr82

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: AAAAAElFTkSuQmCC

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: --b2=_iL46DtX8gxwLJ4AbbSmz8kpOFIVK09WbLBoJA8MOLA--

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: --b1=_iL46DtX8gxwLJ4AbbSmz8kpOFIVK09WbLBoJA8MOLA--

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:46] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:01:47] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbdj6QlMz2Srx9

[2025-07-19 08:01:47] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:01:47] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:01:47] Email sent <NAME_EMAIL>
[2025-07-19 08:01:47] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:01:47] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:01:47] Global sendEmail called for Admin <<EMAIL>>
[2025-07-19 08:01:47] Initial email body length: 1859 characters
[2025-07-19 08:01:47] Email type: Regular
[2025-07-19 08:01:47] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:01:47] Set Reply-To address: <EMAIL>
[2025-07-19 08:01:47] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:01:47] Set HTML body (1859 chars) and plain text alternative (984 chars)
[2025-07-19 08:01:47] Attempting to send email to: <EMAIL> with subject: Birthday Email Report: 4 sent, 0 failed
[2025-07-19 08:01:47] Email content validation: Body length=1859, HTML=Yes
[2025-07-19 08:01:48] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:01:48] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:01:48] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:01:48] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:01:48] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:01:48] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:48] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:01:48] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:01:48] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:01:48] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:01:49] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:01:49] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:01:49] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:01:47 +0200

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: To: Admin <<EMAIL>>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Email Report: 4 sent, 0 failed

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_8jfmMCsQLXXlstJR8touY9vzfguwCMtVJynYZKWU"

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: --b1=_8jfmMCsQLXXlstJR8touY9vzfguwCMtVJynYZKWU

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=us-ascii

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; } table { border-collapse: collapse; width: 100%; margin-bottom: 20px; } th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } th { background-color: #f2f2f2; font-weight: bold; } tr:nth-child(even) { background-color: #f9f9f9; } h2, h3 { color: #333; margin-top: 20px; } .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; } .success { color: #28a745; } .error { color: #dc3545; } Birthday Email Report This is an automated report of birthday emails sent by the system. Total Sent: 4 Total Failed: 0 Date/Time: 2025-07-19 08:01:47 Sent Emails: <EMAIL> <EMAIL> <EMAIL> <EMAIL> Templatebirthday_notificationFailed Emails: 0No emails failed to send.

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: --b1=_8jfmMCsQLXXlstJR8touY9vzfguwCMtVJynYZKWU

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=us-ascii

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:         <html>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:         <head>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:             <style>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 th { background-color: #f2f2f2; font-weight: bold; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 tr:nth-child(even) { background-color: #f9f9f9; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 h2, h3 { color: #333; margin-top: 20px; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 .success { color: #28a745; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 .error { color: #dc3545; }

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:             </style>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:         </head>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:         <body>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:             <h2>Birthday Email Report</h2>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:             <div class="summary">

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p>This is an automated report of birthday emails sent by the system.</p>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Sent:</strong> <span class="success">4</span></p>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Failed:</strong> <span class="error">0</span></p>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Date/Time:</strong> 2025-07-19 08:01:47</p>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:             </div><h3>Sent Emails: 4</h3><table border="1" cellpadding="5" style="border-collapse: collapse;"><tr><th>Member</th><th>Email</th><th>Template</th><th>Type</th></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_reminder</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr></table><h3>Failed Emails: 0</h3><p>No emails failed to send.</p></body></html>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: --b1=_8jfmMCsQLXXlstJR8touY9vzfguwCMtVJynYZKWU--

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:01:49] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbdm4jF4z5Z5ml

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:01:50] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:01:50] Email sent <NAME_EMAIL>
[2025-07-19 08:01:50] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:01:50] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

