[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:             <h2>Birthday Email Report</h2>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:             <div class="summary">

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p>This is an automated report of birthday emails sent by the system.</p>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Sent:</strong> <span class="success">4</span></p>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Failed:</strong> <span class="error">0</span></p>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Date/Time:</strong> 2025-07-19 08:01:47</p>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER:             </div><h3>Sent Emails: 4</h3><table border="1" cellpadding="5" style="border-collapse: collapse;"><tr><th>Member</th><th>Email</th><th>Template</th><th>Type</th></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_reminder</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr></table><h3>Failed Emails: 0</h3><p>No emails failed to send.</p></body></html>

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: --b1=_8jfmMCsQLXXlstJR8touY9vzfguwCMtVJynYZKWU--

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:01:49] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbdm4jF4z5Z5ml

[2025-07-19 08:01:49] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:01:50] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:01:50] Email sent <NAME_EMAIL>
[2025-07-19 08:01:50] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:01:50] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye


================================================================================
[2025-07-19 08:11:52] ALL BIRTHDAY FUNCTIONS TEST: Starting comprehensive test
================================================================================


================================================================================
[2025-07-19 08:12:17] ALL BIRTHDAY FUNCTIONS TEST: Starting comprehensive test
================================================================================


================================================================================
[2025-07-19 08:13:11] ALL BIRTHDAY FUNCTIONS TEST: Starting comprehensive test
================================================================================


================================================================================
[2025-07-19 08:14:18] REAL BIRTHDAY EMAIL TEST: Starting test
================================================================================

[2025-07-19 08:14:18] Global sendEmail called for Test User <<EMAIL>>
[2025-07-19 08:14:18] Initial email body length: 2528 characters
[2025-07-19 08:14:18] Birthday notification detected via explicit flag
[2025-07-19 08:14:18] Birthday member data extracted: Array
(
    [name] => Test Birthday Celebration
    [email] => 
    [image_path] => uploads/members/test_birthday.jpg
    [photo_url] => http://localhost/campaign/church/uploads/members/test_birthday.jpg
)

[2025-07-19 08:14:18] Email type: Birthday Notification
[2025-07-19 08:14:18] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:14:18] Set Reply-To address: <EMAIL>
[2025-07-19 08:14:18] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:14:18] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:14:18] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:14:18] SYSTEMATIC FIX: Found 1 images referenced in HTML
[2025-07-19 08:14:18] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:14:18] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:14:18] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:14:18] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_20090a6b5e83fb7ca3c76948abd9a348, mime=image/jpeg
[2025-07-19 08:14:18] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:14:18] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/members/test_birthday.jpg as CID: birthday_image_20090a6b5e83fb7ca3c76948abd9a348
[2025-07-19 08:14:18] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:14:18] SYSTEMATIC FIX: SUCCESS - All original URLs replaced with CIDs
[2025-07-19 08:14:18] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:14:18] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:14:18]   - Email type: Birthday Notification
[2025-07-19 08:14:18]   - Images found in HTML: 1
[2025-07-19 08:14:18]   - Images successfully embedded: 1
[2025-07-19 08:14:18]   - Processed image URLs: 1
[2025-07-19 08:14:18]   - Processed image paths: 1
[2025-07-19 08:14:18] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:14:18]   - URL: http://localhost/campaign/church/uploads/members/test_birthday.jpg -> CID: birthday_image_20090a6b5e83fb7ca3c76948abd9a348
[2025-07-19 08:14:18]   - Embedded images: http://localhost/campaign/church/uploads/members/test_birthday.jpg
[2025-07-19 08:14:18] Set HTML body (2513 chars) and plain text alternative (1026 chars)
[2025-07-19 08:14:18] Attempting to send email to: <EMAIL> with subject: 🎉 Happy Birthday Test Birthday! 🎂
[2025-07-19 08:14:18] Email content validation: Body length=2513, HTML=Yes
[2025-07-19 08:14:19] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:14:19] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:14:19] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:14:19] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:14:19] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:14:19] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:14:20] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:14:20] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:14:20] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:14:20] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:14:20] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:14:20] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:14:20] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:14:20] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:14:21] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:14:18 +0200

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: To: Test User <<EMAIL>>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Subject: =?UTF-8?Q?=F0=9F=8E=89_Happy_Birthday_Test_Birthday!_=F0=9F=8E=82?=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_ApyXMJwTQ8FWdmnZac5Pf7K20y62x5BjMidyUVWr5E"

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ApyXMJwTQ8FWdmnZac5Pf7K20y62x5BjMidyUVWr5E

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Birthday Celebration body { font-family: Arial, sans-serif; margin: 0; padd=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: ing: 20px; background-color: #f8f9fa; } .container { max-width: 600px; marg=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: in: 0 auto; background-color: white; padding: 30px; border-radius: 10px; bo=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: x-shadow: 0 4px 8px rgba(0,0,0,0.1); } .header { text-align: center; margin=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: -bottom: 30px; } .member-photo { text-align: center; margin: 20px 0; } .bir=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: thday-details { background-color: #fff3cd; padding: 20px; border-radius: 8p=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: x; margin: 20px 0; } .footer { text-align: center; margin-top: 30px; color:=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:  #666; } =F0=9F=8E=89 Happy Birthday Celebration! =F0=9F=8E=89 Let's celebr=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: ate a cherished member of our church family! =F0=9F=92=92 Dear Test User, =

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=82 We are thrilled to celebrate Test Birthday's birthday today! =

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 =F0=9F=8E=88 Birthday: Saturday, July 19, 2025 =F0=9F=8E=82 Tu=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: rning 40 years =F0=9F=8E=81 Ways to Bless Test Birthday: =F0=9F=92=8C Send =

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: a heartfelt birthday message =F0=9F=99=8F Pray for their growth and blessin=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: gs =F0=9F=93=96 Share a scripture that uplifts them =F0=9F=8E=81 Gift them =

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: something meaningful =F0=9F=8E=82 With blessings from Freedom Assembly Chur=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: ch =F0=9F=92=92

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ApyXMJwTQ8FWdmnZac5Pf7K20y62x5BjMidyUVWr5E

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_ApyXMJwTQ8FWdmnZac5Pf7K20y62x5BjMidyUVWr5E";

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: --b2=_ApyXMJwTQ8FWdmnZac5Pf7K20y62x5BjMidyUVWr5E

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:     <html>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:     <head>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:         <title>Birthday Celebration</title>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:         <style>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f8f9fa; }

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             .container { max-width: 600px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             .header { text-align: center; margin-bottom: 30px; }

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             .member-photo { text-align: center; margin: 20px 0; }

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             .birthday-details { background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; }

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             .footer { text-align: center; margin-top: 30px; color: #666; }

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:         </style>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:     </head>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:     <body>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:         <div class="container">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             <div class="header">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 <h1>🎉 Happy Birthday Celebration! 🎉</h1>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Let's celebrate a cherished member of our church family! 💒</p>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             <div class="content">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="greeting">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     <p>Dear Test User,</p>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     <p>🎂 We are thrilled to celebrate Test Birthday's birthday today! 🎉</p>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="member-photo">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     <img src="cid:birthday_image_20090a6b5e83fb7ca3c76948abd9a348" alt="Test Birthday" style="display:block; width:160px; height:160px; border-radius:50%; margin:15px auto; object-fit:cover; border:6px solid #fff; box-shadow:0 4px 8px rgba(0,0,0,0.1);">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-details">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="birthday-date">🎈 Birthday: Saturday, July 19, 2025</div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="birthday-age">🎂 Turning 40 years</div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="suggestions">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     <h3>🎁 Ways to Bless Test Birthday:</h3>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     <ul>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                         <li>💌 Send a heartfelt birthday message</li>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                         <li>🙏 Pray for their growth and blessings</li>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                         <li>📖 Share a scripture that uplifts them</li>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                         <li>🎁 Gift them something meaningful</li>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     </ul>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="footer">

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                     <p>🎂 With blessings from <strong>Freedom Assembly Church</strong> 💒</p>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:     </body>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER:     </html>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: --b2=_ApyXMJwTQ8FWdmnZac5Pf7K20y62x5BjMidyUVWr5E

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/jpeg; name=

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_20090a6b5e83fb7ca3c76948abd9a348>

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: /9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS4wICh1c2luZyBJSkcg

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: SlBFRyB2ODApLCBxdWFsaXR5ID0gOTAK/9sAQwADAgIDAgIDAwMDBAMDBAUIBQUEBAUKBwcGCAwK

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: DAwLCgsLDQ4SEA0OEQ4LCxAWEBETFBUVFQwPFxgWFBgSFBUU/9sAQwEDBAQFBAUJBQUJFA0LDRQU

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: FBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQU/8AAEQgAyADI

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: AwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMF

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: BQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkq

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: NDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqi

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: o6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/E

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: AB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMR

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: BAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVG

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: R0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKz

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: tLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/aAAwDAQACEQMRAD8A

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: +4KKKK4j9TCiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACi

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: iigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKK

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: KACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooo

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: AKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigA

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: ooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAorx7WP2kraz8

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: ceOfC2keA/F3iu/8GRW02rSaLFZMqrPb+fEIkluo5JWKhhtRC25cAHK5PiB+1F4W8D/CSx+J1nYa

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: p4v8C3MSzPq/h/7My26tKkSCSOeeKTcZJNhVVZkZHDhCKdmYOvTSbvt+m/3dex7DRXk/ir9oKPwh

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: rPgXQrzwL4ol8T+MPt32DQ4G04zxfZFDy+bIbsQjKMHXbI2RwcN8tGh/tBR+JL7xNo+neBfFFx4r

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 8NXdrb6r4c3act1bR3EBmhn8xrsW7xsoIwkxcHqgHNFmHtoXtfX0fa9vW2tj1iivnu1/bR8P3HwW

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: vPivJ4J8XWvgWCISR6jKmnlrlvtiWnlpEt4ZAwkZj86qu2NjnO0N2vxh/aF8OfBLVtF0/XLLVLub

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: VtP1bUoG0+KN1WPTrX7VOG3yLhmQYQAEFupUc0WYvrFJx5r6afjt956fRXj3hb9prRtdn8GnVfDX

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: iHwfpnjKJH0DV9cW0+x30kiLJDb74LiXyppEYsiShC+xlXLDbVn/AIad8D/8LU/4Qbz73zftf9lf

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 2/8AZT/Y/wDauN39l/a87ftez5vL6Z+Td5nyUWY/b07X5j1iivJ5f2mPCNl4c+Jur6il7o//AAr6

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 7mtdVsdSEMF1LtQNDJDG0nzR3O4CBnKeaeBXo3hrWv8AhJPDmlav9gvdL+32kV19h1KHybq23oG8

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: uaPJ2SLnDLk4IIpWLjUjN2izSooooNAooooAKKKKACiiigAooooAKKKKACiiigAooooA+IvGvgjT

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: pvjv+0fP4y0D4kvo+v6fpcGlSeDbLViuohdMaOdFNsv2eVgzKgFzmMMWB4L1pftA6P46l/YHtPAv

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: iDw3Pf8AxGvtPsoBpXg7RJ7m2jFveW77G+zRNDAywKuQCsZdZBFlQAPsuiq5jz3hFaaT+K/4u/z8

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: j5O/aE8C33x++MXwButGk8a+HtBH/CQfbNf0e0utKvtM/wBHjEe9poQ1v5jxFB5ijerHbkMDXa/s

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: teFb7wFfeOvDWv6Bev4rsLu1F/47uPtU0fiuIwYtp1nuXd/MjjXZJArtFE5bYcOQPfKKV9LGscOl

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: U9rfV/5Jfp+h8Ef8K18Xf8OqP+ES/wCEV1v/AISv/oBf2dN9u/5Dnm/6jbv/ANX8/T7vPTmtr9rP

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 4FahaeMvBs/hHSvF3iKE+GvGMF3JcajqWtLBJLo7x26AzyS+U0jsUULtMjYHzEDH27RT5mYvBQcF

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: FvZRX/gLv+J8aWmj6z8UfAP7NHgfSvDfiGw1PwdqGh65r95rmiXem2djHp9sEmh82eJPNmd5AqLE

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: HB2sWKqN1eX/APCp/i5/w7Z/4Rv7D/3J/wDwit3/AG5/yGPM+/8AaP8Atr/x7f6vj/br9G6KOYTw

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: UZX5pbpr77L9D5y+N3w1/wCEk/a2/Z98QL4V/tSysP7Z/tPVBp3nR22y2V7LzpdpCbZi7RbiMOSV

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: +bNfRtFFTe52QpqEpSX2nf8ABL9AooooNgooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKA

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: M3xFoFr4o0a40y8lvYbafbvfTr+eynG1gw2zQOkicgZ2sMjIOQSD8aeAoNQPwe/aZ8RyeKfF0+se

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: Edb8VaXok9x4q1KVbO3gs/3CiNrgo7IXYq7qzhsNuyoI+3a8w0j9nrw5ovgv4l+GIL3VGsPH+oan

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: qWqSSSxmWGS+jEcwgIjAVQB8oYOQepaqTscdek6kk0uj/wCAcD+yrps+ueEvA+v6povxCg1KTw/a

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 30mua94vlvNO1CaS3QOy2o1GX7/mNIokgULjOEYKK848rxD8KPhj/bPxlm+KGl69Yar5mq/EDw74

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: pjm0uPfqWIZo9Pa7ZTblHiQw/YD8hYeVmvo34cfB+8+GtroWnWvxC8UapoOjWiWNto2ow6Z5BhSL

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: y41Z4rNJjtAUg+YCSo3EjIPOeIv2XbHxf4YuPCmvePvGut+C7i7W4m8P6jeWtwksa3IuFga7e3N4

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: YwyqoJuN4UAb+Kd9TndCXs0ktUn2tfTV/wCe5wPxn8LCx/ak+C2h2niHxdZ6P4ul1+bWbK28W6pF

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: FO0Nos0QRVuB5Kq7EhYti44xgAV9P6bp8Wk6da2MDTvDbRJCjXM7zylVAALySFndsDlmJYnkkk5r

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: k/FXwm0jxf8AEnwL43vLm9i1Xwf9u+wQwOggl+1wiGXzQULHCqCu1lweueldrUt3OulT5JTdt3p6

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: WX63CiiikdIUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: UUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRR

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: RQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFF

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: ABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUA

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: FFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAU

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: UUUAf//Z

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: --b2=_ApyXMJwTQ8FWdmnZac5Pf7K20y62x5BjMidyUVWr5E--

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ApyXMJwTQ8FWdmnZac5Pf7K20y62x5BjMidyUVWr5E--

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:14:21] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbwD0nw0z5ZBhQ

[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:14:21] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:14:21] Email sent <NAME_EMAIL>
[2025-07-19 08:14:21] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:14:21] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:15:44] Global sendEmail called for Ndivhuwo Machiba <<EMAIL>>
[2025-07-19 08:15:44] Initial email body length: 2297 characters
[2025-07-19 08:15:44] Birthday notification detected via explicit flag
[2025-07-19 08:15:44] Birthday member data extracted: Array
(
    [name] => Ndivhuwo Machiba
    [email] => <EMAIL>
    [image_path] => uploads/685dc5657df2f.png
    [photo_url] => http://localhost/campaign/church/uploads/685dc5657df2f.png
)

[2025-07-19 08:15:44] Email type: Birthday Notification
[2025-07-19 08:15:44] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:15:44] Set Reply-To address: <EMAIL>
[2025-07-19 08:15:44] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:15:44] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:15:44] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:15:44] SYSTEMATIC FIX: Found 1 images referenced in HTML
[2025-07-19 08:15:44] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/track.php?id=email_687b3810590f90.68678019&mid=29
[2025-07-19 08:15:44] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:15:44] ONE-TIME FIX: No birthday image referenced in HTML body
[2025-07-19 08:15:44] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:15:44] SYSTEMATIC FIX: WARNING - 1 original URLs still present in HTML!
[2025-07-19 08:15:44] SYSTEMATIC FIX: Remaining URL: http://localhost/campaign/church/track.php?id=email_687b3810590f90.68678019&mid=29
[2025-07-19 08:15:44] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:15:44] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:15:44]   - Email type: Birthday Notification
[2025-07-19 08:15:44]   - Images found in HTML: 1
[2025-07-19 08:15:44]   - Images successfully embedded: 0
[2025-07-19 08:15:44]   - Processed image URLs: 0
[2025-07-19 08:15:44]   - Processed image paths: 0
[2025-07-19 08:15:44] Set HTML body (2297 chars) and plain text alternative (553 chars)
[2025-07-19 08:15:44] Attempting to send email to: <EMAIL> with subject: Celebrate with us! Ndivhuwo's birthday is coming up soon!
[2025-07-19 08:15:44] Email content validation: Body length=2297, HTML=Yes
[2025-07-19 08:15:45] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:15:45] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:15:45] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:15:45] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:15:45] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:15:45] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:15:45] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:15:45] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:15:46] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:15:46] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:15:46] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:15:46] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:15:44 +0200

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: To: Ndivhuwo Machiba <<EMAIL>>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Subject: Celebrate with us! Ndivhuwo's birthday is coming up soon!

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_LUrL8Ooihuyr8MqakdbGrvR1yG9sWzZ85bcKCVpVuk"

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: --b1=_LUrL8Ooihuyr8MqakdbGrvR1yG9sWzZ85bcKCVpVuk

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 🎉 Birthday Countdown! 🎂 🎈 Special Birthday Reminder! 🎈 Dear Ndivhuwo Machiba, 🎊 Your special day is just today away! 🎊 📅 Your birthday falls on Saturday, July 19, 2025, and we can't wait to celebrate YOU! 🥳 🙏 At Freedom Assembly Church, we cherish and pray for abundant blessings in your life. 💖✨ 🎊 Happy Early Birthday! 🎊 🎂 We look forward to celebrating with you on Saturday, July 19, 2025! 🎂 💌 With Love & Prayers,🌟 Freedom Assembly Church Team 🌟 🎶 "May the Lord bless you and keep you..." 🎶

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: --b1=_LUrL8Ooihuyr8MqakdbGrvR1yG9sWzZ85bcKCVpVuk

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; color: #333; background-color: #f9f9f9; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center; margin-bottom: 20px;">

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <h1 style="color: #e67e22; font-size: 32px; font-weight: bold; margin: 0;">🎉 Birthday Countdown! 🎂</h1>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:     <div style="background-color: #fff; padding: 25px; border-radius: 12px; box-shadow: 0 5px 15px rgba(0,0,0,0.2); text-align: center;">

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <h2 style="color: #3498db; font-size: 26px; font-weight: bold; margin-bottom: 15px;">🎈 Special Birthday Reminder! 🎈</h2>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 18px; color: #444;">Dear <strong>Ndivhuwo Machiba</strong>,</p>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 18px; color: #555;">🎊 Your special day is just <strong>today</strong> away! 🎊</p>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 18px; color: #555;">📅 Your birthday falls on <strong>Saturday, July 19, 2025</strong>, and we can't wait to celebrate YOU! 🥳</p>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 18px; color: #555;">🙏 At <strong>Freedom Assembly Church</strong>, we cherish and pray for abundant blessings in your life. 💖✨</p>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <div style="text-align: center; margin: 30px 0;">

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:             <span style="display: inline-block; background-color: #e67e22; color: white; padding: 14px 30px; border-radius: 8px; font-size: 20px; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">🎊 Happy Early Birthday! 🎊</span>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <p style="font-size: 18px; color: #2c3e50;">🎂 We look forward to celebrating with you on <strong>Saturday, July 19, 2025</strong>! 🎂</p>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <div style="margin-top: 30px; padding-top: 20px; border-top: 2px solid #ddd;">

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:             <p style="color: #666; font-size: 16px;">💌 With Love & Prayers,<br><strong>🌟 Freedom Assembly Church Team 🌟</strong></p>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:     <div style="text-align: center; margin-top: 20px; font-size: 14px; color: #666;">

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:         <p>🎶 "May the Lord bless you and keep you..." 🎶</p>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: </div>

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: <img src="http://localhost/campaign/church/track.php?id=email_687b3810590f90.68678019&mid=29" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: --b1=_LUrL8Ooihuyr8MqakdbGrvR1yG9sWzZ85bcKCVpVuk--

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:15:46] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbxs5H4Zz2SrxH

[2025-07-19 08:15:46] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:15:47] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:15:47] Email sent <NAME_EMAIL>
[2025-07-19 08:15:47] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:15:47] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:15:47] Global sendEmail called for Admin <<EMAIL>>
[2025-07-19 08:15:47] Initial email body length: 1534 characters
[2025-07-19 08:15:47] Email type: Regular
[2025-07-19 08:15:47] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:15:47] Set Reply-To address: <EMAIL>
[2025-07-19 08:15:47] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:15:47] Set HTML body (1534 chars) and plain text alternative (794 chars)
[2025-07-19 08:15:47] Attempting to send email to: <EMAIL> with subject: Birthday Email Report: 1 sent, 0 failed
[2025-07-19 08:15:47] Email content validation: Body length=1534, HTML=Yes
[2025-07-19 08:15:48] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:15:48] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:15:48] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:15:48] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:15:48] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:15:48] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:15:48] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:15:48] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:15:48] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:15:48] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:15:49] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:15:49] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:15:49] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:15:47 +0200

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: To: Admin <<EMAIL>>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Email Report: 1 sent, 0 failed

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_ZQwNlUbdl9NuifHAo3Z3BXTUd83hctI2ud6nXvI"

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ZQwNlUbdl9NuifHAo3Z3BXTUd83hctI2ud6nXvI

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=us-ascii

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; } table { border-collapse: collapse; width: 100%; margin-bottom: 20px; } th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } th { background-color: #f2f2f2; font-weight: bold; } tr:nth-child(even) { background-color: #f9f9f9; } h2, h3 { color: #333; margin-top: 20px; } .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; } .success { color: #28a745; } .error { color: #dc3545; } Birthday Email Report This is an automated report of birthday emails sent by the system. Total Sent: 1 Total Failed: 0 Date/Time: 2025-07-19 08:15:47 Sent Emails: <EMAIL> Templatebirthday_reminderFailed Emails: 0No emails failed to send.

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ZQwNlUbdl9NuifHAo3Z3BXTUd83hctI2ud6nXvI

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=us-ascii

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:         <html>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:         <head>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:             <style>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 th { background-color: #f2f2f2; font-weight: bold; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 tr:nth-child(even) { background-color: #f9f9f9; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 h2, h3 { color: #333; margin-top: 20px; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 .success { color: #28a745; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 .error { color: #dc3545; }

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:             </style>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:         </head>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:         <body>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:             <h2>Birthday Email Report</h2>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:             <div class="summary">

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p>This is an automated report of birthday emails sent by the system.</p>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Sent:</strong> <span class="success">1</span></p>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Failed:</strong> <span class="error">0</span></p>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Date/Time:</strong> 2025-07-19 08:15:47</p>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER:             </div><h3>Sent Emails: 1</h3><table border="1" cellpadding="5" style="border-collapse: collapse;"><tr><th>Member</th><th>Email</th><th>Template</th><th>Type</th></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_reminder</td></tr></table><h3>Failed Emails: 0</h3><p>No emails failed to send.</p></body></html>

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: --b1=_ZQwNlUbdl9NuifHAo3Z3BXTUd83hctI2ud6nXvI--

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:15:49] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbxw4GRHz8fg6w

[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:15:49] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:15:49] Email sent <NAME_EMAIL>
[2025-07-19 08:15:49] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:15:50] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:15:50] Global sendEmail called for Sandra Stern <<EMAIL>>
[2025-07-19 08:15:50] Initial email body length: 6420 characters
[2025-07-19 08:15:50] Birthday notification detected via explicit flag
[2025-07-19 08:15:50] Birthday member data extracted: Array
(
    [name] => Ndivhuwo Machiba
    [email] => <EMAIL>
    [image_path] => uploads/685dc5657df2f.png
    [photo_url] => http://localhost/campaign/church/uploads/685dc5657df2f.png
)

[2025-07-19 08:15:50] Email type: Birthday Notification
[2025-07-19 08:15:50] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:15:50] Set Reply-To address: <EMAIL>
[2025-07-19 08:15:50] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:15:50] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:15:50] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:15:50] SYSTEMATIC FIX: Found 2 images referenced in HTML
[2025-07-19 08:15:50] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:15:50] SYSTEMATIC FIX: Referenced image 2: http://localhost/campaign/church/track.php?id=email_687b381618fb83.98518453&mid=30
[2025-07-19 08:15:50] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:15:50] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:15:50] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_e64e514cb38cc9e42d6a93de520a9375, mime=image/png
[2025-07-19 08:15:50] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:15:50] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/685dc5657df2f.png as CID: birthday_image_e64e514cb38cc9e42d6a93de520a9375
[2025-07-19 08:15:50] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:15:50] SYSTEMATIC FIX: WARNING - 1 original URLs still present in HTML!
[2025-07-19 08:15:50] SYSTEMATIC FIX: Remaining URL: http://localhost/campaign/church/track.php?id=email_687b381618fb83.98518453&mid=30
[2025-07-19 08:15:50] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:15:50] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:15:50]   - Email type: Birthday Notification
[2025-07-19 08:15:50]   - Images found in HTML: 2
[2025-07-19 08:15:50]   - Images successfully embedded: 1
[2025-07-19 08:15:50]   - Processed image URLs: 1
[2025-07-19 08:15:50]   - Processed image paths: 1
[2025-07-19 08:15:50] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:15:50]   - URL: http://localhost/campaign/church/uploads/685dc5657df2f.png -> CID: birthday_image_e64e514cb38cc9e42d6a93de520a9375
[2025-07-19 08:15:50]   - Embedded images: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:15:50] Set HTML body (6413 chars) and plain text alternative (2601 chars)
[2025-07-19 08:15:50] Attempting to send email to: <EMAIL> with subject: Birthday Celebration! Ndivhuwo
[2025-07-19 08:15:50] Email content validation: Body length=6413, HTML=Yes
[2025-07-19 08:15:50] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:15:50] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:15:50] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:15:50] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:15:51] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:15:51] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:15:51] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:15:51] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:15:51] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:15:51] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:15:51] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:15:51] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:15:52] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:15:52] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:15:50 +0200

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: To: Sandra Stern <<EMAIL>>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Celebration! Ndivhuwo

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_C6MSGDrd21TgPFvDPuto2LaLzWJj65K0zyvcwpKdbm0"

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: --b1=_C6MSGDrd21TgPFvDPuto2LaLzWJj65K0zyvcwpKdbm0

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Celebrate Ndivhuwo's Birthday! =F0=9F=8E=89 body { margin: 0; =

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: padding: 0; font-family: 'Helvetica Neue', Arial, sans-serif; background: l=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: inear-gradient(180deg, #fceabb 0%, #f8b500 100%); color: #333; line-height:=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  1.6; } .container { max-width: 600px; margin: 40px auto; padding: 20px; } =

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ..card { background: #ffffff; border-radius: 15px; box-shadow: 0 15px 35px r=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: gba(0, 0, 0, 0.15); overflow: hidden; width: 100%; text-align: center; } .h=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: eader { background: linear-gradient(135deg, #ff512f, #dd2476); padding: 40p=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: x; color: #ffffff; border-radius: 15px 15px 0 0; } .header h1 { font-size: =

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 34px; margin: 0; font-weight: bold; } .header p { font-size: 18px; margin-t=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: op: 10px; } .content { padding: 30px; } .photo { width: 150px; height: 150p=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: x; border-radius: 50%; border: 5px solid #ff512f; overflow: hidden; margin:=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  20px auto; box-shadow: 0 10px 20px rgba(255, 81, 47, 0.4); } .photo img { =

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: width: 100%; height: 100%; object-fit: cover; } .member-name { font-size: 2=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 6px; font-weight: bold; color: #ff512f; margin: 15px 0; } .birthday-details=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  { background: #ffe5d9; border-radius: 12px; padding: 20px; margin: 25px 0;=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  display: inline-block; width: 100%; } .birthday-date { font-size: 20px; fo=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: nt-weight: bold; color: #dd2476; } .birthday-age { font-size: 18px; font-we=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ight: bold; background: #ff512f; color: #ffffff; padding: 8px 16px; border-=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: radius: 30px; display: inline-block; margin-top: 10px; } .countdown { font-=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: size: 18px; color: #333; margin-top: 12px; } .suggestions { background: #ff=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: dfba; border-radius: 12px; padding: 20px; margin: 25px 0; text-align: left;=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  } .suggestions h3 { color: #ff512f; font-size: 20px; margin-bottom: 10px; =

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: } .suggestions ul { list-style: none; padding: 0; margin: 0; } .suggestions=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  li { padding: 10px 0; border-bottom: 1px solid #ffb199; font-size: 18px; c=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: olor: #555; } .suggestions li:last-child { border-bottom: none; } .action-b=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: utton { display: inline-block; background: linear-gradient(135deg, #ff512f,=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  #dd2476); color: #ffffff; text-decoration: none; padding: 14px 35px; borde=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: r-radius: 30px; font-size: 18px; font-weight: bold; margin: 20px 0; box-sha=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: dow: 0 8px 20px rgba(221, 36, 118, 0.4); } .footer { background: #f9f9f9; p=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: adding: 20px; text-align: center; font-size: 16px; color: #777; border-top:=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  1px solid #ffb199; } .church-name { color: #dd2476; font-weight: bold; } .=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: unsubscribe { margin-top: 10px; font-size: 14px; } .unsubscribe a { color: =

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: #777; text-decoration: none; } =F0=9F=8E=89 Happy Birthday, Ndivhuwo! =

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Let's come together and celebrate this wonderful day! =

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=82 Dear Sandra Stern, =F0=9F=8E=82 We are excited to celebrate Nd=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ivhuwo Machiba's birthday today! =F0=9F=8E=89

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: --b1=_C6MSGDrd21TgPFvDPuto2LaLzWJj65K0zyvcwpKdbm0

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_C6MSGDrd21TgPFvDPuto2LaLzWJj65K0zyvcwpKdbm0";

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: --b2=_C6MSGDrd21TgPFvDPuto2LaLzWJj65K0zyvcwpKdbm0

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: <html lang="en">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: <head>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:     <title>🎉 Celebrate Ndivhuwo's Birthday! 🎉</title>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:     <style>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         body {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-family: 'Helvetica Neue', Arial, sans-serif;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(180deg, #fceabb 0%, #f8b500 100%);

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             line-height: 1.6;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .container {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             max-width: 600px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 40px auto;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .card {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffffff;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 15px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             overflow: hidden;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .header {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff512f, #dd2476);

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 40px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 15px 15px 0 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .header h1 {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 34px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .header p {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .content {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .photo {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             width: 150px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             height: 150px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 50%;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border: 5px solid #ff512f;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             overflow: hidden;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px auto;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 10px 20px rgba(255, 81, 47, 0.4);

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .photo img {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             height: 100%;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             object-fit: cover;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .member-name {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 26px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff512f;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 15px 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-details {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffe5d9;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 25px 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-date {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #dd2476;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-age {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             background: #ff512f;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 8px 16px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 30px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .countdown {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 12px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffdfba;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 25px 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             text-align: left;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions h3 {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff512f;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin-bottom: 10px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions ul {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             list-style: none;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 10px 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: 1px solid #ffb199;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #555;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li:last-child {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: none;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .action-button {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff512f, #dd2476);

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 14px 35px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 30px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 8px 20px rgba(221, 36, 118, 0.4);

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .footer {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             background: #f9f9f9;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #777;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             border-top: 1px solid #ffb199;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .church-name {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #dd2476;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .unsubscribe {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 14px;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         .unsubscribe a {

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             color: #777;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:     </style>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: </head>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:     <div class="container" style="text-align: center !important; margin: 0 auto !important;">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         <div class="card">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             <div class="header">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <h1>🎉 Happy Birthday, Ndivhuwo! 🎉</h1>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Let's come together and celebrate this wonderful day! 🎂</p>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             <div class="content">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Dear Sandra Stern,</p>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 We are excited to celebrate Ndivhuwo Machiba's birthday today! 🎉</p>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="photo"><img src="http://localhost/campaign/church/uploads/685dc5657df2f.<img src="cid:birthday_image_e64e514cb38cc9e42d6a93de520a9375" alt="Ndivhuwo Machiba" style="width: 100%; height: 100%; object-fit: cover;"></div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="member-name">Ndivhuwo</div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-details">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="birthday-date">🎈 Birthday: Saturday, July 19, 2025</div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="birthday-age">🎂 Turning 40 years</div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="countdown">🎉 Celebration is today!</div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="suggestions">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                     <h3>🎁 Ways to Celebrate Ndivhuwo:</h3>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                     <ul>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                         <li>💌 Send a heartfelt birthday message</li>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                         <li>🙏 Pray for their growth and blessings</li>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                         <li>📖 Share an inspiring scripture</li>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                         <li>🎁 Consider a meaningful gift</li>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                     </ul>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <a href="mailto:<EMAIL>?subject=Happy%20Birthday%20Ndivhuwo!&body=Dear%20Ndivhuwo,%0D%0A%0D%0AWishing%20you%20a%20wonderful%20birthday! 🎂%0D%0A%0D%0A[Add%20your%20personal%20message]%0D%0A%0D%0ABlessings,%0D%0ASandra Stern" class="action-button">🎉 Send Birthday Wishes</a>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             <div class="footer">

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:                 <p>With love from <span class="church-name">Freedom Assembly Church 💒</span></p>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: <img src="http://localhost/campaign/church/track.php?id=email_687b381618fb83.98518453&mid=30" alt="" width="1" height="1" style="display:none;width:1px;height:1px;"></body>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: --b2=_C6MSGDrd21TgPFvDPuto2LaLzWJj65K0zyvcwpKdbm0

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/png; name=

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_e64e514cb38cc9e42d6a93de520a9375>

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: iVBORw0KGgoAAAANSUhEUgAAAHcAAAA/CAYAAADaMleLAAAgAElEQVR4nO19CZhVxdF29XbOufvM

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: MAMCCqLiggQXiIiguGGUiIlGMO5EI4oLGtRPY/RjjEs0RHEJGhA3oqKDYlCCUWJQICpK3ACjiIDs

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: yyx3Pfvp/p/qey+iH26BqCR/Pc/xcmfmntt9qrvqrbeqWgb/Xz4lSim66667mq5hpAcNG9ZhwUsv

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ZXfUJ0S+A2P4TkjjI3elH5v8VH/H9Q7klB1gF4uHEICXLzj+xNMbGxvljjin/1rl4g4ddv1FcWbT

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 9LLlq/quXLfhjGwhfyiltDYVT7DIc0udO3Qc+c6f//LId2C4/5LwHXDM2yyXNDamdz/04J8Ug3Cw

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: pGo/4LxLICOjfaedSDFfAM/zgBP61sBBh895589/2WHnSb8DY/jGpKmpydh30BH9n3xhxrMtrn1H

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: q1v6Sch59zbHNakVI0GkgFIOKghVp4YO8wZ02m3NjjxfbZaVUvqVEKK+9RH9G2R4Y6P1yqtzv+94

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: pWOy+cIplBvdqcEBuICi60DXrl1x7rB62QpIGhZEhZJ7aP8BR02/555XduR5a7P8ozOH7S6FmQaA

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: N7/9IW1fOeqss9rN/NsLwzc2bxidydS1Z1aCRZGCMASgSkJNTR0EQQT5fB6EaUDgB2AZorlD+/Zv

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 7+hz12a55JNdFr7/wfWnXnppr29/SNtP+h533M4L3n3z1xvbWm+q7dSpkxSCF12PMGEAKAKcC7f7

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 7t1ztm1DNpuFKIogHrdAymgFdOwY7Ojz18oteCUjkPLwv/z1+SmHnnRS/29/WNsm6Ga6Dxiw2/IN

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: G6dQZvy8pq7WROUhUEokEtV7tx100EF/Wbx4MUXl1mZqgBEKhUJBCmHOnzBiRLiDTn+z0MbGRpor

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: lohIxKgPcu/VG9ZeMnTo0B2a3PjBqafu3OYUb4+IOiSiYLiuC8l4AmKGqRXs+z507bbrpoULF3Zz

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: XTchpQT8G3wlUkWB6yz7T8AftHHMGEU5Z2iuRDJJWx37pFdWrLi/749+1KMKtHYkubCxMfnmB+/d

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Ekg4nsXjlKIJBoAwDDVoQqVJpRZ5rre8tbW1p2VZVDAODIi+lJQbM+nkoh1t3lsTCoQo23dUwSmB

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: pxTweFzYYXDGkmUfjTv67LPrvntD/nwZ2tTE5rwy94dOKAdLQZkTBRARAGHG9GfQ/HLO7Z07d3ps

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 7fq1dbFkguFuRYlCHwwhQIbBGkaNld/F+X1d0T43U5ORTJiKcQPytgPEshhY5tFv/nPx07secciP

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: RowYIb7rE0Er8+rYW85ZuX79eCuTrOGxOISEgGQEfweRH4FlWaqurm5+c0trf0MYBxSLRVS2NtXx

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: eBzy2RzUpFJv3ztmzOrvwJS2WbRy8wVHUcaUJABEcCi5DoRAaNH3B2zM5sf99f3FgxobG7/TbNYP

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: fzasgxtG5zPTamf7AZR8FwKQAIRqFGwYBgRBUCrZpQWu5w4KleRWLKZ9rSUMUGEEBqOKKLX48MMP

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: j74DU9pm0cqNUSE5YwpXuB+EYFgxIEKAiMUIi8W62b5/34MvzLxq5E2NvZqamr5zYAt37Ucr1g+I

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: QHb3VQSKUaCMgzAsCGSEIAl9aRQzzUkyijKMMYo7FhWLSsedi6++5ztdunZ99T+FzKGV/0QyCENU

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: biwWAwkKgiiEEBTuYGgp5ju5IK97/Iknm258eNIZp40cWfvtD/0TuX7cuNrmbPY0KYwkMw0Qhglo

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: hVBpVAEwwoAT9gLj5tN+EPSjgvMgDME0TYiiEBLJGBQLOahNpzf23Kfb8u/KvLZVtHINi4eM0RDN

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: F2MMEGOEUsf5+orX1IAdhiZLJfdasuLj+178xxsPDD7vvD0xjPq2ETV+/4y/vdArkPJoJjjFhYmL

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: FHerjCLglAFVyhGcTiZRcLCM5J74e7y4YBApCRFaKy6UkmoJQMr+NuezPaW8cwl4nHEXV3kUlmN3

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: NFsYOiggICmDguuCK6VG0z4lx89949WnJ780+9GDfnz8UaeOGFH/bU3g+okTY2vXbRopYlYy1BZH

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: aR9LKQWDMCARKi94RyqVLRSLF3LOTQyL8PeoWJQgCHCuUSqZfLMTgP9tzWV7i1Zu4ISlKAiLgnOk

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 3jA6AsYJRKC0eSu5LsTSGdQ4UNPCXcytTKbHxlL+p4uXL39y5muvPPy9Y47ojaDrm97J06ZM2c0O

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: vD4RBYLKYqK8KJFtokSBi+ifwNMykMeFYdiVUkooq+zYyiLAy+QilGH4YWNj4w7PTFVFK9fKmCXH

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: KeU5L09U71ilNGODZs6IWRCEEbiBD3nXAWIYYEcRSMYBYlaGJq1jP25uefrhl//6bLdD+10yZPjw

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: vRcvXmx8ExMoRX5PYvB6hVOhXLsVHfqEIXBFFJVqrmFYs6Mo7GtZ1mbkjK9VQbRcKpVswWjzNzHm

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: b0q0co888JCsjKKspt8wNpTVVV1WsmvbwAwThBUDKxEHNwpAcgoBVcBiJrS5JaossUshcI9tLRVu

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: f37u35oG/3z4tfsfe+yu/855NM6ezcMw6M6FiBPO9MLUTBQ6EymBgvJjhvEAA6illHZHpVYXLf6t

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 9s2E6M8opVoNwXbo/O1nRceuPzj4YOf3Tzy6CTlXzHGGMgJKGJjCAC8MwIzFIAx9oExA4DtgWTGQ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: CFgAwAtCyNTUgYsMlxNCMh5nSUP0bHPtvV0ph+xz5OHjd8nUvvaTY49dNmLECGd7hhmrnnkmHQI9

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: zA18JokCxbj2n8gjQxgB8eVaw2DvRyAHMM4z6GZQqY7jQDxRjnFNgahRQeh6hZ277L5hIczeXsPD

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 4gDW0NAQu+GeezKE0gQJw4SkVNAwNEKNdQiVCOMAImEYSgaBL5Xy27Vv710+cqTthaEHsZibchyv

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: d+/eESHka9Vyaf/YqBSd1K/vdRvsYiNLJMANAkina6BUsEEIBoqjEj3gFP0ZgGBMk++IRLVIBYwR

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: IJVdgaYRfyalVEKRgPvB0jg13ujSZefHexx77IsTzz9/u6TTBgwd2nvh0g9fAEvUhehHKz8XCkD6

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: AXAJT5o1NReXctk5IPieOhLQupQ6RKKEAJURoP/wC6U/X3/r2J+MGjzY25YxIeY46/rrG954bd6A

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ku31K9qlXRhlHRljdYSQGqWUKaUUhBC0mogTcLFLQkjAGAkIIU4gIzuKwnwoZdEAkqNh2FKXrm2L

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: x6wP99hzz1X779Pz/ZWUbsTM1Rdtls3gZ+BPTz50wZIPXgyEIYRlgeMHAJ4sMzvKAysRA0aIRtGt

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 6zdAPJna7Jvxwh1R9dFVk4fvcQGkseKhLaeYkq0dGxp+n7Lifzp1+PAPRg8b5mzLg+x22GHnbsy1

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 3cdTSRIxArIyTeX5AH5oJ5k5lHGezIbuFGJwWvXHqFydKNBKluDl87I+U3Plir/PH/d1LQsqc/Dg

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: wSk3Ht/FjmDvlWtWHqpM84RssdCJmkIwxkgURQS/e8vnVUligMSQjHPtGiIZ6GfHNOBToKJIqUiC

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ySiGayoIgkhKKTnnhbhptiWTyYXpeOIDTvl7CUNsgkC67TKZnO849hmnnZbbrNwRjY3xyU8+/nZo

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: mN1FIq5jXQoMosAHwpDlCCGZTEL33ffQie1sSyu0tLQgXwtU8DKIUWWfrX2fjPTPMOMSOQ6kYnGI

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: XB8szkLfcZcJoqbutftet7w0dWrxX1EsPtQO3+/zO1tFo6llQUi1sdAPKijZwBRMSxnWSM/1bo8Y

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: PU0JRnBsaJpVRX8cFHCcqO+3DjjggMOeve/hxV9LqaNGGWv++c8D1re2XsxMYz8vCrv5URhn3CAY

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: M2jcIrhWIDBa/Zy2GlAua9Lvqz8HIj+9YYAAYWUcgS4kZlqI6sGvMGqcc5XP5xUTNIhbViA4d4WU

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: OQtIwc3mN20ukJs4ZozToX2Hl0PflYHv6XAoioLyTRiD0A8gdDx4b+Ei2H3XbnoQqFi8qoOpDhQD

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ZxRZ+Te3TGgrFQAMDoUw4AGlexb84Jr3li555cBjjhnyr4RPU6dOxaimCy4k/V1oNRAQooIZazZN

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Y1JNMpmASHZjhBJW3dXomzU5I0GiC6NECcHnW3U7feVkwYQJE0TPow/vs+itBU8vWrlsdonK0wsQ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 9nQgSpCYQXCeyJQhyCM4PnRtaFlomRSKcCHiayXU1G6sMgd8hhjGocXD9+j+RMyCRCYNlDP9GQzl

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: HNeFvGuTWG2GkphpFpWfLIZOfXMpv3vBc3pxYapPqh8JUSyK/rZTQ/scmoJqDFhybJBBCOlEUgMP

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Rim88/bbcMABB2R79er1UWtLSykMQ4V/v6WSq2Y5xAsJhWQSSjLS6FqaHGjCIi5RPddkN43b58jD

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: r76ssfHrImsjDIOYVmjFlDFKQEYh7pSFIlRvqojWM847cYx5tQcqm0OF5hAVXL5PKCifN7R37y+1

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: IBMmTIgPGT78+zc/9ODt67O5h5rt0g/MuhpLxSwIOQOFNViIvimBUuCDrxR4UQQBAjZZvtC6oOuq

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Xvicqrlmg5vAqdBJjAgxA6GQiMUh35YDXP+UG8CFCbXt66G+Ywe9gIquDU7og6QEnMCXwjBXxWLx

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Cfvtt+/ln8r0SMJne8XcO4LQwzGbYsawnihE0l1TeTgQ5GtRkX+dNSuTzqQ3fK9nr8c+WL70JM5Y

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: O61UqAAsQoGw8oZ07RJYySQESoJCBkxFYAoBhAvi+P7ubmvLjU0zpg/Z57DDzj7lyCM/+ioV/gXT

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 5GEQGjwegwhDG1Fe/Wj6mYLXNr4xfxPvd2g/RulOqMyqKrWNwB2rtC3ESRcNLhYNGzbsczNBaFn2

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: HDhw7xvuu++itlJhOI+ZcSOZJDT0dZICmTF0SUIIrSgMMBUluuAOKoUCn/haCgQtWwUgkOpFKtau

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: HIYqjVdwwQLxe/f5vh9LxOP4RDds2ABrV6+Eom3r+xu4qPBzQSSpF7518slDh99/zZjFywhRn6pb

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: /u2FF27ikryXNGPasuZybcANsdm84EMUhqH/nUyliOt6eyxbsayLQch4Qdk7DEggwwhw51MNWBCN

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: KuBWTK/aWCKuFcxMC0qBB5hMB8sElkzQrOcetKmQe3zavDmjJzQ1Zb5MuSGlkRA8QvehINIuBMp8

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ssykMq/C0KHED9yBWGyOu1QrAD7BSsg900gBi+Sms088+XMrL8647LKOPQcfe+aGYn6yLegIUZNO

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: RJyTousBZwaEodRRAroDVbkwHMN54uVFZaqzWumBz4UqCuiU8TmRclQBruu4nuetMyxzYZcuXV/v

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: se/3Xt1r7x4fduu2u+OXXP7R+x+Stxb8A1YuXwGO50HMNBG/APECxZwgWwfGI3323HfkGdcMfB8q

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: oPBTyh06dKisz6T+2rx+Ha5+MA0BVWID410NChgF23OrTA8Lw/AYqeBCIORmpdRjKgi9CNEqxpmR

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: BAgizfGGtg3VxIQfBmAlU9r3eCpC3wHJdu2YFOLANS0tN9x4222XT548OfF5Dxyl1nFCw7BsBBra

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 1EaykpNl6+o71rzVu7bWBEpPiJSkEUETWEbyaBQR0CCQYpEGVC9CLrdia7sVOfNnX5x1/wdrVj8Q

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: xkSfogxEgMl/w9AmEdOJCOBQYRge4iNFAIk71Khw8/iAGbp2Vb5Q+/hc0NXh5fv+RkLoczXpmqsa

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: GtpfbhrGtDXr1wfvLlrYe9GiRXsu+WhpzbJly2LFYoFqnwxlq4dgUNoeMNfP1/P4r08/6ZQLX3li

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 2htHkCM206efMssYBpxyyYh3l69e3SYI1EZAwQ08zJho1IZjQ8WgOdBgQGoFkyAI2glGzzPN5Fiu

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: YL3v+eeSULbjnJMIJARBCJYZA64o+KEEysuhElZ+IBkiEgnIew4gnwCMmoFjX3LzIw87J1500cSn

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: x49v2Zpy33vvvTAMghZM2yE1alkm2MWSisdi81JmTc7N579nl0r1VialF1DIoByuISnDOGBanvrB

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: +p5779v0WTeAgGmf/of3afbzl4YmG2TELBZhWIrWS5vRMqLVr1El5OMcOKuwXti1EFVZMgokiEBQ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: VC0JozBypIwKgvIV8VRiDhPmOwSiyPa805ubN/WKomgXxhgzLUvDUj8IgFsG5At5SCaSEOcC3EIJ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: OKVS+tHCvbt1Hzflzjuf6Natm/vZZ/R/2kmOPHvEyq6dOz1u53Jh4Dk6v6v9FH4VLUPzMigpw/dQ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: SSxRIaWSfVQpm7s/bsWLdZmaKzmjr/ieF6Gp5PjHfgilfAFMyvXqdorFsp+hBIIoAlxuUjAQiRiJ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: DFazoS3b+PbixfeOb2pKbk25qJDdunVdYheKilXQfGC7eUHYszeeeaazYtmygbFkIo6+kJoCOCbj

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: fRfiVgxU4IN0fTsdi//2hEMOeXXL+2LLyVNz/n7m+mLLMy6Rw0gsxpHQrAIgnePGpEPlPQouMNyR

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: nutCgOSOBDAxhyz1boDI8QISyRcSpjWmvrbuonZ19dekksl5VJE+ge+NzReLD/qe92NCyG5cCMF4

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: OSZH96c4hYLnQKpdBlzf0fiFSxm4udzzA77f9+zXnnnm4a0pdqvKPb9Pn4ADf1QosjpuCAg8t4x8

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: K6GNqihk8wXleI6UEUFnP/BHEUpziVjipyYXf5Z+gHQMMKWgXSajTQWaJcu0tI9E/6StBhIMjAGW

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: yGCKUXJurG1r/fGEx/7489ubmmJbG3yH9g3vCk5DjqFF6EMmlVofM+OvXz1pUlyGsmfRdRgCnmoG

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: CL/Ptx2wCIcENz8YfurpD4waNWozI9U4YUL8ylt+e+ncBfN/Y2TS7cCySKD9Kfpn0H5S+8rKYkfQ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: hA8frRnjXNdEJwwLCC7Wkh2B7TazIHymobZmcDKRuChwo5dz2dyB2ZbWG3PZ7OWFYvFox7Z3Cfww

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: iTfFEKeaaq0uIPwuYjHwy8sfDMqUSdm039962/kzHnjgna09l89VLsox++//Zl0mNU36oYphCk2V

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: QQOtfKDKrlR3HvpgLgSuYOL6XkOhVLw1UmqQaVk3cc4fZwpshPZ2rgAcTZkfgu96yEPrwLyadtNx

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: XRhoWI8+jVmGWLz0w1/efufvTsPCgM+OM24mlpqMt2FaD3dk4Idv9u7f/2NZjDJu6O+m0SqjGuEj

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: u1aTTEHKjIGbL/oNqfS0xlGj8psVO25czR9+f88VLpVjzFSifc5xiBMEgN0JRBLtT7cERfh+y+eA

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 32EXipHvullO2NxEPHFbOpH4WW0y2RhE0a6e447xI++ZIAgui5TchVLKkb3S5hwVyj7JxqFiyxtK

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ldOvWFVSstEKOEGh8OJhffvccM6Pf7zqixT7ucodN26cc0CvA+8WBDbgTemWE1LlUAevKo2Gg8Od

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 4csKIwNqj6JTGu8F3klJxkYZQlwdF2Y2bpgK0XTMsqA2mYbWjc0aeHE0P5UUHIZfGKxjkR41BFjJ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: REPetW96Z9OaPT47zpqMt9EwjLmmMJRTssPdd97lsaljxgSt+Q3tiZK7Ec4IhiGpRByEUlBszUJY

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: cqKkYT15YO/e929W7F13pf/Y9HhjZNCrHCnjkSHAiMeAM6HjzfJGJRoQ4fwRZRNEyWEZMCLhT6Vc

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: wTm/wkqZfRPJ5E8NIWYGUg4ret6ztuff7Uby9AhIOyoMgnNEYgfnh34caDXFiopVm1k+JDIQrjEv

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: VMzxcj26drvkwYfGn/zU+Pu+EpP2uS2cgtLmpBWfR4FI3G3oN7Xv3EKxm/ljVC5WQUi0XQQpIhKC

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: irkyGm4Lej5IOd2KWSMLudzLoJTv2Q44tg0GKyNNVLjOIyuokA2fsFz4VBWjHea+Pn/sKRdeuM+W

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Y2wP7W3LNBcIykKqoNihY8eluNRrUqm0GTNTuA6RBi20ZSHGDUgKC3zPeX9Av35jHho7dj3e439u

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: vTV1z6SJozcV8z/zKIk7WOnMMcFAwPdDMAyrPKeKoEvBiyIwjFTRiODtGGHXJ2LmsEQ8OSXO4ntF

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: YXBNvlR6OG+Xzih4TmcQzJKcUowOsIDPCXzwKhcSECho4nX16RbWQKPjIFQyV1jXMZa646ZzL5gy

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: rM+g3FdR7Bcqd+o99xSplLdJL1ixOaxBknvLeK2ym3WKEPOpnG32FRzpN8Hbu0Fwve05//OLa897

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: tuseewy17dLdBKRHZAQ1mQwErqcXi84woQ/GMEoqsLDITcqyTxYMgczxf531wh+vGDt2c4iEoCpS

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 8JaSMmcKA3eOPr8im8+3LxVLFj6wdDoNNekMuCVblXLZ5Uf2P+ziqffcsxTK4Q57df4rp0SCXs7T

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 8ZQNEaQa6qDkOZp4wNShXSyVwVRlIauyYm0B9NkU4/1imcyAVKb2D8RRHQotG+c0t2yYWvBKI8Ek

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: XVjaIjyTADv0tVVD/4/uprpr8T2QT5RadU9QwTESFesGH40cesZxq/7+xo1Dhgz5WvVdX9h8PeSU

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 09+qTSWnRwHWzkWYPdicKkMF6xWmQ6QymtQIusKhVuqTCOOcF3132FNTZp25n2Xljzl04E0mYfcZ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: QF2nkIeYZeh7VbnhzYDC87XiU8mMDpkiQqhIJfZ/Z/78g7ccY9ISy4J8Pmtxvn7ooEEaNWZqa+MY

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: oiVMAwotLaA8D9y2bFtDuuYX1/7853/fPL9zzz7o9YWLrvYoSeZ8bKeJQTZf1OgXF63vOYCgkmmy

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: QwELJVZ2bBBArzGkef6Gzp3/yUxT+S25q6IoeEAIszsXpol5MTeMSIA0Lsa8VkyTQVAJJXHHYnUp

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: WiedAapwyvrZYvwd4oWVJLJgKTW+azr9ISHka5f/fKFy7x41yuu37/63W0IstBLW5hIcgwkdx0U4

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: eKkAY0DkVKuX3Hxzqak+nkk0vLbwrbtf+HjZo/v06FEzZPDxV3apbz88yOUXqMAPMNWFRD7WP+Hk

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: VSghJpCBMUAFEUQhASIs8AxOF65ecWnjvfe2r47x+vMuXrZrfafnVd5Z2qFnT51CLDqlGp3e8wKw

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: pJKqZM8+4Zhjj1y1YMH0Pn36aHg+6JRTjpn7+oLp8YZ2uwUYKiUS4AdlUkIQBr5tQxLDQLQcRcev

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: IWJmO9O6oF+P3gftn0qMT2REx5plKyZ5zS0fuFReEQnWoDgniPgVpSCYAE4MYCHFUAgSwtQWCUM2

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: zg3dwY99wrghMPkSBL6OJJCwZJ4b1Qlj5olHH3VM61vv3Dl69Oh/KTX6pccm/OCCCzYkDPFktqXV

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: re6uYiVGxf2LA93yVpsRdRV84XvBESjxUIbHP/b4Yxf26tGDvD1zZtPoyy7/mXT9vwkCMs65Cn1P

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: m0LkaLP5PFhmHDw30CQIUpYBo6TFzh8x7bmnj6uODznh/fbc48GEYPOP22MPrThLmDEZBTTIF3zq

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: erOOOfSIC564++7NYcNxl1xivrfkw595APWYCtS4SJZTbLggEER2qG2HqFqGttsSF9b9h/bpf945

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Rx89CWKQ+yhUQ9ev3/gQs4yz7NDfOaSUBoTqBAkudLyT2uLR4oLBNClKKpXSJhfNPs5TL8ZCHjKJ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: OISeq0qtraFFyAfHH3v0FX/8ze/mwzZUrnxp98CMiRPlD44/4a3mXAsTnPcu2SUjnkppc4ylLZjn

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: RQxUnQ5TW1BtFVrcc8pVHJZlcdf3+7355lv7rGlre/W3o0YtveXu8bPn/e0lo5DN7Z5MpuKOa2tT

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Fk8mQKL/VpH236VSCeKIpAkxshtbeoz+xeWPznvxRW2G3/7HPzYo33//4IMP1qYrVlfX03OddMK0

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: xtXWmr+eM3X65rBh8vPPJ56YPPkOj6pTsIXeQ5bNNLXZNRQFGkR6l9m5wrq4ZU1NJ8SF62PxyW6h

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Vc19990ha9avvyFvly6z0onOIUgSVlOIGGvT6qv65CKYjGdgIKunInB9V783TUNHCFgzbXEBFmIO

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: L2j9/n77/fKMYade85vLrtjm4vivdOAJgqv99/re7eD5f4qZlsR6KgRNtl0sk+KyzJ/yLTnULSRh

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: WnoC+UIJXBmSDdmWHz721FOnNTY1GaOHDVtz6YgLrqkVsclRyY0wzMAcsu06UPRsDTxc3aiV0P5Q

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: cBOR5c7F0Nuv+g1YW7QlGdGrR4/pXes7nL7mjX+M/2D2gk/Rlzf/7pY+G+38aS5TVsjKmCGyPRCh

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: AiNUkBaWCkrO6r69+5zzyIQJl64+6rhFHQzDbG1rvXLTpk33gmDH0ZhhlTxXJwXQlUAF6UIl6f6p

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: kLHiZzGSwEWgQRMueNuBwHGhNp6AOKGqec2afMf6dndccMKP773q3HPXbqti4eueQ/W9QYMGrm7b

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 9EjOs3c2kimIJZK6LVLXCKsyg1MVHTURqJD0WOzggZGMa8gfBR4EJcdrH0vddP5ZZz1w7ciRa+6e

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: MqXTDbfcepMdBceLTLI+Mlg5T6kImFSA57jlqo8oAkvJ0AzldateX3Dr1ymLuWLsFYkHnph1Z1vo

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: nRPLpAj6PMxcWUDBy5egNpEqha43+4iBR9w15c7bZukO/aOOOmBDIXdyKfAvS6RTMbQgVjymTarr

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: e5tjfaiSO9Uqiy1GhSATSQ6kcnXmKoz0gpdeAEGpJAWoFft/r8eNv772+icH7L134evo5IvkazV1

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 3XDVVWvnv7f444iQfslkItWWzRKkETFtRcrNvrA5c1pRrv6BlGDoSkpfh03YyxP5Pncdt9/iRYvr

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: Ro8477mrLrkkd92v73pp3fpV7zW3NA/0Aj8REkXwbzH9iiE0mjVdwuP7JCaM4l577f38Yw8+uFVe

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 9bOCDNfkZ58/y6bRxejMMeZEUCgIBREoSBuxdTvV1v/PPbf99pYrz/0Zdvrxky699MA169Y+pIQY

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: IgW1ENUizYjhGc4D8Ue1XLY6d81kKag8j/Irfoay8o7VgEr7dk+5+YKXNMWMvbrscvXLTdOe7drQ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: sE3FeZ+Vr3UO1fnnnx/86szhf6oxzd9sXLrcrk+ngERlWh15T+2D8cJQqNJnhIpG0h7hvylMXQOE

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: qS4jFgeRjFlZzzn5kRdnn3fLhAmZq88flps2/pk/xxT8Lk7ppgQmv4NyHIymz4zHIMCwgXFS8oO9

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 7/j973f6qmNfvGZjr035wq/cUNYRo8yoGQlqgqgAAAvdSURBVJRDnIrIK9rvZJKp6/5w45jJR/ft

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 2/LCu+/Gzx0z5vJVK1c9rJTqGQSBERcWCGC6qSwZT4IKJKTjKfBsr0JPli+qyBYp+PJuRqCIqFui

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 9VIKTEJUUCx81KWh/a9Hjxj5s79P//Pcr1u2+lXka7djzpgxQ47+1f8u/nDpkjCfy+5HGY1T7XMq

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: F60kFyo7F6cY4W4VWNjlA6MMglD35kCEq1wws1QsDtywYVNp7ZIPXrnzzuvDGa+99ua6j1b8Y9Wa

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: NQcSCQ1o+BhyxBgmaQKMQeh58Q7pzEsr33//gy8b812PPJK+54H7xiba1x0SMU3gYk0pmJIou7Xt

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 9ctHXvbzi8bfMat/587hMWeemXj4iSnX5kqlq/0o3MlKJAm2g0a6FIZpK4RECxYLIi7AnQvqExtc

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: bZ6rXmWyR4GFLaVAwc7nsrVW/MHDBxx8yaiTT3nu/LPP/rc1nv3LfT1jx45NjH304Qtdom7k8biB

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: LEyEhV7IwvByzyuucLfCTbNKNYKu1NC2qpJZQnYqDMGIYMmAA3sP/tPEiR9B5WzGbgP6jmjOFe4Q

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: qYwZYXGd52gAg6U0ZqRk7927X/33qVPHftlYd+vfv08h8ppsGXSTFeKAYcFw0X13n932GD3/mWd0

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: JfrQoUONl1auvM6OvFGUsbSnD0pJgeeWqymEpiKlzrFieIeMHFZF6DEpJGDKjd5+pfpCKx4XheNC

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 0jCiXHPr8s47NUw49MgfTHqosfHffhrsv3w84JVXXlm69pwRd8UpvynbvHE10oUWUmoVgIEPEPO3

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: GONVRfPRmJ/TZTusnB/Faoa4hVVqu788f/4TR5955g+ggoBHn3LGY52SNfdJ183b+TwkYpa2AAjK

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: mCHo8lWr9vyyceIiISb7Ud61O+HRCBjmUCeQMclm7t1l11Nemz79Zfy7u5qaGuasWTXeVcHlThSk

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: AwoQT6d0KRCGYrFkCnJ2EXxZ5tBRsRiLG5ZZLnBTUvtTBJdYecIpAd9xMbGQSwr+QoqJK04a8qOj

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: n7j97ju/CcXC9ji19a6ZM83npk8dMmfB/EkRFxnNl1Y6EVSg9FkTSCXKT5mrSrqwwtXihVUYGBrE

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: GFtw89XXDrlo2DBN7N9++6S6cVMm3O1Z7NR8GBFsRgPGIMkMECX/L5eeeMIPv6ig7vIbb+x6+8P3

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: v5GoyzQg4+UWS0ra7sJfXXnV2b8aMUKfEodga9ILf7myEAbX+1SZmE9GRGzni5o2dMNyWh7dAS4u

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: UglxsHgQARQWD8ZNU79iiIP0pe85PgX+Tm0iMXHgUUc1PTJmTOGb7tjf5oM9sf1i5h8eeNoC+kuL

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: kEUxwrygUISMGdflLDplWOGiUaJq96CsFNExVuZyhaHLXnk83uPWceN+umDBAk3fjB7989afnnji

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: PYVNzZuwQyARj5ePQVB64TR80RzwHjNmzTorXVtTjzuqlMurJDM+GHjQwedfc955mrHCyoum2bPP

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: bm5tGc0EN6udAYVcvgII0ckT4DEBShBwijlQtFzTXe5aQM45UjyKQub6LTEF7xme9/ROiczp55xw

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: wtGr5sx54NHGxvy3cRTDdjm1FRuXb57w0KR+++x7SkLR2xqsZMFty+ocqoF+NSqXs6IyuQ7iQSf/

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: qyEEFl5jvIwNZ8BZLOuWLrvmjjsOr95/cL9+8zvUZBqjYqkFC8NS8QSUCkUo2bYJXbt+7kEsD8+Y

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 0aHg2oNUGGEtD8Q5b96prt0NMx95ZH71Yf9x1qzua1o2/TJTV9uAY0BMgH+LKUKMr7F2GJkkRLxI

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 6mfq6jTvDBirF/LgtbX67RPxebTo/O+u7evP6Nau/uTxt407a9mcOU/dXVbqt3YQ93ZvlJ49ezb/

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: xS03Dft47fqzuWn2VpzVISuFfhILx6ptFTprVAlx0CzrszhkCMVsDhKUQX0y/eaJxw4+/bZrrnkf

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: /x77fc+85sqL3vpw6S2punYGxpLcDRf98vQrDxo9eus9R4cNG3bY0pUrnwRBGvySvX6vXXe9buIN

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: N0/ed999fSQorvvtb/d8dPq0u/KeN8iRIUkm0+B4LjAqyqnGcvpNKgqhEMwDKb3Q89pkJNepIFjX

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: uaHDG/vs3X1u9z33XTjuXyT3/52y3Y8fOuKII/DglMev+c1vZk2bNeuQZjt3MaX0UF9FBgl9IiQD

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: xoQO+aNquMSo7oXBmBl7igxCoLmQ6/XIjGkX3nXXXVcitYgKGdHYOOHj9RuGRVL19fDEO+AB5+s/

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: d2e0FYt9/DCoFYoW2tWkr68qFn938x137PTUn2eMb85nBxLDJJh082xHeY4jiaKY7Mru3LnLeqeQ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: ezVG6eJONbXLWnK5Urq+LvuLyy/flKyryx7fu/d2bUnd3vJvOVuqYoo2AcD0S+666y9PNj16tnSD

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: EdIP9gXGTCKAYKU8Nm9FlTgYfR0lvAxQGMP2E+5LOWTBunXXYxsw3ndiY6PduX/fJ1py+QNMYZjJ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: mNnc2tq61XbQEY0j4rNeev8MyxC8lG175aGJk6ZVFYvy0rx5Ry35cEnv2p3at0agVlsKVqcsa0l9

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: PLnEMoylQ08+eTFs2JBtHDMGg3K1JYt/6vPP/3u1sp3kGzm/Ak1108svd5n/6qt9bN870vXsg1qL

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: hQ6EixpqCCMiiklUbaWVERGpznf6Qdi5rv7cRc8++8fqDul7/PHdl61aMZEADKxLZn7/z3nzLt3a

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 7hl40glHvL9y5XSqYNNO8dQ5P5ozZ25jxf9hU/S9j00+vKWtEE/VpdpMxdddfPHF2VrGcmh5voln

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 8k3IN37MUKWKkfIOHTo88fST38/Zpf1c3zsgUNDVC4POQpgxx3EMLN9FOGNROnfsL2849cyfHLuu

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: eo9Bpw/r//7i92fUJtNnvTtv3rOf/Q5U3tgH77t1+erVZ3Vo1+6sxbPnPr8tedEdVb7xI/8qMSle

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: eP7Emialnu24cmH6/nsfyKxesb62JZ9PJSyrXT6b38l33Q5uoRCLm+Gn6pa7d+/xzqolyx494IAe

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 7707b97/+Y4/zZ6d3rhmbaeaZOqJnRs6vr74v1Cx33lBRLu13l382e8mTKjHAretzeHtt9/u3OuQ

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: vhciwfLf/gz/4wR9/Jc1kv03yDb7XPShPXr0IO8BsI5tbaq2tlZit+B/6v/pZEeSL1Qumr+XVqww

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: X54xo27ua6/VOGGYAA61MW50/2D5R3sKxtt5UVgvwzARoYnUh1IwPJ8h70ZBThCytnPnndd4jvth

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: fW1tq18seBBA8cZf/SpbTKXyg7t3367J6a0Jkh9hGIogCBR0AoC1AIjM2vm+MnbdlcDatdAixObn

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: gAliJLUNzokfhqqaMNaf30L8Dh22eL8KGtwG+dYee4TDCPlax/kuWLAgnkwmWZaxzWMIsUTkM1Ib

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: RQpPMNjyp+u3GPNnBTsst6pcVOroceNqm5qmnOBT6B9FsGfMsjq7QVhr28UYZUIYJme6+D4KdNjC

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: DIZsBDhuSVdFmhaeWRWpSAbSMowwCvzAZGbJ5LQlsL3VnPHlFmeLjjjiqFmFXr2WTv2CzvZtkcHD

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: fjLszUXv/cSMWxKPMJAqUoZhKddxpBsFhDFBDMGp73t4coZ+HliArvlwzvEEGd1FSLD2Flk23btD

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: kTvVZw3p4yZASRXJsEunTvN+MfP5h7+qgmfPnm1dds0vb3b8oHMESmAMFip9pg0WPejuJPweQ1vB

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: SJcCRPoEhEp7YXkESm9S7GbUw6Ggz0Fimg/81CoSdzY17dFl4CEjck7pcNt39zZjCRM7s/C4g1Cf

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: zmJAMp0B2ynpu2LJaYgdCZgJwiMBhAGCG+D4LlDOiRAGiwCYH4WmJJDE1hCPqH1U6CkHRPT4M9M3

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: xZ5/7oWDTj7xKVJy57w2c+Z2y57gIj1gQN+uIcgTCSU8Ukyf+4gneaHJwPMssfjOx6MHRPxTGtEP

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: UVGlsJgcz2bGA9j0MQtY7xPqY44y6TTJZ7MKO9wj34cVHy8zks8992jl418qmzZtCop2qU/J8w8W

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: MUtEWPQgmPLDSHc64vkKOmesVVrujy6f5YGH5ZYPS0Ep/78ZCC45ffSS5ByUDOH/AcRNdcEflr82

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: AAAAAElFTkSuQmCC

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: --b2=_C6MSGDrd21TgPFvDPuto2LaLzWJj65K0zyvcwpKdbm0--

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: --b1=_C6MSGDrd21TgPFvDPuto2LaLzWJj65K0zyvcwpKdbm0--

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:15:52] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:15:53] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbxz2CDfz2SrxG

[2025-07-19 08:15:53] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:15:53] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:15:53] Email sent <NAME_EMAIL>
[2025-07-19 08:15:53] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:15:53] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:15:59] Global sendEmail called for Godwin Bointa <<EMAIL>>
[2025-07-19 08:15:59] Initial email body length: 6422 characters
[2025-07-19 08:15:59] Birthday notification detected via explicit flag
[2025-07-19 08:15:59] Birthday member data extracted: Array
(
    [name] => Ndivhuwo Machiba
    [email] => <EMAIL>
    [image_path] => uploads/685dc5657df2f.png
    [photo_url] => http://localhost/campaign/church/uploads/685dc5657df2f.png
)

[2025-07-19 08:15:59] Email type: Birthday Notification
[2025-07-19 08:15:59] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:15:59] Set Reply-To address: <EMAIL>
[2025-07-19 08:15:59] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:15:59] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:15:59] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:15:59] SYSTEMATIC FIX: Found 2 images referenced in HTML
[2025-07-19 08:15:59] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:15:59] SYSTEMATIC FIX: Referenced image 2: http://localhost/campaign/church/track.php?id=email_687b381fac7d06.76381268&mid=51
[2025-07-19 08:15:59] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:15:59] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:15:59] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_93dc000af2326b8f93dd72f38abc9e9f, mime=image/png
[2025-07-19 08:15:59] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:15:59] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/685dc5657df2f.png as CID: birthday_image_93dc000af2326b8f93dd72f38abc9e9f
[2025-07-19 08:15:59] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:15:59] SYSTEMATIC FIX: WARNING - 1 original URLs still present in HTML!
[2025-07-19 08:15:59] SYSTEMATIC FIX: Remaining URL: http://localhost/campaign/church/track.php?id=email_687b381fac7d06.76381268&mid=51
[2025-07-19 08:15:59] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:15:59] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:15:59]   - Email type: Birthday Notification
[2025-07-19 08:15:59]   - Images found in HTML: 2
[2025-07-19 08:15:59]   - Images successfully embedded: 1
[2025-07-19 08:15:59]   - Processed image URLs: 1
[2025-07-19 08:15:59]   - Processed image paths: 1
[2025-07-19 08:15:59] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:15:59]   - URL: http://localhost/campaign/church/uploads/685dc5657df2f.png -> CID: birthday_image_93dc000af2326b8f93dd72f38abc9e9f
[2025-07-19 08:15:59]   - Embedded images: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:15:59] Set HTML body (6415 chars) and plain text alternative (2602 chars)
[2025-07-19 08:15:59] Attempting to send email to: <EMAIL> with subject: Birthday Celebration! Ndivhuwo
[2025-07-19 08:15:59] Email content validation: Body length=6415, HTML=Yes
[2025-07-19 08:16:00] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:16:00] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:16:00] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:16:00] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:16:00] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:16:00] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:16:00] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:16:00] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:16:01] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:16:01] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:16:01] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:16:01] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:15:59 +0200

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: To: Godwin Bointa <<EMAIL>>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Celebration! Ndivhuwo

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_we1QxwC2foZUiE6SaP2shqyV98tvtWJmEg9EH0UzY"

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: --b1=_we1QxwC2foZUiE6SaP2shqyV98tvtWJmEg9EH0UzY

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Celebrate Ndivhuwo's Birthday! =F0=9F=8E=89 body { margin: 0; =

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: padding: 0; font-family: 'Helvetica Neue', Arial, sans-serif; background: l=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: inear-gradient(180deg, #fceabb 0%, #f8b500 100%); color: #333; line-height:=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  1.6; } .container { max-width: 600px; margin: 40px auto; padding: 20px; } =

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ..card { background: #ffffff; border-radius: 15px; box-shadow: 0 15px 35px r=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: gba(0, 0, 0, 0.15); overflow: hidden; width: 100%; text-align: center; } .h=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: eader { background: linear-gradient(135deg, #ff512f, #dd2476); padding: 40p=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: x; color: #ffffff; border-radius: 15px 15px 0 0; } .header h1 { font-size: =

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 34px; margin: 0; font-weight: bold; } .header p { font-size: 18px; margin-t=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: op: 10px; } .content { padding: 30px; } .photo { width: 150px; height: 150p=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: x; border-radius: 50%; border: 5px solid #ff512f; overflow: hidden; margin:=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  20px auto; box-shadow: 0 10px 20px rgba(255, 81, 47, 0.4); } .photo img { =

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: width: 100%; height: 100%; object-fit: cover; } .member-name { font-size: 2=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 6px; font-weight: bold; color: #ff512f; margin: 15px 0; } .birthday-details=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  { background: #ffe5d9; border-radius: 12px; padding: 20px; margin: 25px 0;=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  display: inline-block; width: 100%; } .birthday-date { font-size: 20px; fo=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: nt-weight: bold; color: #dd2476; } .birthday-age { font-size: 18px; font-we=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ight: bold; background: #ff512f; color: #ffffff; padding: 8px 16px; border-=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: radius: 30px; display: inline-block; margin-top: 10px; } .countdown { font-=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: size: 18px; color: #333; margin-top: 12px; } .suggestions { background: #ff=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: dfba; border-radius: 12px; padding: 20px; margin: 25px 0; text-align: left;=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  } .suggestions h3 { color: #ff512f; font-size: 20px; margin-bottom: 10px; =

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: } .suggestions ul { list-style: none; padding: 0; margin: 0; } .suggestions=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  li { padding: 10px 0; border-bottom: 1px solid #ffb199; font-size: 18px; c=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: olor: #555; } .suggestions li:last-child { border-bottom: none; } .action-b=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: utton { display: inline-block; background: linear-gradient(135deg, #ff512f,=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  #dd2476); color: #ffffff; text-decoration: none; padding: 14px 35px; borde=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: r-radius: 30px; font-size: 18px; font-weight: bold; margin: 20px 0; box-sha=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: dow: 0 8px 20px rgba(221, 36, 118, 0.4); } .footer { background: #f9f9f9; p=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: adding: 20px; text-align: center; font-size: 16px; color: #777; border-top:=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  1px solid #ffb199; } .church-name { color: #dd2476; font-weight: bold; } .=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: unsubscribe { margin-top: 10px; font-size: 14px; } .unsubscribe a { color: =

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: #777; text-decoration: none; } =F0=9F=8E=89 Happy Birthday, Ndivhuwo! =

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Let's come together and celebrate this wonderful day! =

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=82 Dear Godwin Bointa, =F0=9F=8E=82 We are excited to celebrate N=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: divhuwo Machiba's birthday today! =F0=9F=8E=89

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: --b1=_we1QxwC2foZUiE6SaP2shqyV98tvtWJmEg9EH0UzY

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_we1QxwC2foZUiE6SaP2shqyV98tvtWJmEg9EH0UzY";

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: --b2=_we1QxwC2foZUiE6SaP2shqyV98tvtWJmEg9EH0UzY

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: <html lang="en">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: <head>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:     <title>🎉 Celebrate Ndivhuwo's Birthday! 🎉</title>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:     <style>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         body {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-family: 'Helvetica Neue', Arial, sans-serif;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(180deg, #fceabb 0%, #f8b500 100%);

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             line-height: 1.6;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .container {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             max-width: 600px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 40px auto;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .card {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffffff;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 15px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             overflow: hidden;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .header {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff512f, #dd2476);

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 40px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 15px 15px 0 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .header h1 {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 34px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .header p {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .content {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .photo {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             width: 150px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             height: 150px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 50%;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border: 5px solid #ff512f;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             overflow: hidden;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px auto;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 10px 20px rgba(255, 81, 47, 0.4);

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .photo img {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             height: 100%;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             object-fit: cover;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .member-name {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 26px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff512f;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 15px 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-details {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffe5d9;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 25px 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-date {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #dd2476;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-age {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             background: #ff512f;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 8px 16px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 30px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .countdown {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 12px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffdfba;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 25px 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             text-align: left;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions h3 {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff512f;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin-bottom: 10px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions ul {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             list-style: none;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 10px 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: 1px solid #ffb199;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #555;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li:last-child {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: none;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .action-button {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff512f, #dd2476);

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 14px 35px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 30px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 8px 20px rgba(221, 36, 118, 0.4);

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .footer {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             background: #f9f9f9;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #777;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             border-top: 1px solid #ffb199;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .church-name {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #dd2476;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .unsubscribe {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 14px;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         .unsubscribe a {

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             color: #777;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:     </style>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: </head>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:     <div class="container" style="text-align: center !important; margin: 0 auto !important;">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         <div class="card">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             <div class="header">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <h1>🎉 Happy Birthday, Ndivhuwo! 🎉</h1>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Let's come together and celebrate this wonderful day! 🎂</p>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             <div class="content">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Dear Godwin Bointa,</p>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 We are excited to celebrate Ndivhuwo Machiba's birthday today! 🎉</p>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="photo"><img src="http://localhost/campaign/church/uploads/685dc5657df2f.<img src="cid:birthday_image_93dc000af2326b8f93dd72f38abc9e9f" alt="Ndivhuwo Machiba" style="width: 100%; height: 100%; object-fit: cover;"></div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="member-name">Ndivhuwo</div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-details">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="birthday-date">🎈 Birthday: Saturday, July 19, 2025</div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="birthday-age">🎂 Turning 40 years</div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="countdown">🎉 Celebration is today!</div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="suggestions">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                     <h3>🎁 Ways to Celebrate Ndivhuwo:</h3>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                     <ul>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                         <li>💌 Send a heartfelt birthday message</li>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                         <li>🙏 Pray for their growth and blessings</li>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                         <li>📖 Share an inspiring scripture</li>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                         <li>🎁 Consider a meaningful gift</li>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                     </ul>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <a href="mailto:<EMAIL>?subject=Happy%20Birthday%20Ndivhuwo!&body=Dear%20Ndivhuwo,%0D%0A%0D%0AWishing%20you%20a%20wonderful%20birthday! 🎂%0D%0A%0D%0A[Add%20your%20personal%20message]%0D%0A%0D%0ABlessings,%0D%0AGodwin Bointa" class="action-button">🎉 Send Birthday Wishes</a>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             <div class="footer">

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:                 <p>With love from <span class="church-name">Freedom Assembly Church 💒</span></p>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: <img src="http://localhost/campaign/church/track.php?id=email_687b381fac7d06.76381268&mid=51" alt="" width="1" height="1" style="display:none;width:1px;height:1px;"></body>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: --b2=_we1QxwC2foZUiE6SaP2shqyV98tvtWJmEg9EH0UzY

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/png; name=

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_93dc000af2326b8f93dd72f38abc9e9f>

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: iVBORw0KGgoAAAANSUhEUgAAAHcAAAA/CAYAAADaMleLAAAgAElEQVR4nO19CZhVxdF29XbOufvM

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: MAMCCqLiggQXiIiguGGUiIlGMO5EI4oLGtRPY/RjjEs0RHEJGhA3oqKDYlCCUWJQICpK3ACjiIDs

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: yyx3Pfvp/p/qey+iH26BqCR/Pc/xcmfmntt9qrvqrbeqWgb/Xz4lSim66667mq5hpAcNG9ZhwUsv

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ZXfUJ0S+A2P4TkjjI3elH5v8VH/H9Q7klB1gF4uHEICXLzj+xNMbGxvljjin/1rl4g4ddv1FcWbT

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 9LLlq/quXLfhjGwhfyiltDYVT7DIc0udO3Qc+c6f//LId2C4/5LwHXDM2yyXNDamdz/04J8Ug3Cw

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: pGo/4LxLICOjfaedSDFfAM/zgBP61sBBh895589/2WHnSb8DY/jGpKmpydh30BH9n3xhxrMtrn1H

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: q1v6Sch59zbHNakVI0GkgFIOKghVp4YO8wZ02m3NjjxfbZaVUvqVEKK+9RH9G2R4Y6P1yqtzv+94

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: pWOy+cIplBvdqcEBuICi60DXrl1x7rB62QpIGhZEhZJ7aP8BR02/555XduR5a7P8ozOH7S6FmQaA

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: N7/9IW1fOeqss9rN/NsLwzc2bxidydS1Z1aCRZGCMASgSkJNTR0EQQT5fB6EaUDgB2AZorlD+/Zv

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 7+hz12a55JNdFr7/wfWnXnppr29/SNtP+h533M4L3n3z1xvbWm+q7dSpkxSCF12PMGEAKAKcC7f7

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 7t1ztm1DNpuFKIogHrdAymgFdOwY7Ojz18oteCUjkPLwv/z1+SmHnnRS/29/WNsm6Ga6Dxiw2/IN

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: G6dQZvy8pq7WROUhUEokEtV7tx100EF/Wbx4MUXl1mZqgBEKhUJBCmHOnzBiRLiDTn+z0MbGRpor

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: lohIxKgPcu/VG9ZeMnTo0B2a3PjBqafu3OYUb4+IOiSiYLiuC8l4AmKGqRXs+z507bbrpoULF3Zz

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: XTchpQT8G3wlUkWB6yz7T8AftHHMGEU5Z2iuRDJJWx37pFdWrLi/749+1KMKtHYkubCxMfnmB+/d

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Ekg4nsXjlKIJBoAwDDVoQqVJpRZ5rre8tbW1p2VZVDAODIi+lJQbM+nkoh1t3lsTCoQo23dUwSmB

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: pxTweFzYYXDGkmUfjTv67LPrvntD/nwZ2tTE5rwy94dOKAdLQZkTBRARAGHG9GfQ/HLO7Z07d3ps

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 7fq1dbFkguFuRYlCHwwhQIbBGkaNld/F+X1d0T43U5ORTJiKcQPytgPEshhY5tFv/nPx07secciP

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: RowYIb7rE0Er8+rYW85ZuX79eCuTrOGxOISEgGQEfweRH4FlWaqurm5+c0trf0MYBxSLRVS2NtXx

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: eBzy2RzUpFJv3ztmzOrvwJS2WbRy8wVHUcaUJABEcCi5DoRAaNH3B2zM5sf99f3FgxobG7/TbNYP

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: fzasgxtG5zPTamf7AZR8FwKQAIRqFGwYBgRBUCrZpQWu5w4KleRWLKZ9rSUMUGEEBqOKKLX48MMP

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: j74DU9pm0cqNUSE5YwpXuB+EYFgxIEKAiMUIi8W62b5/34MvzLxq5E2NvZqamr5zYAt37Ucr1g+I

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: QHb3VQSKUaCMgzAsCGSEIAl9aRQzzUkyijKMMYo7FhWLSsedi6++5ztdunZ99T+FzKGV/0QyCENU

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: biwWAwkKgiiEEBTuYGgp5ju5IK97/Iknm258eNIZp40cWfvtD/0TuX7cuNrmbPY0KYwkMw0Qhglo

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: hVBpVAEwwoAT9gLj5tN+EPSjgvMgDME0TYiiEBLJGBQLOahNpzf23Kfb8u/KvLZVtHINi4eM0RDN

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: F2MMEGOEUsf5+orX1IAdhiZLJfdasuLj+178xxsPDD7vvD0xjPq2ETV+/4y/vdArkPJoJjjFhYmL

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: FHerjCLglAFVyhGcTiZRcLCM5J74e7y4YBApCRFaKy6UkmoJQMr+NuezPaW8cwl4nHEXV3kUlmN3

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: NFsYOiggICmDguuCK6VG0z4lx89949WnJ780+9GDfnz8UaeOGFH/bU3g+okTY2vXbRopYlYy1BZH

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: aR9LKQWDMCARKi94RyqVLRSLF3LOTQyL8PeoWJQgCHCuUSqZfLMTgP9tzWV7i1Zu4ISlKAiLgnOk

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 3jA6AsYJRKC0eSu5LsTSGdQ4UNPCXcytTKbHxlL+p4uXL39y5muvPPy9Y47ojaDrm97J06ZM2c0O

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: vD4RBYLKYqK8KJFtokSBi+ifwNMykMeFYdiVUkooq+zYyiLAy+QilGH4YWNj4w7PTFVFK9fKmCXH

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: KeU5L09U71ilNGODZs6IWRCEEbiBD3nXAWIYYEcRSMYBYlaGJq1jP25uefrhl//6bLdD+10yZPjw

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: vRcvXmx8ExMoRX5PYvB6hVOhXLsVHfqEIXBFFJVqrmFYs6Mo7GtZ1mbkjK9VQbRcKpVswWjzNzHm

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: b0q0co888JCsjKKspt8wNpTVVV1WsmvbwAwThBUDKxEHNwpAcgoBVcBiJrS5JaossUshcI9tLRVu

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: f37u35oG/3z4tfsfe+yu/855NM6ezcMw6M6FiBPO9MLUTBQ6EymBgvJjhvEAA6illHZHpVYXLf6t

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 9s2E6M8opVoNwXbo/O1nRceuPzj4YOf3Tzy6CTlXzHGGMgJKGJjCAC8MwIzFIAx9oExA4DtgWTGQ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: CFgAwAtCyNTUgYsMlxNCMh5nSUP0bHPtvV0ph+xz5OHjd8nUvvaTY49dNmLECGd7hhmrnnkmHQI9

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: zA18JokCxbj2n8gjQxgB8eVaw2DvRyAHMM4z6GZQqY7jQDxRjnFNgahRQeh6hZ277L5hIczeXsPD

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 4gDW0NAQu+GeezKE0gQJw4SkVNAwNEKNdQiVCOMAImEYSgaBL5Xy27Vv710+cqTthaEHsZibchyv

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: d+/eESHka9Vyaf/YqBSd1K/vdRvsYiNLJMANAkina6BUsEEIBoqjEj3gFP0ZgGBMk++IRLVIBYwR

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: IJVdgaYRfyalVEKRgPvB0jg13ujSZefHexx77IsTzz9/u6TTBgwd2nvh0g9fAEvUhehHKz8XCkD6

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: AXAJT5o1NReXctk5IPieOhLQupQ6RKKEAJURoP/wC6U/X3/r2J+MGjzY25YxIeY46/rrG954bd6A

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ku31K9qlXRhlHRljdYSQGqWUKaUUhBC0mogTcLFLQkjAGAkIIU4gIzuKwnwoZdEAkqNh2FKXrm2L

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: x6wP99hzz1X779Pz/ZWUbsTM1Rdtls3gZ+BPTz50wZIPXgyEIYRlgeMHAJ4sMzvKAysRA0aIRtGt

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 6zdAPJna7Jvxwh1R9dFVk4fvcQGkseKhLaeYkq0dGxp+n7Lifzp1+PAPRg8b5mzLg+x22GHnbsy1

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 3cdTSRIxArIyTeX5AH5oJ5k5lHGezIbuFGJwWvXHqFydKNBKluDl87I+U3Plir/PH/d1LQsqc/Dg

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: wSk3Ht/FjmDvlWtWHqpM84RssdCJmkIwxkgURQS/e8vnVUligMSQjHPtGiIZ6GfHNOBToKJIqUiC

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ySiGayoIgkhKKTnnhbhptiWTyYXpeOIDTvl7CUNsgkC67TKZnO849hmnnZbbrNwRjY3xyU8+/nZo

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: mN1FIq5jXQoMosAHwpDlCCGZTEL33ffQie1sSyu0tLQgXwtU8DKIUWWfrX2fjPTPMOMSOQ6kYnGI

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: XB8szkLfcZcJoqbutftet7w0dWrxX1EsPtQO3+/zO1tFo6llQUi1sdAPKijZwBRMSxnWSM/1bo8Y

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: PU0JRnBsaJpVRX8cFHCcqO+3DjjggMOeve/hxV9LqaNGGWv++c8D1re2XsxMYz8vCrv5URhn3CAY

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: M2jcIrhWIDBa/Zy2GlAua9Lvqz8HIj+9YYAAYWUcgS4kZlqI6sGvMGqcc5XP5xUTNIhbViA4d4WU

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: OQtIwc3mN20ukJs4ZozToX2Hl0PflYHv6XAoioLyTRiD0A8gdDx4b+Ei2H3XbnoQqFi8qoOpDhQD

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ZxRZ+Te3TGgrFQAMDoUw4AGlexb84Jr3li555cBjjhnyr4RPU6dOxaimCy4k/V1oNRAQooIZazZN

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Y1JNMpmASHZjhBJW3dXomzU5I0GiC6NECcHnW3U7feVkwYQJE0TPow/vs+itBU8vWrlsdonK0wsQ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 9nQgSpCYQXCeyJQhyCM4PnRtaFlomRSKcCHiayXU1G6sMgd8hhjGocXD9+j+RMyCRCYNlDP9GQzl

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: HNeFvGuTWG2GkphpFpWfLIZOfXMpv3vBc3pxYapPqh8JUSyK/rZTQ/scmoJqDFhybJBBCOlEUgMP

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Rim88/bbcMABB2R79er1UWtLSykMQ4V/v6WSq2Y5xAsJhWQSSjLS6FqaHGjCIi5RPddkN43b58jD

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: r76ssfHrImsjDIOYVmjFlDFKQEYh7pSFIlRvqojWM847cYx5tQcqm0OF5hAVXL5PKCifN7R37y+1

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: IBMmTIgPGT78+zc/9ODt67O5h5rt0g/MuhpLxSwIOQOFNViIvimBUuCDrxR4UQQBAjZZvtC6oOuq

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Xvicqrlmg5vAqdBJjAgxA6GQiMUh35YDXP+UG8CFCbXt66G+Ywe9gIquDU7og6QEnMCXwjBXxWLx

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Cfvtt+/ln8r0SMJne8XcO4LQwzGbYsawnihE0l1TeTgQ5GtRkX+dNSuTzqQ3fK9nr8c+WL70JM5Y

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: O61UqAAsQoGw8oZ07RJYySQESoJCBkxFYAoBhAvi+P7ubmvLjU0zpg/Z57DDzj7lyCM/+ioV/gXT

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 5GEQGjwegwhDG1Fe/Wj6mYLXNr4xfxPvd2g/RulOqMyqKrWNwB2rtC3ESRcNLhYNGzbsczNBaFn2

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: HDhw7xvuu++itlJhOI+ZcSOZJDT0dZICmTF0SUIIrSgMMBUluuAOKoUCn/haCgQtWwUgkOpFKtau

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: HIYqjVdwwQLxe/f5vh9LxOP4RDds2ABrV6+Eom3r+xu4qPBzQSSpF7518slDh99/zZjFywhRn6pb

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: /u2FF27ikryXNGPasuZybcANsdm84EMUhqH/nUyliOt6eyxbsayLQch4Qdk7DEggwwhw51MNWBCN

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: KuBWTK/aWCKuFcxMC0qBB5hMB8sElkzQrOcetKmQe3zavDmjJzQ1Zb5MuSGlkRA8QvehINIuBMp8

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ssykMq/C0KHED9yBWGyOu1QrAD7BSsg900gBi+Sms088+XMrL8647LKOPQcfe+aGYn6yLegIUZNO

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: RJyTousBZwaEodRRAroDVbkwHMN54uVFZaqzWumBz4UqCuiU8TmRclQBruu4nuetMyxzYZcuXV/v

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: se/3Xt1r7x4fduu2u+OXXP7R+x+Stxb8A1YuXwGO50HMNBG/APECxZwgWwfGI3323HfkGdcMfB8q

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: oPBTyh06dKisz6T+2rx+Ha5+MA0BVWID410NChgF23OrTA8Lw/AYqeBCIORmpdRjKgi9CNEqxpmR

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: BAgizfGGtg3VxIQfBmAlU9r3eCpC3wHJdu2YFOLANS0tN9x4222XT548OfF5Dxyl1nFCw7BsBBra

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 1EaykpNl6+o71rzVu7bWBEpPiJSkEUETWEbyaBQR0CCQYpEGVC9CLrdia7sVOfNnX5x1/wdrVj8Q

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: xkSfogxEgMl/w9AmEdOJCOBQYRge4iNFAIk71Khw8/iAGbp2Vb5Q+/hc0NXh5fv+RkLoczXpmqsa

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: GtpfbhrGtDXr1wfvLlrYe9GiRXsu+WhpzbJly2LFYoFqnwxlq4dgUNoeMNfP1/P4r08/6ZQLX3li

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 2htHkCM206efMssYBpxyyYh3l69e3SYI1EZAwQ08zJho1IZjQ8WgOdBgQGoFkyAI2glGzzPN5Fiu

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: YL3v+eeSULbjnJMIJARBCJYZA64o+KEEysuhElZ+IBkiEgnIew4gnwCMmoFjX3LzIw87J1500cSn

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: x49v2Zpy33vvvTAMghZM2yE1alkm2MWSisdi81JmTc7N579nl0r1VialF1DIoByuISnDOGBanvrB

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: +p5779v0WTeAgGmf/of3afbzl4YmG2TELBZhWIrWS5vRMqLVr1El5OMcOKuwXti1EFVZMgokiEBQ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: VC0JozBypIwKgvIV8VRiDhPmOwSiyPa805ubN/WKomgXxhgzLUvDUj8IgFsG5At5SCaSEOcC3EIJ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: OKVS+tHCvbt1Hzflzjuf6Natm/vZZ/R/2kmOPHvEyq6dOz1u53Jh4Dk6v6v9FH4VLUPzMigpw/dQ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: SSxRIaWSfVQpm7s/bsWLdZmaKzmjr/ieF6Gp5PjHfgilfAFMyvXqdorFsp+hBIIoAlxuUjAQiRiJ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: DFazoS3b+PbixfeOb2pKbk25qJDdunVdYheKilXQfGC7eUHYszeeeaazYtmygbFkIo6+kJoCOCbj

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: fRfiVgxU4IN0fTsdi//2hEMOeXXL+2LLyVNz/n7m+mLLMy6Rw0gsxpHQrAIgnePGpEPlPQouMNyR

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: nutCgOSOBDAxhyz1boDI8QISyRcSpjWmvrbuonZ19dekksl5VJE+ge+NzReLD/qe92NCyG5cCMF4

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: OSZH96c4hYLnQKpdBlzf0fiFSxm4udzzA77f9+zXnnnm4a0pdqvKPb9Pn4ADf1QosjpuCAg8t4x8

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: K6GNqihk8wXleI6UEUFnP/BHEUpziVjipyYXf5Z+gHQMMKWgXSajTQWaJcu0tI9E/6StBhIMjAGW

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: yGCKUXJurG1r/fGEx/7489ubmmJbG3yH9g3vCk5DjqFF6EMmlVofM+OvXz1pUlyGsmfRdRgCnmoG

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: CL/Ptx2wCIcENz8YfurpD4waNWozI9U4YUL8ylt+e+ncBfN/Y2TS7cCySKD9Kfpn0H5S+8rKYkfQ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: hA8frRnjXNdEJwwLCC7Wkh2B7TazIHymobZmcDKRuChwo5dz2dyB2ZbWG3PZ7OWFYvFox7Z3Cfww

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: iTfFEKeaaq0uIPwuYjHwy8sfDMqUSdm039962/kzHnjgna09l89VLsox++//Zl0mNU36oYphCk2V

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: QQOtfKDKrlR3HvpgLgSuYOL6XkOhVLw1UmqQaVk3cc4fZwpshPZ2rgAcTZkfgu96yEPrwLyadtNx

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: XRhoWI8+jVmGWLz0w1/efufvTsPCgM+OM24mlpqMt2FaD3dk4Idv9u7f/2NZjDJu6O+m0SqjGuEj

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: u1aTTEHKjIGbL/oNqfS0xlGj8psVO25czR9+f88VLpVjzFSifc5xiBMEgN0JRBLtT7cERfh+y+eA

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 32EXipHvullO2NxEPHFbOpH4WW0y2RhE0a6e447xI++ZIAgui5TchVLKkb3S5hwVyj7JxqFiyxtK

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ldOvWFVSstEKOEGh8OJhffvccM6Pf7zqixT7ucodN26cc0CvA+8WBDbgTemWE1LlUAevKo2Gg8Od

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 4csKIwNqj6JTGu8F3klJxkYZQlwdF2Y2bpgK0XTMsqA2mYbWjc0aeHE0P5UUHIZfGKxjkR41BFjJ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: REPetW96Z9OaPT47zpqMt9EwjLmmMJRTssPdd97lsaljxgSt+Q3tiZK7Ec4IhiGpRByEUlBszUJY

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: cqKkYT15YO/e929W7F13pf/Y9HhjZNCrHCnjkSHAiMeAM6HjzfJGJRoQ4fwRZRNEyWEZMCLhT6Vc

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: wTm/wkqZfRPJ5E8NIWYGUg4ret6ztuff7Uby9AhIOyoMgnNEYgfnh34caDXFiopVm1k+JDIQrjEv

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: VMzxcj26drvkwYfGn/zU+Pu+EpP2uS2cgtLmpBWfR4FI3G3oN7Xv3EKxm/ljVC5WQUi0XQQpIhKC

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: irkyGm4Lej5IOd2KWSMLudzLoJTv2Q44tg0GKyNNVLjOIyuokA2fsFz4VBWjHea+Pn/sKRdeuM+W

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Y2wP7W3LNBcIykKqoNihY8eluNRrUqm0GTNTuA6RBi20ZSHGDUgKC3zPeX9Av35jHho7dj3e439u

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: vTV1z6SJozcV8z/zKIk7WOnMMcFAwPdDMAyrPKeKoEvBiyIwjFTRiODtGGHXJ2LmsEQ8OSXO4ntF

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: YXBNvlR6OG+Xzih4TmcQzJKcUowOsIDPCXzwKhcSECho4nX16RbWQKPjIFQyV1jXMZa646ZzL5gy

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: rM+g3FdR7Bcqd+o99xSplLdJL1ixOaxBknvLeK2ym3WKEPOpnG32FRzpN8Hbu0Fwve05//OLa897

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: tuseewy17dLdBKRHZAQ1mQwErqcXi84woQ/GMEoqsLDITcqyTxYMgczxf531wh+vGDt2c4iEoCpS

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 8JaSMmcKA3eOPr8im8+3LxVLFj6wdDoNNekMuCVblXLZ5Uf2P+ziqffcsxTK4Q57df4rp0SCXs7T

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 8ZQNEaQa6qDkOZp4wNShXSyVwVRlIauyYm0B9NkU4/1imcyAVKb2D8RRHQotG+c0t2yYWvBKI8Ek

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: XVjaIjyTADv0tVVD/4/uprpr8T2QT5RadU9QwTESFesGH40cesZxq/7+xo1Dhgz5WvVdX9h8PeSU

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 09+qTSWnRwHWzkWYPdicKkMF6xWmQ6QymtQIusKhVuqTCOOcF3132FNTZp25n2Xljzl04E0mYfcZ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: QF2nkIeYZeh7VbnhzYDC87XiU8mMDpkiQqhIJfZ/Z/78g7ccY9ISy4J8Pmtxvn7ooEEaNWZqa+MY

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: oiVMAwotLaA8D9y2bFtDuuYX1/7853/fPL9zzz7o9YWLrvYoSeZ8bKeJQTZf1OgXF63vOYCgkmmy

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: QwELJVZ2bBBArzGkef6Gzp3/yUxT+S25q6IoeEAIszsXpol5MTeMSIA0Lsa8VkyTQVAJJXHHYnUp

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: WiedAapwyvrZYvwd4oWVJLJgKTW+azr9ISHka5f/fKFy7x41yuu37/63W0IstBLW5hIcgwkdx0U4

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: eKkAY0DkVKuX3Hxzqak+nkk0vLbwrbtf+HjZo/v06FEzZPDxV3apbz88yOUXqMAPMNWFRD7WP+Hk

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: VSghJpCBMUAFEUQhASIs8AxOF65ecWnjvfe2r47x+vMuXrZrfafnVd5Z2qFnT51CLDqlGp3e8wKw

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: pJKqZM8+4Zhjj1y1YMH0Pn36aHg+6JRTjpn7+oLp8YZ2uwUYKiUS4AdlUkIQBr5tQxLDQLQcRcev

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: IWJmO9O6oF+P3gftn0qMT2REx5plKyZ5zS0fuFReEQnWoDgniPgVpSCYAE4MYCHFUAgSwtQWCUM2

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: zg3dwY99wrghMPkSBL6OJJCwZJ4b1Qlj5olHH3VM61vv3Dl69Oh/KTX6pccm/OCCCzYkDPFktqXV

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: re6uYiVGxf2LA93yVpsRdRV84XvBESjxUIbHP/b4Yxf26tGDvD1zZtPoyy7/mXT9vwkCMs65Cn1P

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: m0LkaLP5PFhmHDw30CQIUpYBo6TFzh8x7bmnj6uODznh/fbc48GEYPOP22MPrThLmDEZBTTIF3zq

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: erOOOfSIC564++7NYcNxl1xivrfkw595APWYCtS4SJZTbLggEER2qG2HqFqGttsSF9b9h/bpf945

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Rx89CWKQ+yhUQ9ev3/gQs4yz7NDfOaSUBoTqBAkudLyT2uLR4oLBNClKKpXSJhfNPs5TL8ZCHjKJ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: OISeq0qtraFFyAfHH3v0FX/8ze/mwzZUrnxp98CMiRPlD44/4a3mXAsTnPcu2SUjnkppc4ylLZjn

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: RQxUnQ5TW1BtFVrcc8pVHJZlcdf3+7355lv7rGlre/W3o0YtveXu8bPn/e0lo5DN7Z5MpuKOa2tT

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Fk8mQKL/VpH236VSCeKIpAkxshtbeoz+xeWPznvxRW2G3/7HPzYo33//4IMP1qYrVlfX03OddMK0

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: xtXWmr+eM3X65rBh8vPPJ56YPPkOj6pTsIXeQ5bNNLXZNRQFGkR6l9m5wrq4ZU1NJ8SF62PxyW6h

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Vc19990ha9avvyFvly6z0onOIUgSVlOIGGvT6qv65CKYjGdgIKunInB9V783TUNHCFgzbXEBFmIO

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: L2j9/n77/fKMYade85vLrtjm4vivdOAJgqv99/re7eD5f4qZlsR6KgRNtl0sk+KyzJ/yLTnULSRh

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: WnoC+UIJXBmSDdmWHz721FOnNTY1GaOHDVtz6YgLrqkVsclRyY0wzMAcsu06UPRsDTxc3aiV0P5Q

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: cBOR5c7F0Nuv+g1YW7QlGdGrR4/pXes7nL7mjX+M/2D2gk/Rlzf/7pY+G+38aS5TVsjKmCGyPRCh

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: AiNUkBaWCkrO6r69+5zzyIQJl64+6rhFHQzDbG1rvXLTpk33gmDH0ZhhlTxXJwXQlUAF6UIl6f6p

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: kLHiZzGSwEWgQRMueNuBwHGhNp6AOKGqec2afMf6dndccMKP773q3HPXbqti4eueQ/W9QYMGrm7b

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 9EjOs3c2kimIJZK6LVLXCKsyg1MVHTURqJD0WOzggZGMa8gfBR4EJcdrH0vddP5ZZz1w7ciRa+6e

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: MqXTDbfcepMdBceLTLI+Mlg5T6kImFSA57jlqo8oAkvJ0AzldateX3Dr1ymLuWLsFYkHnph1Z1vo

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: nRPLpAj6PMxcWUDBy5egNpEqha43+4iBR9w15c7bZukO/aOOOmBDIXdyKfAvS6RTMbQgVjymTarr

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: e5tjfaiSO9Uqiy1GhSATSQ6kcnXmKoz0gpdeAEGpJAWoFft/r8eNv772+icH7L134evo5IvkazV1

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 3XDVVWvnv7f444iQfslkItWWzRKkETFtRcrNvrA5c1pRrv6BlGDoSkpfh03YyxP5Pncdt9/iRYvr

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: Ro8477mrLrkkd92v73pp3fpV7zW3NA/0Aj8REkXwbzH9iiE0mjVdwuP7JCaM4l577f38Yw8+uFVe

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 9bOCDNfkZ58/y6bRxejMMeZEUCgIBREoSBuxdTvV1v/PPbf99pYrz/0Zdvrxky699MA169Y+pIQY

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: IgW1ENUizYjhGc4D8Ue1XLY6d81kKag8j/Irfoay8o7VgEr7dk+5+YKXNMWMvbrscvXLTdOe7drQ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: sE3FeZ+Vr3UO1fnnnx/86szhf6oxzd9sXLrcrk+ngERlWh15T+2D8cJQqNJnhIpG0h7hvylMXQOE

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: qS4jFgeRjFlZzzn5kRdnn3fLhAmZq88flps2/pk/xxT8Lk7ppgQmv4NyHIymz4zHIMCwgXFS8oO9

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 7/j973f6qmNfvGZjr035wq/cUNYRo8yoGQlqgqgAAAvdSURBVJRDnIrIK9rvZJKp6/5w45jJR/ft

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 2/LCu+/Gzx0z5vJVK1c9rJTqGQSBERcWCGC6qSwZT4IKJKTjKfBsr0JPli+qyBYp+PJuRqCIqFui

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 9VIKTEJUUCx81KWh/a9Hjxj5s79P//Pcr1u2+lXka7djzpgxQ47+1f8u/nDpkjCfy+5HGY1T7XMq

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: F60kFyo7F6cY4W4VWNjlA6MMglD35kCEq1wws1QsDtywYVNp7ZIPXrnzzuvDGa+99ua6j1b8Y9Wa

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: NQcSCQ1o+BhyxBgmaQKMQeh58Q7pzEsr33//gy8b812PPJK+54H7xiba1x0SMU3gYk0pmJIou7Xt

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 9ctHXvbzi8bfMat/587hMWeemXj4iSnX5kqlq/0o3MlKJAm2g0a6FIZpK4RECxYLIi7AnQvqExtc

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: bZ6rXmWyR4GFLaVAwc7nsrVW/MHDBxx8yaiTT3nu/LPP/rc1nv3LfT1jx45NjH304Qtdom7k8biB

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: LEyEhV7IwvByzyuucLfCTbNKNYKu1NC2qpJZQnYqDMGIYMmAA3sP/tPEiR9B5WzGbgP6jmjOFe4Q

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: qYwZYXGd52gAg6U0ZqRk7927X/33qVPHftlYd+vfv08h8ppsGXSTFeKAYcFw0X13n932GD3/mWd0

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: JfrQoUONl1auvM6OvFGUsbSnD0pJgeeWqymEpiKlzrFieIeMHFZF6DEpJGDKjd5+pfpCKx4XheNC

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 0jCiXHPr8s47NUw49MgfTHqosfHffhrsv3w84JVXXlm69pwRd8UpvynbvHE10oUWUmoVgIEPEPO3

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: GONVRfPRmJ/TZTusnB/Faoa4hVVqu788f/4TR5955g+ggoBHn3LGY52SNfdJ183b+TwkYpa2AAjK

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: mCHo8lWr9vyyceIiISb7Ud61O+HRCBjmUCeQMclm7t1l11Nemz79Zfy7u5qaGuasWTXeVcHlThSk

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: AwoQT6d0KRCGYrFkCnJ2EXxZ5tBRsRiLG5ZZLnBTUvtTBJdYecIpAd9xMbGQSwr+QoqJK04a8qOj

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: n7j97ju/CcXC9ji19a6ZM83npk8dMmfB/EkRFxnNl1Y6EVSg9FkTSCXKT5mrSrqwwtXihVUYGBrE

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: GFtw89XXDrlo2DBN7N9++6S6cVMm3O1Z7NR8GBFsRgPGIMkMECX/L5eeeMIPv6ig7vIbb+x6+8P3

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: v5GoyzQg4+UWS0ra7sJfXXnV2b8aMUKfEodga9ILf7myEAbX+1SZmE9GRGzni5o2dMNyWh7dAS4u

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: UglxsHgQARQWD8ZNU79iiIP0pe85PgX+Tm0iMXHgUUc1PTJmTOGb7tjf5oM9sf1i5h8eeNoC+kuL

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: kEUxwrygUISMGdflLDplWOGiUaJq96CsFNExVuZyhaHLXnk83uPWceN+umDBAk3fjB7989afnnji

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: PYVNzZuwQyARj5ePQVB64TR80RzwHjNmzTorXVtTjzuqlMurJDM+GHjQwedfc955mrHCyoum2bPP

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: bm5tGc0EN6udAYVcvgII0ckT4DEBShBwijlQtFzTXe5aQM45UjyKQub6LTEF7xme9/ROiczp55xw

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: wtGr5sx54NHGxvy3cRTDdjm1FRuXb57w0KR+++x7SkLR2xqsZMFty+ocqoF+NSqXs6IyuQ7iQSf/

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: qyEEFl5jvIwNZ8BZLOuWLrvmjjsOr95/cL9+8zvUZBqjYqkFC8NS8QSUCkUo2bYJXbt+7kEsD8+Y

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 0aHg2oNUGGEtD8Q5b96prt0NMx95ZH71Yf9x1qzua1o2/TJTV9uAY0BMgH+LKUKMr7F2GJkkRLxI

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 6mfq6jTvDBirF/LgtbX67RPxebTo/O+u7evP6Nau/uTxt407a9mcOU/dXVbqt3YQ93ZvlJ49ezb/

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: xS03Dft47fqzuWn2VpzVISuFfhILx6ptFTprVAlx0CzrszhkCMVsDhKUQX0y/eaJxw4+/bZrrnkf

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: /x77fc+85sqL3vpw6S2punYGxpLcDRf98vQrDxo9eus9R4cNG3bY0pUrnwRBGvySvX6vXXe9buIN

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: N0/ed999fSQorvvtb/d8dPq0u/KeN8iRIUkm0+B4LjAqyqnGcvpNKgqhEMwDKb3Q89pkJNepIFjX

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: uaHDG/vs3X1u9z33XTjuXyT3/52y3Y8fOuKII/DglMev+c1vZk2bNeuQZjt3MaX0UF9FBgl9IiQD

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: xoQO+aNquMSo7oXBmBl7igxCoLmQ6/XIjGkX3nXXXVcitYgKGdHYOOHj9RuGRVL19fDEO+AB5+s/

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: d2e0FYt9/DCoFYoW2tWkr68qFn938x137PTUn2eMb85nBxLDJJh082xHeY4jiaKY7Mru3LnLeqeQ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: ezVG6eJONbXLWnK5Urq+LvuLyy/flKyryx7fu/d2bUnd3vJvOVuqYoo2AcD0S+666y9PNj16tnSD

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: EdIP9gXGTCKAYKU8Nm9FlTgYfR0lvAxQGMP2E+5LOWTBunXXYxsw3ndiY6PduX/fJ1py+QNMYZjJ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: mNnc2tq61XbQEY0j4rNeev8MyxC8lG175aGJk6ZVFYvy0rx5Ry35cEnv2p3at0agVlsKVqcsa0l9

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: PLnEMoylQ08+eTFs2JBtHDMGg3K1JYt/6vPP/3u1sp3kGzm/Ak1108svd5n/6qt9bN870vXsg1qL

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: hQ6EixpqCCMiiklUbaWVERGpznf6Qdi5rv7cRc8++8fqDul7/PHdl61aMZEADKxLZn7/z3nzLt3a

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 7hl40glHvL9y5XSqYNNO8dQ5P5ozZ25jxf9hU/S9j00+vKWtEE/VpdpMxdddfPHF2VrGcmh5voln

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 8k3IN37MUKWKkfIOHTo88fST38/Zpf1c3zsgUNDVC4POQpgxx3EMLN9FOGNROnfsL2849cyfHLuu

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: eo9Bpw/r//7i92fUJtNnvTtv3rOf/Q5U3tgH77t1+erVZ3Vo1+6sxbPnPr8tedEdVb7xI/8qMSle

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: eP7Emialnu24cmH6/nsfyKxesb62JZ9PJSyrXT6b38l33Q5uoRCLm+Gn6pa7d+/xzqolyx494IAe

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 7707b97/+Y4/zZ6d3rhmbaeaZOqJnRs6vr74v1Cx33lBRLu13l382e8mTKjHAretzeHtt9/u3OuQ

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: vhciwfLf/gz/4wR9/Jc1kv03yDb7XPShPXr0IO8BsI5tbaq2tlZit+B/6v/pZEeSL1Qumr+XVqww

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: X54xo27ua6/VOGGYAA61MW50/2D5R3sKxtt5UVgvwzARoYnUh1IwPJ8h70ZBThCytnPnndd4jvth

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: fW1tq18seBBA8cZf/SpbTKXyg7t3367J6a0Jkh9hGIogCBR0AoC1AIjM2vm+MnbdlcDatdAixObn

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: gAliJLUNzokfhqqaMNaf30L8Dh22eL8KGtwG+dYee4TDCPlax/kuWLAgnkwmWZaxzWMIsUTkM1Ib

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: RQpPMNjyp+u3GPNnBTsst6pcVOroceNqm5qmnOBT6B9FsGfMsjq7QVhr28UYZUIYJme6+D4KdNjC

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: DIZsBDhuSVdFmhaeWRWpSAbSMowwCvzAZGbJ5LQlsL3VnPHlFmeLjjjiqFmFXr2WTv2CzvZtkcHD

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: fjLszUXv/cSMWxKPMJAqUoZhKddxpBsFhDFBDMGp73t4coZ+HliArvlwzvEEGd1FSLD2Flk23btD

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: kTvVZw3p4yZASRXJsEunTvN+MfP5h7+qgmfPnm1dds0vb3b8oHMESmAMFip9pg0WPejuJPweQ1vB

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: SJcCRPoEhEp7YXkESm9S7GbUw6Ggz0Fimg/81CoSdzY17dFl4CEjck7pcNt39zZjCRM7s/C4g1Cf

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: zmJAMp0B2ynpu2LJaYgdCZgJwiMBhAGCG+D4LlDOiRAGiwCYH4WmJJDE1hCPqH1U6CkHRPT4M9M3

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: xZ5/7oWDTj7xKVJy57w2c+Z2y57gIj1gQN+uIcgTCSU8Ukyf+4gneaHJwPMssfjOx6MHRPxTGtEP

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: UVGlsJgcz2bGA9j0MQtY7xPqY44y6TTJZ7MKO9wj34cVHy8zks8992jl418qmzZtCop2qU/J8w8W

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: MUtEWPQgmPLDSHc64vkKOmesVVrujy6f5YGH5ZYPS0Ep/78ZCC45ffSS5ByUDOH/AcRNdcEflr82

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: AAAAAElFTkSuQmCC

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: --b2=_we1QxwC2foZUiE6SaP2shqyV98tvtWJmEg9EH0UzY--

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: --b1=_we1QxwC2foZUiE6SaP2shqyV98tvtWJmEg9EH0UzY--

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:01] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:16:02] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkby85Yz7z2SrxG

[2025-07-19 08:16:02] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:16:02] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:16:02] Email sent <NAME_EMAIL>
[2025-07-19 08:16:02] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:16:02] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:16:07] Global sendEmail called for Jennifer Godson <<EMAIL>>
[2025-07-19 08:16:07] Initial email body length: 6426 characters
[2025-07-19 08:16:07] Birthday notification detected via explicit flag
[2025-07-19 08:16:07] Birthday member data extracted: Array
(
    [name] => Ndivhuwo Machiba
    [email] => <EMAIL>
    [image_path] => uploads/685dc5657df2f.png
    [photo_url] => http://localhost/campaign/church/uploads/685dc5657df2f.png
)

[2025-07-19 08:16:07] Email type: Birthday Notification
[2025-07-19 08:16:07] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:16:07] Set Reply-To address: <EMAIL>
[2025-07-19 08:16:07] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:16:07] Birthday notification - letting PHPMailer auto-handle ContentType
[2025-07-19 08:16:07] ONE-TIME FIX: Processing HTML email with single image embedding
[2025-07-19 08:16:07] SYSTEMATIC FIX: Found 2 images referenced in HTML
[2025-07-19 08:16:07] SYSTEMATIC FIX: Referenced image 1: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:16:07] SYSTEMATIC FIX: Referenced image 2: http://localhost/campaign/church/track.php?id=email_687b3827627055.06414424&mid=52
[2025-07-19 08:16:07] ONE-TIME FIX: Processing birthday notification with single image embedding
[2025-07-19 08:16:07] ONE-TIME FIX: Found birthday image referenced in HTML: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:16:07] SYSTEMATIC FIX: addEmbeddedImage called with filename=' ' (length=1), CID=birthday_image_e6fbcdadd3e121504fac07d45d120aa2, mime=image/png
[2025-07-19 08:16:07] SYSTEMATIC FIX: URL replacement - Before: 1 occurrences, After: 0 occurrences, CID references: 1
[2025-07-19 08:16:07] ONE-TIME FIX: Successfully embedded birthday image: C:/xampp/htdocs/campaign/church/uploads/685dc5657df2f.png as CID: birthday_image_e6fbcdadd3e121504fac07d45d120aa2
[2025-07-19 08:16:07] SYSTEMATIC FIX: Final validation starting...
[2025-07-19 08:16:07] SYSTEMATIC FIX: WARNING - 1 original URLs still present in HTML!
[2025-07-19 08:16:07] SYSTEMATIC FIX: Remaining URL: http://localhost/campaign/church/track.php?id=email_687b3827627055.06414424&mid=52
[2025-07-19 08:16:07] SYSTEMATIC FIX: SUCCESS - All embedded images have CID references in HTML
[2025-07-19 08:16:07] SYSTEMATIC FIX: Image processing complete
[2025-07-19 08:16:07]   - Email type: Birthday Notification
[2025-07-19 08:16:07]   - Images found in HTML: 2
[2025-07-19 08:16:07]   - Images successfully embedded: 1
[2025-07-19 08:16:07]   - Processed image URLs: 1
[2025-07-19 08:16:07]   - Processed image paths: 1
[2025-07-19 08:16:07] SYSTEMATIC FIX: Embedded images summary:
[2025-07-19 08:16:07]   - URL: http://localhost/campaign/church/uploads/685dc5657df2f.png -> CID: birthday_image_e6fbcdadd3e121504fac07d45d120aa2
[2025-07-19 08:16:07]   - Embedded images: http://localhost/campaign/church/uploads/685dc5657df2f.png
[2025-07-19 08:16:07] Set HTML body (6419 chars) and plain text alternative (2604 chars)
[2025-07-19 08:16:07] Attempting to send email to: <EMAIL> with subject: Birthday Celebration! Ndivhuwo
[2025-07-19 08:16:07] Email content validation: Body length=6419, HTML=Yes
[2025-07-19 08:16:08] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:16:08] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:16:08] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:16:08] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:16:08] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:16:08] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:16:08] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:16:08] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:16:08] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:16:08] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:16:08] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:16:08] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:16:09] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:16:09] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:16:07 +0200

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: To: Jennifer Godson <<EMAIL>>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Celebration! Ndivhuwo

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_q1dGnEEa7M68cmQQgMbydhSB5RgWQYURawtofjTaM3Q"

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: --b1=_q1dGnEEa7M68cmQQgMbydhSB5RgWQYURawtofjTaM3Q

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=UTF-8

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: quoted-printable

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Celebrate Ndivhuwo's Birthday! =F0=9F=8E=89 body { margin: 0; =

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: padding: 0; font-family: 'Helvetica Neue', Arial, sans-serif; background: l=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: inear-gradient(180deg, #fceabb 0%, #f8b500 100%); color: #333; line-height:=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  1.6; } .container { max-width: 600px; margin: 40px auto; padding: 20px; } =

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ..card { background: #ffffff; border-radius: 15px; box-shadow: 0 15px 35px r=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: gba(0, 0, 0, 0.15); overflow: hidden; width: 100%; text-align: center; } .h=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: eader { background: linear-gradient(135deg, #ff512f, #dd2476); padding: 40p=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: x; color: #ffffff; border-radius: 15px 15px 0 0; } .header h1 { font-size: =

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 34px; margin: 0; font-weight: bold; } .header p { font-size: 18px; margin-t=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: op: 10px; } .content { padding: 30px; } .photo { width: 150px; height: 150p=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: x; border-radius: 50%; border: 5px solid #ff512f; overflow: hidden; margin:=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  20px auto; box-shadow: 0 10px 20px rgba(255, 81, 47, 0.4); } .photo img { =

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: width: 100%; height: 100%; object-fit: cover; } .member-name { font-size: 2=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 6px; font-weight: bold; color: #ff512f; margin: 15px 0; } .birthday-details=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  { background: #ffe5d9; border-radius: 12px; padding: 20px; margin: 25px 0;=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  display: inline-block; width: 100%; } .birthday-date { font-size: 20px; fo=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: nt-weight: bold; color: #dd2476; } .birthday-age { font-size: 18px; font-we=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ight: bold; background: #ff512f; color: #ffffff; padding: 8px 16px; border-=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: radius: 30px; display: inline-block; margin-top: 10px; } .countdown { font-=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: size: 18px; color: #333; margin-top: 12px; } .suggestions { background: #ff=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: dfba; border-radius: 12px; padding: 20px; margin: 25px 0; text-align: left;=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  } .suggestions h3 { color: #ff512f; font-size: 20px; margin-bottom: 10px; =

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: } .suggestions ul { list-style: none; padding: 0; margin: 0; } .suggestions=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  li { padding: 10px 0; border-bottom: 1px solid #ffb199; font-size: 18px; c=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: olor: #555; } .suggestions li:last-child { border-bottom: none; } .action-b=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: utton { display: inline-block; background: linear-gradient(135deg, #ff512f,=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  #dd2476); color: #ffffff; text-decoration: none; padding: 14px 35px; borde=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: r-radius: 30px; font-size: 18px; font-weight: bold; margin: 20px 0; box-sha=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: dow: 0 8px 20px rgba(221, 36, 118, 0.4); } .footer { background: #f9f9f9; p=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: adding: 20px; text-align: center; font-size: 16px; color: #777; border-top:=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  1px solid #ffb199; } .church-name { color: #dd2476; font-weight: bold; } .=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: unsubscribe { margin-top: 10px; font-size: 14px; } .unsubscribe a { color: =

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: #777; text-decoration: none; } =F0=9F=8E=89 Happy Birthday, Ndivhuwo! =

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=89 Let's come together and celebrate this wonderful day! =

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: =F0=9F=8E=82 Dear Jennifer Godson, =F0=9F=8E=82 We are excited to celebrate=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  Ndivhuwo Machiba's birthday today! =F0=9F=8E=89

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: --b1=_q1dGnEEa7M68cmQQgMbydhSB5RgWQYURawtofjTaM3Q

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/related;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b2=_q1dGnEEa7M68cmQQgMbydhSB5RgWQYURawtofjTaM3Q";

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:  type="text/html"

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: --b2=_q1dGnEEa7M68cmQQgMbydhSB5RgWQYURawtofjTaM3Q

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=UTF-8

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: <html lang="en">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: <head>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:     <meta charset="UTF-8">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:     <meta name="viewport" content="width=device-width, initial-scale=1.0">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:     <title>🎉 Celebrate Ndivhuwo's Birthday! 🎉</title>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:     <style>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         body {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-family: 'Helvetica Neue', Arial, sans-serif;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(180deg, #fceabb 0%, #f8b500 100%);

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             line-height: 1.6;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .container {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             max-width: 600px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 40px auto;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .card {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffffff;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 15px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             overflow: hidden;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .header {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff512f, #dd2476);

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 40px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 15px 15px 0 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .header h1 {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 34px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .header p {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .content {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 30px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .photo {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             width: 150px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             height: 150px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 50%;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border: 5px solid #ff512f;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             overflow: hidden;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px auto;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 10px 20px rgba(255, 81, 47, 0.4);

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .photo img {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             height: 100%;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             object-fit: cover;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .member-name {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 26px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff512f;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 15px 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-details {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffe5d9;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 25px 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             width: 100%;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-date {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #dd2476;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .birthday-age {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             background: #ff512f;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 8px 16px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 30px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .countdown {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #333;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 12px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             background: #ffdfba;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 12px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 25px 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             text-align: left;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions h3 {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #ff512f;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 20px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin-bottom: 10px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions ul {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             list-style: none;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 10px 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: 1px solid #ffb199;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #555;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .suggestions li:last-child {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-bottom: none;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .action-button {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             display: inline-block;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             background: linear-gradient(135deg, #ff512f, #dd2476);

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #ffffff;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 14px 35px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-radius: 30px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 18px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin: 20px 0;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             box-shadow: 0 8px 20px rgba(221, 36, 118, 0.4);

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .footer {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             background: #f9f9f9;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             padding: 20px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             text-align: center;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 16px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #777;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             border-top: 1px solid #ffb199;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .church-name {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #dd2476;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-weight: bold;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .unsubscribe {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             margin-top: 10px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             font-size: 14px;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         .unsubscribe a {

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             color: #777;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             text-decoration: none;

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         }

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:     </style>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: </head>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: <body>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:     <div class="container" style="text-align: center !important; margin: 0 auto !important;">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         <div class="card">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             <div class="header">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <h1>🎉 Happy Birthday, Ndivhuwo! 🎉</h1>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Let's come together and celebrate this wonderful day! 🎂</p>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             <div class="content">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <p>Dear Jennifer Godson,</p>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <p>🎂 We are excited to celebrate Ndivhuwo Machiba's birthday today! 🎉</p>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="photo"><img src="http://localhost/campaign/church/uploads/685dc5657df2f.<img src="cid:birthday_image_e6fbcdadd3e121504fac07d45d120aa2" alt="Ndivhuwo Machiba" style="width: 100%; height: 100%; object-fit: cover;"></div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="member-name">Ndivhuwo</div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="birthday-details">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="birthday-date">🎈 Birthday: Saturday, July 19, 2025</div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="birthday-age">🎂 Turning 40 years</div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                     <div class="countdown">🎉 Celebration is today!</div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <div class="suggestions">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                     <h3>🎁 Ways to Celebrate Ndivhuwo:</h3>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                     <ul>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                         <li>💌 Send a heartfelt birthday message</li>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                         <li>🙏 Pray for their growth and blessings</li>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                         <li>📖 Share an inspiring scripture</li>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                         <li>🎁 Consider a meaningful gift</li>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                     </ul>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 </div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <a href="mailto:<EMAIL>?subject=Happy%20Birthday%20Ndivhuwo!&body=Dear%20Ndivhuwo,%0D%0A%0D%0AWishing%20you%20a%20wonderful%20birthday! 🎂%0D%0A%0D%0A[Add%20your%20personal%20message]%0D%0A%0D%0ABlessings,%0D%0AJennifer Godson" class="action-button">🎉 Send Birthday Wishes</a>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             <div class="footer">

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:                 <p>With love from <span class="church-name">Freedom Assembly Church 💒</span></p>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:             </div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:         </div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER:     </div>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: <img src="http://localhost/campaign/church/track.php?id=email_687b3827627055.06414424&mid=52" alt="" width="1" height="1" style="display:none;width:1px;height:1px;"></body>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: </html>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: --b2=_q1dGnEEa7M68cmQQgMbydhSB5RgWQYURawtofjTaM3Q

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: image/png; name=

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: base64

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-ID: <birthday_image_e6fbcdadd3e121504fac07d45d120aa2>

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Content-Disposition: inline

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: iVBORw0KGgoAAAANSUhEUgAAAHcAAAA/CAYAAADaMleLAAAgAElEQVR4nO19CZhVxdF29XbOufvM

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: MAMCCqLiggQXiIiguGGUiIlGMO5EI4oLGtRPY/RjjEs0RHEJGhA3oqKDYlCCUWJQICpK3ACjiIDs

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: yyx3Pfvp/p/qey+iH26BqCR/Pc/xcmfmntt9qrvqrbeqWgb/Xz4lSim66667mq5hpAcNG9ZhwUsv

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ZXfUJ0S+A2P4TkjjI3elH5v8VH/H9Q7klB1gF4uHEICXLzj+xNMbGxvljjin/1rl4g4ddv1FcWbT

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 9LLlq/quXLfhjGwhfyiltDYVT7DIc0udO3Qc+c6f//LId2C4/5LwHXDM2yyXNDamdz/04J8Ug3Cw

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: pGo/4LxLICOjfaedSDFfAM/zgBP61sBBh895589/2WHnSb8DY/jGpKmpydh30BH9n3xhxrMtrn1H

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: q1v6Sch59zbHNakVI0GkgFIOKghVp4YO8wZ02m3NjjxfbZaVUvqVEKK+9RH9G2R4Y6P1yqtzv+94

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: pWOy+cIplBvdqcEBuICi60DXrl1x7rB62QpIGhZEhZJ7aP8BR02/555XduR5a7P8ozOH7S6FmQaA

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: N7/9IW1fOeqss9rN/NsLwzc2bxidydS1Z1aCRZGCMASgSkJNTR0EQQT5fB6EaUDgB2AZorlD+/Zv

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 7+hz12a55JNdFr7/wfWnXnppr29/SNtP+h533M4L3n3z1xvbWm+q7dSpkxSCF12PMGEAKAKcC7f7

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 7t1ztm1DNpuFKIogHrdAymgFdOwY7Ojz18oteCUjkPLwv/z1+SmHnnRS/29/WNsm6Ga6Dxiw2/IN

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: G6dQZvy8pq7WROUhUEokEtV7tx100EF/Wbx4MUXl1mZqgBEKhUJBCmHOnzBiRLiDTn+z0MbGRpor

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: lohIxKgPcu/VG9ZeMnTo0B2a3PjBqafu3OYUb4+IOiSiYLiuC8l4AmKGqRXs+z507bbrpoULF3Zz

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: XTchpQT8G3wlUkWB6yz7T8AftHHMGEU5Z2iuRDJJWx37pFdWrLi/749+1KMKtHYkubCxMfnmB+/d

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Ekg4nsXjlKIJBoAwDDVoQqVJpRZ5rre8tbW1p2VZVDAODIi+lJQbM+nkoh1t3lsTCoQo23dUwSmB

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: pxTweFzYYXDGkmUfjTv67LPrvntD/nwZ2tTE5rwy94dOKAdLQZkTBRARAGHG9GfQ/HLO7Z07d3ps

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 7fq1dbFkguFuRYlCHwwhQIbBGkaNld/F+X1d0T43U5ORTJiKcQPytgPEshhY5tFv/nPx07secciP

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: RowYIb7rE0Er8+rYW85ZuX79eCuTrOGxOISEgGQEfweRH4FlWaqurm5+c0trf0MYBxSLRVS2NtXx

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: eBzy2RzUpFJv3ztmzOrvwJS2WbRy8wVHUcaUJABEcCi5DoRAaNH3B2zM5sf99f3FgxobG7/TbNYP

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: fzasgxtG5zPTamf7AZR8FwKQAIRqFGwYBgRBUCrZpQWu5w4KleRWLKZ9rSUMUGEEBqOKKLX48MMP

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: j74DU9pm0cqNUSE5YwpXuB+EYFgxIEKAiMUIi8W62b5/34MvzLxq5E2NvZqamr5zYAt37Ucr1g+I

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: QHb3VQSKUaCMgzAsCGSEIAl9aRQzzUkyijKMMYo7FhWLSsedi6++5ztdunZ99T+FzKGV/0QyCENU

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: biwWAwkKgiiEEBTuYGgp5ju5IK97/Iknm258eNIZp40cWfvtD/0TuX7cuNrmbPY0KYwkMw0Qhglo

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: hVBpVAEwwoAT9gLj5tN+EPSjgvMgDME0TYiiEBLJGBQLOahNpzf23Kfb8u/KvLZVtHINi4eM0RDN

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: F2MMEGOEUsf5+orX1IAdhiZLJfdasuLj+178xxsPDD7vvD0xjPq2ETV+/4y/vdArkPJoJjjFhYmL

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: FHerjCLglAFVyhGcTiZRcLCM5J74e7y4YBApCRFaKy6UkmoJQMr+NuezPaW8cwl4nHEXV3kUlmN3

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: NFsYOiggICmDguuCK6VG0z4lx89949WnJ780+9GDfnz8UaeOGFH/bU3g+okTY2vXbRopYlYy1BZH

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: aR9LKQWDMCARKi94RyqVLRSLF3LOTQyL8PeoWJQgCHCuUSqZfLMTgP9tzWV7i1Zu4ISlKAiLgnOk

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 3jA6AsYJRKC0eSu5LsTSGdQ4UNPCXcytTKbHxlL+p4uXL39y5muvPPy9Y47ojaDrm97J06ZM2c0O

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: vD4RBYLKYqK8KJFtokSBi+ifwNMykMeFYdiVUkooq+zYyiLAy+QilGH4YWNj4w7PTFVFK9fKmCXH

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: KeU5L09U71ilNGODZs6IWRCEEbiBD3nXAWIYYEcRSMYBYlaGJq1jP25uefrhl//6bLdD+10yZPjw

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: vRcvXmx8ExMoRX5PYvB6hVOhXLsVHfqEIXBFFJVqrmFYs6Mo7GtZ1mbkjK9VQbRcKpVswWjzNzHm

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: b0q0co888JCsjKKspt8wNpTVVV1WsmvbwAwThBUDKxEHNwpAcgoBVcBiJrS5JaossUshcI9tLRVu

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: f37u35oG/3z4tfsfe+yu/855NM6ezcMw6M6FiBPO9MLUTBQ6EymBgvJjhvEAA6illHZHpVYXLf6t

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 9s2E6M8opVoNwXbo/O1nRceuPzj4YOf3Tzy6CTlXzHGGMgJKGJjCAC8MwIzFIAx9oExA4DtgWTGQ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: CFgAwAtCyNTUgYsMlxNCMh5nSUP0bHPtvV0ph+xz5OHjd8nUvvaTY49dNmLECGd7hhmrnnkmHQI9

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: zA18JokCxbj2n8gjQxgB8eVaw2DvRyAHMM4z6GZQqY7jQDxRjnFNgahRQeh6hZ277L5hIczeXsPD

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 4gDW0NAQu+GeezKE0gQJw4SkVNAwNEKNdQiVCOMAImEYSgaBL5Xy27Vv710+cqTthaEHsZibchyv

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: d+/eESHka9Vyaf/YqBSd1K/vdRvsYiNLJMANAkina6BUsEEIBoqjEj3gFP0ZgGBMk++IRLVIBYwR

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: IJVdgaYRfyalVEKRgPvB0jg13ujSZefHexx77IsTzz9/u6TTBgwd2nvh0g9fAEvUhehHKz8XCkD6

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: AXAJT5o1NReXctk5IPieOhLQupQ6RKKEAJURoP/wC6U/X3/r2J+MGjzY25YxIeY46/rrG954bd6A

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ku31K9qlXRhlHRljdYSQGqWUKaUUhBC0mogTcLFLQkjAGAkIIU4gIzuKwnwoZdEAkqNh2FKXrm2L

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: x6wP99hzz1X779Pz/ZWUbsTM1Rdtls3gZ+BPTz50wZIPXgyEIYRlgeMHAJ4sMzvKAysRA0aIRtGt

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 6zdAPJna7Jvxwh1R9dFVk4fvcQGkseKhLaeYkq0dGxp+n7Lifzp1+PAPRg8b5mzLg+x22GHnbsy1

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 3cdTSRIxArIyTeX5AH5oJ5k5lHGezIbuFGJwWvXHqFydKNBKluDl87I+U3Plir/PH/d1LQsqc/Dg

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: wSk3Ht/FjmDvlWtWHqpM84RssdCJmkIwxkgURQS/e8vnVUligMSQjHPtGiIZ6GfHNOBToKJIqUiC

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ySiGayoIgkhKKTnnhbhptiWTyYXpeOIDTvl7CUNsgkC67TKZnO849hmnnZbbrNwRjY3xyU8+/nZo

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: mN1FIq5jXQoMosAHwpDlCCGZTEL33ffQie1sSyu0tLQgXwtU8DKIUWWfrX2fjPTPMOMSOQ6kYnGI

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: XB8szkLfcZcJoqbutftet7w0dWrxX1EsPtQO3+/zO1tFo6llQUi1sdAPKijZwBRMSxnWSM/1bo8Y

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: PU0JRnBsaJpVRX8cFHCcqO+3DjjggMOeve/hxV9LqaNGGWv++c8D1re2XsxMYz8vCrv5URhn3CAY

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: M2jcIrhWIDBa/Zy2GlAua9Lvqz8HIj+9YYAAYWUcgS4kZlqI6sGvMGqcc5XP5xUTNIhbViA4d4WU

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: OQtIwc3mN20ukJs4ZozToX2Hl0PflYHv6XAoioLyTRiD0A8gdDx4b+Ei2H3XbnoQqFi8qoOpDhQD

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ZxRZ+Te3TGgrFQAMDoUw4AGlexb84Jr3li555cBjjhnyr4RPU6dOxaimCy4k/V1oNRAQooIZazZN

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Y1JNMpmASHZjhBJW3dXomzU5I0GiC6NECcHnW3U7feVkwYQJE0TPow/vs+itBU8vWrlsdonK0wsQ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 9nQgSpCYQXCeyJQhyCM4PnRtaFlomRSKcCHiayXU1G6sMgd8hhjGocXD9+j+RMyCRCYNlDP9GQzl

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: HNeFvGuTWG2GkphpFpWfLIZOfXMpv3vBc3pxYapPqh8JUSyK/rZTQ/scmoJqDFhybJBBCOlEUgMP

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Rim88/bbcMABB2R79er1UWtLSykMQ4V/v6WSq2Y5xAsJhWQSSjLS6FqaHGjCIi5RPddkN43b58jD

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: r76ssfHrImsjDIOYVmjFlDFKQEYh7pSFIlRvqojWM847cYx5tQcqm0OF5hAVXL5PKCifN7R37y+1

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: IBMmTIgPGT78+zc/9ODt67O5h5rt0g/MuhpLxSwIOQOFNViIvimBUuCDrxR4UQQBAjZZvtC6oOuq

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Xvicqrlmg5vAqdBJjAgxA6GQiMUh35YDXP+UG8CFCbXt66G+Ywe9gIquDU7og6QEnMCXwjBXxWLx

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Cfvtt+/ln8r0SMJne8XcO4LQwzGbYsawnihE0l1TeTgQ5GtRkX+dNSuTzqQ3fK9nr8c+WL70JM5Y

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: O61UqAAsQoGw8oZ07RJYySQESoJCBkxFYAoBhAvi+P7ubmvLjU0zpg/Z57DDzj7lyCM/+ioV/gXT

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 5GEQGjwegwhDG1Fe/Wj6mYLXNr4xfxPvd2g/RulOqMyqKrWNwB2rtC3ESRcNLhYNGzbsczNBaFn2

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: HDhw7xvuu++itlJhOI+ZcSOZJDT0dZICmTF0SUIIrSgMMBUluuAOKoUCn/haCgQtWwUgkOpFKtau

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: HIYqjVdwwQLxe/f5vh9LxOP4RDds2ABrV6+Eom3r+xu4qPBzQSSpF7518slDh99/zZjFywhRn6pb

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: /u2FF27ikryXNGPasuZybcANsdm84EMUhqH/nUyliOt6eyxbsayLQch4Qdk7DEggwwhw51MNWBCN

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: KuBWTK/aWCKuFcxMC0qBB5hMB8sElkzQrOcetKmQe3zavDmjJzQ1Zb5MuSGlkRA8QvehINIuBMp8

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ssykMq/C0KHED9yBWGyOu1QrAD7BSsg900gBi+Sms088+XMrL8647LKOPQcfe+aGYn6yLegIUZNO

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: RJyTousBZwaEodRRAroDVbkwHMN54uVFZaqzWumBz4UqCuiU8TmRclQBruu4nuetMyxzYZcuXV/v

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: se/3Xt1r7x4fduu2u+OXXP7R+x+Stxb8A1YuXwGO50HMNBG/APECxZwgWwfGI3323HfkGdcMfB8q

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: oPBTyh06dKisz6T+2rx+Ha5+MA0BVWID410NChgF23OrTA8Lw/AYqeBCIORmpdRjKgi9CNEqxpmR

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: BAgizfGGtg3VxIQfBmAlU9r3eCpC3wHJdu2YFOLANS0tN9x4222XT548OfF5Dxyl1nFCw7BsBBra

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 1EaykpNl6+o71rzVu7bWBEpPiJSkEUETWEbyaBQR0CCQYpEGVC9CLrdia7sVOfNnX5x1/wdrVj8Q

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: xkSfogxEgMl/w9AmEdOJCOBQYRge4iNFAIk71Khw8/iAGbp2Vb5Q+/hc0NXh5fv+RkLoczXpmqsa

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: GtpfbhrGtDXr1wfvLlrYe9GiRXsu+WhpzbJly2LFYoFqnwxlq4dgUNoeMNfP1/P4r08/6ZQLX3li

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 2htHkCM206efMssYBpxyyYh3l69e3SYI1EZAwQ08zJho1IZjQ8WgOdBgQGoFkyAI2glGzzPN5Fiu

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: YL3v+eeSULbjnJMIJARBCJYZA64o+KEEysuhElZ+IBkiEgnIew4gnwCMmoFjX3LzIw87J1500cSn

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: x49v2Zpy33vvvTAMghZM2yE1alkm2MWSisdi81JmTc7N579nl0r1VialF1DIoByuISnDOGBanvrB

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: +p5779v0WTeAgGmf/of3afbzl4YmG2TELBZhWIrWS5vRMqLVr1El5OMcOKuwXti1EFVZMgokiEBQ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: VC0JozBypIwKgvIV8VRiDhPmOwSiyPa805ubN/WKomgXxhgzLUvDUj8IgFsG5At5SCaSEOcC3EIJ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: OKVS+tHCvbt1Hzflzjuf6Natm/vZZ/R/2kmOPHvEyq6dOz1u53Jh4Dk6v6v9FH4VLUPzMigpw/dQ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: SSxRIaWSfVQpm7s/bsWLdZmaKzmjr/ieF6Gp5PjHfgilfAFMyvXqdorFsp+hBIIoAlxuUjAQiRiJ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: DFazoS3b+PbixfeOb2pKbk25qJDdunVdYheKilXQfGC7eUHYszeeeaazYtmygbFkIo6+kJoCOCbj

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: fRfiVgxU4IN0fTsdi//2hEMOeXXL+2LLyVNz/n7m+mLLMy6Rw0gsxpHQrAIgnePGpEPlPQouMNyR

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: nutCgOSOBDAxhyz1boDI8QISyRcSpjWmvrbuonZ19dekksl5VJE+ge+NzReLD/qe92NCyG5cCMF4

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: OSZH96c4hYLnQKpdBlzf0fiFSxm4udzzA77f9+zXnnnm4a0pdqvKPb9Pn4ADf1QosjpuCAg8t4x8

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: K6GNqihk8wXleI6UEUFnP/BHEUpziVjipyYXf5Z+gHQMMKWgXSajTQWaJcu0tI9E/6StBhIMjAGW

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: yGCKUXJurG1r/fGEx/7489ubmmJbG3yH9g3vCk5DjqFF6EMmlVofM+OvXz1pUlyGsmfRdRgCnmoG

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: CL/Ptx2wCIcENz8YfurpD4waNWozI9U4YUL8ylt+e+ncBfN/Y2TS7cCySKD9Kfpn0H5S+8rKYkfQ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: hA8frRnjXNdEJwwLCC7Wkh2B7TazIHymobZmcDKRuChwo5dz2dyB2ZbWG3PZ7OWFYvFox7Z3Cfww

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: iTfFEKeaaq0uIPwuYjHwy8sfDMqUSdm039962/kzHnjgna09l89VLsox++//Zl0mNU36oYphCk2V

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: QQOtfKDKrlR3HvpgLgSuYOL6XkOhVLw1UmqQaVk3cc4fZwpshPZ2rgAcTZkfgu96yEPrwLyadtNx

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: XRhoWI8+jVmGWLz0w1/efufvTsPCgM+OM24mlpqMt2FaD3dk4Idv9u7f/2NZjDJu6O+m0SqjGuEj

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: u1aTTEHKjIGbL/oNqfS0xlGj8psVO25czR9+f88VLpVjzFSifc5xiBMEgN0JRBLtT7cERfh+y+eA

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 32EXipHvullO2NxEPHFbOpH4WW0y2RhE0a6e447xI++ZIAgui5TchVLKkb3S5hwVyj7JxqFiyxtK

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ldOvWFVSstEKOEGh8OJhffvccM6Pf7zqixT7ucodN26cc0CvA+8WBDbgTemWE1LlUAevKo2Gg8Od

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 4csKIwNqj6JTGu8F3klJxkYZQlwdF2Y2bpgK0XTMsqA2mYbWjc0aeHE0P5UUHIZfGKxjkR41BFjJ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: REPetW96Z9OaPT47zpqMt9EwjLmmMJRTssPdd97lsaljxgSt+Q3tiZK7Ec4IhiGpRByEUlBszUJY

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: cqKkYT15YO/e929W7F13pf/Y9HhjZNCrHCnjkSHAiMeAM6HjzfJGJRoQ4fwRZRNEyWEZMCLhT6Vc

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: wTm/wkqZfRPJ5E8NIWYGUg4ret6ztuff7Uby9AhIOyoMgnNEYgfnh34caDXFiopVm1k+JDIQrjEv

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: VMzxcj26drvkwYfGn/zU+Pu+EpP2uS2cgtLmpBWfR4FI3G3oN7Xv3EKxm/ljVC5WQUi0XQQpIhKC

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: irkyGm4Lej5IOd2KWSMLudzLoJTv2Q44tg0GKyNNVLjOIyuokA2fsFz4VBWjHea+Pn/sKRdeuM+W

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Y2wP7W3LNBcIykKqoNihY8eluNRrUqm0GTNTuA6RBi20ZSHGDUgKC3zPeX9Av35jHho7dj3e439u

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: vTV1z6SJozcV8z/zKIk7WOnMMcFAwPdDMAyrPKeKoEvBiyIwjFTRiODtGGHXJ2LmsEQ8OSXO4ntF

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: YXBNvlR6OG+Xzih4TmcQzJKcUowOsIDPCXzwKhcSECho4nX16RbWQKPjIFQyV1jXMZa646ZzL5gy

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: rM+g3FdR7Bcqd+o99xSplLdJL1ixOaxBknvLeK2ym3WKEPOpnG32FRzpN8Hbu0Fwve05//OLa897

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: tuseewy17dLdBKRHZAQ1mQwErqcXi84woQ/GMEoqsLDITcqyTxYMgczxf531wh+vGDt2c4iEoCpS

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 8JaSMmcKA3eOPr8im8+3LxVLFj6wdDoNNekMuCVblXLZ5Uf2P+ziqffcsxTK4Q57df4rp0SCXs7T

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 8ZQNEaQa6qDkOZp4wNShXSyVwVRlIauyYm0B9NkU4/1imcyAVKb2D8RRHQotG+c0t2yYWvBKI8Ek

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: XVjaIjyTADv0tVVD/4/uprpr8T2QT5RadU9QwTESFesGH40cesZxq/7+xo1Dhgz5WvVdX9h8PeSU

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 09+qTSWnRwHWzkWYPdicKkMF6xWmQ6QymtQIusKhVuqTCOOcF3132FNTZp25n2Xljzl04E0mYfcZ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: QF2nkIeYZeh7VbnhzYDC87XiU8mMDpkiQqhIJfZ/Z/78g7ccY9ISy4J8Pmtxvn7ooEEaNWZqa+MY

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: oiVMAwotLaA8D9y2bFtDuuYX1/7853/fPL9zzz7o9YWLrvYoSeZ8bKeJQTZf1OgXF63vOYCgkmmy

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: QwELJVZ2bBBArzGkef6Gzp3/yUxT+S25q6IoeEAIszsXpol5MTeMSIA0Lsa8VkyTQVAJJXHHYnUp

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: WiedAapwyvrZYvwd4oWVJLJgKTW+azr9ISHka5f/fKFy7x41yuu37/63W0IstBLW5hIcgwkdx0U4

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: eKkAY0DkVKuX3Hxzqak+nkk0vLbwrbtf+HjZo/v06FEzZPDxV3apbz88yOUXqMAPMNWFRD7WP+Hk

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: VSghJpCBMUAFEUQhASIs8AxOF65ecWnjvfe2r47x+vMuXrZrfafnVd5Z2qFnT51CLDqlGp3e8wKw

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: pJKqZM8+4Zhjj1y1YMH0Pn36aHg+6JRTjpn7+oLp8YZ2uwUYKiUS4AdlUkIQBr5tQxLDQLQcRcev

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: IWJmO9O6oF+P3gftn0qMT2REx5plKyZ5zS0fuFReEQnWoDgniPgVpSCYAE4MYCHFUAgSwtQWCUM2

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: zg3dwY99wrghMPkSBL6OJJCwZJ4b1Qlj5olHH3VM61vv3Dl69Oh/KTX6pccm/OCCCzYkDPFktqXV

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: re6uYiVGxf2LA93yVpsRdRV84XvBESjxUIbHP/b4Yxf26tGDvD1zZtPoyy7/mXT9vwkCMs65Cn1P

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: m0LkaLP5PFhmHDw30CQIUpYBo6TFzh8x7bmnj6uODznh/fbc48GEYPOP22MPrThLmDEZBTTIF3zq

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: erOOOfSIC564++7NYcNxl1xivrfkw595APWYCtS4SJZTbLggEER2qG2HqFqGttsSF9b9h/bpf945

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Rx89CWKQ+yhUQ9ev3/gQs4yz7NDfOaSUBoTqBAkudLyT2uLR4oLBNClKKpXSJhfNPs5TL8ZCHjKJ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: OISeq0qtraFFyAfHH3v0FX/8ze/mwzZUrnxp98CMiRPlD44/4a3mXAsTnPcu2SUjnkppc4ylLZjn

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: RQxUnQ5TW1BtFVrcc8pVHJZlcdf3+7355lv7rGlre/W3o0YtveXu8bPn/e0lo5DN7Z5MpuKOa2tT

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Fk8mQKL/VpH236VSCeKIpAkxshtbeoz+xeWPznvxRW2G3/7HPzYo33//4IMP1qYrVlfX03OddMK0

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: xtXWmr+eM3X65rBh8vPPJ56YPPkOj6pTsIXeQ5bNNLXZNRQFGkR6l9m5wrq4ZU1NJ8SF62PxyW6h

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Vc19990ha9avvyFvly6z0onOIUgSVlOIGGvT6qv65CKYjGdgIKunInB9V783TUNHCFgzbXEBFmIO

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: L2j9/n77/fKMYade85vLrtjm4vivdOAJgqv99/re7eD5f4qZlsR6KgRNtl0sk+KyzJ/yLTnULSRh

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: WnoC+UIJXBmSDdmWHz721FOnNTY1GaOHDVtz6YgLrqkVsclRyY0wzMAcsu06UPRsDTxc3aiV0P5Q

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: cBOR5c7F0Nuv+g1YW7QlGdGrR4/pXes7nL7mjX+M/2D2gk/Rlzf/7pY+G+38aS5TVsjKmCGyPRCh

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: AiNUkBaWCkrO6r69+5zzyIQJl64+6rhFHQzDbG1rvXLTpk33gmDH0ZhhlTxXJwXQlUAF6UIl6f6p

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: kLHiZzGSwEWgQRMueNuBwHGhNp6AOKGqec2afMf6dndccMKP773q3HPXbqti4eueQ/W9QYMGrm7b

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 9EjOs3c2kimIJZK6LVLXCKsyg1MVHTURqJD0WOzggZGMa8gfBR4EJcdrH0vddP5ZZz1w7ciRa+6e

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: MqXTDbfcepMdBceLTLI+Mlg5T6kImFSA57jlqo8oAkvJ0AzldateX3Dr1ymLuWLsFYkHnph1Z1vo

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: nRPLpAj6PMxcWUDBy5egNpEqha43+4iBR9w15c7bZukO/aOOOmBDIXdyKfAvS6RTMbQgVjymTarr

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: e5tjfaiSO9Uqiy1GhSATSQ6kcnXmKoz0gpdeAEGpJAWoFft/r8eNv772+icH7L134evo5IvkazV1

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 3XDVVWvnv7f444iQfslkItWWzRKkETFtRcrNvrA5c1pRrv6BlGDoSkpfh03YyxP5Pncdt9/iRYvr

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: Ro8477mrLrkkd92v73pp3fpV7zW3NA/0Aj8REkXwbzH9iiE0mjVdwuP7JCaM4l577f38Yw8+uFVe

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 9bOCDNfkZ58/y6bRxejMMeZEUCgIBREoSBuxdTvV1v/PPbf99pYrz/0Zdvrxky699MA169Y+pIQY

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: IgW1ENUizYjhGc4D8Ue1XLY6d81kKag8j/Irfoay8o7VgEr7dk+5+YKXNMWMvbrscvXLTdOe7drQ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: sE3FeZ+Vr3UO1fnnnx/86szhf6oxzd9sXLrcrk+ngERlWh15T+2D8cJQqNJnhIpG0h7hvylMXQOE

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: qS4jFgeRjFlZzzn5kRdnn3fLhAmZq88flps2/pk/xxT8Lk7ppgQmv4NyHIymz4zHIMCwgXFS8oO9

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 7/j973f6qmNfvGZjr035wq/cUNYRo8yoGQlqgqgAAAvdSURBVJRDnIrIK9rvZJKp6/5w45jJR/ft

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 2/LCu+/Gzx0z5vJVK1c9rJTqGQSBERcWCGC6qSwZT4IKJKTjKfBsr0JPli+qyBYp+PJuRqCIqFui

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 9VIKTEJUUCx81KWh/a9Hjxj5s79P//Pcr1u2+lXka7djzpgxQ47+1f8u/nDpkjCfy+5HGY1T7XMq

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: F60kFyo7F6cY4W4VWNjlA6MMglD35kCEq1wws1QsDtywYVNp7ZIPXrnzzuvDGa+99ua6j1b8Y9Wa

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: NQcSCQ1o+BhyxBgmaQKMQeh58Q7pzEsr33//gy8b812PPJK+54H7xiba1x0SMU3gYk0pmJIou7Xt

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 9ctHXvbzi8bfMat/587hMWeemXj4iSnX5kqlq/0o3MlKJAm2g0a6FIZpK4RECxYLIi7AnQvqExtc

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: bZ6rXmWyR4GFLaVAwc7nsrVW/MHDBxx8yaiTT3nu/LPP/rc1nv3LfT1jx45NjH304Qtdom7k8biB

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: LEyEhV7IwvByzyuucLfCTbNKNYKu1NC2qpJZQnYqDMGIYMmAA3sP/tPEiR9B5WzGbgP6jmjOFe4Q

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: qYwZYXGd52gAg6U0ZqRk7927X/33qVPHftlYd+vfv08h8ppsGXSTFeKAYcFw0X13n932GD3/mWd0

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: JfrQoUONl1auvM6OvFGUsbSnD0pJgeeWqymEpiKlzrFieIeMHFZF6DEpJGDKjd5+pfpCKx4XheNC

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 0jCiXHPr8s47NUw49MgfTHqosfHffhrsv3w84JVXXlm69pwRd8UpvynbvHE10oUWUmoVgIEPEPO3

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: GONVRfPRmJ/TZTusnB/Faoa4hVVqu788f/4TR5955g+ggoBHn3LGY52SNfdJ183b+TwkYpa2AAjK

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: mCHo8lWr9vyyceIiISb7Ud61O+HRCBjmUCeQMclm7t1l11Nemz79Zfy7u5qaGuasWTXeVcHlThSk

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: AwoQT6d0KRCGYrFkCnJ2EXxZ5tBRsRiLG5ZZLnBTUvtTBJdYecIpAd9xMbGQSwr+QoqJK04a8qOj

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: n7j97ju/CcXC9ji19a6ZM83npk8dMmfB/EkRFxnNl1Y6EVSg9FkTSCXKT5mrSrqwwtXihVUYGBrE

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: GFtw89XXDrlo2DBN7N9++6S6cVMm3O1Z7NR8GBFsRgPGIMkMECX/L5eeeMIPv6ig7vIbb+x6+8P3

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: v5GoyzQg4+UWS0ra7sJfXXnV2b8aMUKfEodga9ILf7myEAbX+1SZmE9GRGzni5o2dMNyWh7dAS4u

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: UglxsHgQARQWD8ZNU79iiIP0pe85PgX+Tm0iMXHgUUc1PTJmTOGb7tjf5oM9sf1i5h8eeNoC+kuL

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: kEUxwrygUISMGdflLDplWOGiUaJq96CsFNExVuZyhaHLXnk83uPWceN+umDBAk3fjB7989afnnji

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: PYVNzZuwQyARj5ePQVB64TR80RzwHjNmzTorXVtTjzuqlMurJDM+GHjQwedfc955mrHCyoum2bPP

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: bm5tGc0EN6udAYVcvgII0ckT4DEBShBwijlQtFzTXe5aQM45UjyKQub6LTEF7xme9/ROiczp55xw

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: wtGr5sx54NHGxvy3cRTDdjm1FRuXb57w0KR+++x7SkLR2xqsZMFty+ocqoF+NSqXs6IyuQ7iQSf/

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: qyEEFl5jvIwNZ8BZLOuWLrvmjjsOr95/cL9+8zvUZBqjYqkFC8NS8QSUCkUo2bYJXbt+7kEsD8+Y

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 0aHg2oNUGGEtD8Q5b96prt0NMx95ZH71Yf9x1qzua1o2/TJTV9uAY0BMgH+LKUKMr7F2GJkkRLxI

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 6mfq6jTvDBirF/LgtbX67RPxebTo/O+u7evP6Nau/uTxt407a9mcOU/dXVbqt3YQ93ZvlJ49ezb/

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: xS03Dft47fqzuWn2VpzVISuFfhILx6ptFTprVAlx0CzrszhkCMVsDhKUQX0y/eaJxw4+/bZrrnkf

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: /x77fc+85sqL3vpw6S2punYGxpLcDRf98vQrDxo9eus9R4cNG3bY0pUrnwRBGvySvX6vXXe9buIN

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: N0/ed999fSQorvvtb/d8dPq0u/KeN8iRIUkm0+B4LjAqyqnGcvpNKgqhEMwDKb3Q89pkJNepIFjX

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: uaHDG/vs3X1u9z33XTjuXyT3/52y3Y8fOuKII/DglMev+c1vZk2bNeuQZjt3MaX0UF9FBgl9IiQD

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: xoQO+aNquMSo7oXBmBl7igxCoLmQ6/XIjGkX3nXXXVcitYgKGdHYOOHj9RuGRVL19fDEO+AB5+s/

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: d2e0FYt9/DCoFYoW2tWkr68qFn938x137PTUn2eMb85nBxLDJJh082xHeY4jiaKY7Mru3LnLeqeQ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: ezVG6eJONbXLWnK5Urq+LvuLyy/flKyryx7fu/d2bUnd3vJvOVuqYoo2AcD0S+666y9PNj16tnSD

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: EdIP9gXGTCKAYKU8Nm9FlTgYfR0lvAxQGMP2E+5LOWTBunXXYxsw3ndiY6PduX/fJ1py+QNMYZjJ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: mNnc2tq61XbQEY0j4rNeev8MyxC8lG175aGJk6ZVFYvy0rx5Ry35cEnv2p3at0agVlsKVqcsa0l9

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: PLnEMoylQ08+eTFs2JBtHDMGg3K1JYt/6vPP/3u1sp3kGzm/Ak1108svd5n/6qt9bN870vXsg1qL

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: hQ6EixpqCCMiiklUbaWVERGpznf6Qdi5rv7cRc8++8fqDul7/PHdl61aMZEADKxLZn7/z3nzLt3a

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 7hl40glHvL9y5XSqYNNO8dQ5P5ozZ25jxf9hU/S9j00+vKWtEE/VpdpMxdddfPHF2VrGcmh5voln

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 8k3IN37MUKWKkfIOHTo88fST38/Zpf1c3zsgUNDVC4POQpgxx3EMLN9FOGNROnfsL2849cyfHLuu

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: eo9Bpw/r//7i92fUJtNnvTtv3rOf/Q5U3tgH77t1+erVZ3Vo1+6sxbPnPr8tedEdVb7xI/8qMSle

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: eP7Emialnu24cmH6/nsfyKxesb62JZ9PJSyrXT6b38l33Q5uoRCLm+Gn6pa7d+/xzqolyx494IAe

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 7707b97/+Y4/zZ6d3rhmbaeaZOqJnRs6vr74v1Cx33lBRLu13l382e8mTKjHAretzeHtt9/u3OuQ

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: vhciwfLf/gz/4wR9/Jc1kv03yDb7XPShPXr0IO8BsI5tbaq2tlZit+B/6v/pZEeSL1Qumr+XVqww

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: X54xo27ua6/VOGGYAA61MW50/2D5R3sKxtt5UVgvwzARoYnUh1IwPJ8h70ZBThCytnPnndd4jvth

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: fW1tq18seBBA8cZf/SpbTKXyg7t3367J6a0Jkh9hGIogCBR0AoC1AIjM2vm+MnbdlcDatdAixObn

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: gAliJLUNzokfhqqaMNaf30L8Dh22eL8KGtwG+dYee4TDCPlax/kuWLAgnkwmWZaxzWMIsUTkM1Ib

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: RQpPMNjyp+u3GPNnBTsst6pcVOroceNqm5qmnOBT6B9FsGfMsjq7QVhr28UYZUIYJme6+D4KdNjC

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: DIZsBDhuSVdFmhaeWRWpSAbSMowwCvzAZGbJ5LQlsL3VnPHlFmeLjjjiqFmFXr2WTv2CzvZtkcHD

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: fjLszUXv/cSMWxKPMJAqUoZhKddxpBsFhDFBDMGp73t4coZ+HliArvlwzvEEGd1FSLD2Flk23btD

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: kTvVZw3p4yZASRXJsEunTvN+MfP5h7+qgmfPnm1dds0vb3b8oHMESmAMFip9pg0WPejuJPweQ1vB

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: SJcCRPoEhEp7YXkESm9S7GbUw6Ggz0Fimg/81CoSdzY17dFl4CEjck7pcNt39zZjCRM7s/C4g1Cf

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: zmJAMp0B2ynpu2LJaYgdCZgJwiMBhAGCG+D4LlDOiRAGiwCYH4WmJJDE1hCPqH1U6CkHRPT4M9M3

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: xZ5/7oWDTj7xKVJy57w2c+Z2y57gIj1gQN+uIcgTCSU8Ukyf+4gneaHJwPMssfjOx6MHRPxTGtEP

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: UVGlsJgcz2bGA9j0MQtY7xPqY44y6TTJZ7MKO9wj34cVHy8zks8992jl418qmzZtCop2qU/J8w8W

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: MUtEWPQgmPLDSHc64vkKOmesVVrujy6f5YGH5ZYPS0Ep/78ZCC45ffSS5ByUDOH/AcRNdcEflr82

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: AAAAAElFTkSuQmCC

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: --b2=_q1dGnEEa7M68cmQQgMbydhSB5RgWQYURawtofjTaM3Q--

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: --b1=_q1dGnEEa7M68cmQQgMbydhSB5RgWQYURawtofjTaM3Q--

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:16:09] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbyJ3SqHz5Z5ml

[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:16:09] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:16:09] Email sent <NAME_EMAIL>
[2025-07-19 08:16:09] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:16:10] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

[2025-07-19 08:16:10] Global sendEmail called for Admin <<EMAIL>>
[2025-07-19 08:16:10] Initial email body length: 1859 characters
[2025-07-19 08:16:10] Email type: Regular
[2025-07-19 08:16:10] SMTP settings: Host=smtp.hostinger.com, Port=465, Username=<EMAIL>, Secure=ssl
[2025-07-19 08:16:10] Set Reply-To address: <EMAIL>
[2025-07-19 08:16:10] Set custom headers and Message-ID for better deliverability
[2025-07-19 08:16:10] Set HTML body (1859 chars) and plain text alternative (984 chars)
[2025-07-19 08:16:10] Attempting to send email to: <EMAIL> with subject: Birthday Email Report: 4 sent, 0 failed
[2025-07-19 08:16:10] Email content validation: Body length=1859, HTML=Yes
[2025-07-19 08:16:10] SMTP Debug [2]: SERVER -> CLIENT: 220 ESMTP smtp.hostinger.com

[2025-07-19 08:16:10] SMTP Debug [1]: CLIENT -> SERVER: EHLO localhost

[2025-07-19 08:16:11] SMTP Debug [2]: SERVER -> CLIENT: 250-smtp.hostinger.com
250-PIPELINING
250-SIZE 48811212
250-ETRN
250-AUTH PLAIN LOGIN
250-ENHANCEDSTATUSCODES
250-8BITMIME
250-DSN
250 CHUNKING

[2025-07-19 08:16:11] SMTP Debug [1]: CLIENT -> SERVER: AUTH LOGIN

[2025-07-19 08:16:11] SMTP Debug [2]: SERVER -> CLIENT: 334 VXNlcm5hbWU6

[2025-07-19 08:16:11] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:16:11] SMTP Debug [2]: SERVER -> CLIENT: 334 UGFzc3dvcmQ6

[2025-07-19 08:16:11] SMTP Debug [1]: CLIENT -> SERVER: [credentials hidden]
[2025-07-19 08:16:11] SMTP Debug [2]: SERVER -> CLIENT: 235 2.7.0 Authentication successful

[2025-07-19 08:16:11] SMTP Debug [1]: CLIENT -> SERVER: MAIL FROM:<<EMAIL>>

[2025-07-19 08:16:11] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.0 Ok

[2025-07-19 08:16:11] SMTP Debug [1]: CLIENT -> SERVER: RCPT TO:<<EMAIL>>

[2025-07-19 08:16:12] SMTP Debug [2]: SERVER -> CLIENT: 250 2.1.5 Ok

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: DATA

[2025-07-19 08:16:12] SMTP Debug [2]: SERVER -> CLIENT: 354 End data with <CR><LF>.<CR><LF>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: Date: Sat, 19 Jul 2025 08:16:10 +0200

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: To: Admin <<EMAIL>>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: From: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: Reply-To: Freedom Assembly Church <<EMAIL>>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: Subject: Birthday Email Report: 4 sent, 0 failed

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: Message-ID: <<EMAIL>>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: PHPMailer 6.9.3 (https://github.com/PHPMailer/PHPMailer)

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: X-Mailer: Church Management System

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: X-Priority: 3

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: MIME-Version: 1.0

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: multipart/alternative;

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:  boundary="b1=_PfWdf3JLg0LvtwlHK36sp1dOGvql2EI9rcefKTV6Rn8"

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: Content-Transfer-Encoding: 8bit

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: --b1=_PfWdf3JLg0LvtwlHK36sp1dOGvql2EI9rcefKTV6Rn8

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/plain; charset=us-ascii

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; } table { border-collapse: collapse; width: 100%; margin-bottom: 20px; } th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } th { background-color: #f2f2f2; font-weight: bold; } tr:nth-child(even) { background-color: #f9f9f9; } h2, h3 { color: #333; margin-top: 20px; } .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; } .success { color: #28a745; } .error { color: #dc3545; } Birthday Email Report This is an automated report of birthday emails sent by the system. Total Sent: 4 Total Failed: 0 Date/Time: 2025-07-19 08:16:10 Sent Emails: <EMAIL> <EMAIL> <EMAIL> <EMAIL> Templatebirthday_notificationFailed Emails: 0No emails failed to send.

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: --b1=_PfWdf3JLg0LvtwlHK36sp1dOGvql2EI9rcefKTV6Rn8

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: Content-Type: text/html; charset=us-ascii

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: <!DOCTYPE html>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:         <html>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:         <head>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:             <style>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 th { background-color: #f2f2f2; font-weight: bold; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 tr:nth-child(even) { background-color: #f9f9f9; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 h2, h3 { color: #333; margin-top: 20px; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 .summary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 .success { color: #28a745; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 .error { color: #dc3545; }

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:             </style>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:         </head>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:         <body>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:             <h2>Birthday Email Report</h2>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:             <div class="summary">

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 <p>This is an automated report of birthday emails sent by the system.</p>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Sent:</strong> <span class="success">4</span></p>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Total Failed:</strong> <span class="error">0</span></p>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:                 <p><strong>Date/Time:</strong> 2025-07-19 08:16:10</p>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER:             </div><h3>Sent Emails: 4</h3><table border="1" cellpadding="5" style="border-collapse: collapse;"><tr><th>Member</th><th>Email</th><th>Template</th><th>Type</th></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_reminder</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr><tr><td>Unknown</td><td><EMAIL></td><td>Unknown Template</td><td>birthday_notification</td></tr></table><h3>Failed Emails: 0</h3><p>No emails failed to send.</p></body></html>

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: --b1=_PfWdf3JLg0LvtwlHK36sp1dOGvql2EI9rcefKTV6Rn8--

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: 

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: .

[2025-07-19 08:16:12] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok: queued as 4bkbyM2vt1z5ZBhQ

[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: RSET

[2025-07-19 08:16:12] SMTP Debug [2]: SERVER -> CLIENT: 250 2.0.0 Ok

[2025-07-19 08:16:12] Email sent <NAME_EMAIL>
[2025-07-19 08:16:12] SMTP Debug [1]: CLIENT -> SERVER: QUIT

[2025-07-19 08:16:13] SMTP Debug [2]: SERVER -> CLIENT: 221 2.0.0 Bye

