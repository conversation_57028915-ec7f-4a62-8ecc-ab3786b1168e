<?php
/**
 * Check Upcoming Birthday Notification Templates
 * This script will examine the current notification templates and their image handling
 */

require_once 'config.php';

echo "<h1>🔍 Upcoming Birthday Notification Templates Analysis</h1>";

try {
    // Get all notification-related templates
    $stmt = $pdo->prepare("
        SELECT id, template_name, template_category, subject, content, is_birthday_template
        FROM email_templates 
        WHERE template_name LIKE '%upcoming%' 
           OR template_name LIKE '%notification%' 
           OR template_category LIKE '%notification%'
           OR template_name LIKE '%Member%Birthday%'
        ORDER BY template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>📋 Found " . count($templates) . " Notification Templates</h2>";
    
    if (empty($templates)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ No Notification Templates Found</h3>";
        echo "<p>Let me check for all birthday-related templates...</p>";
        echo "</div>";
        
        // Check for all birthday templates
        $stmt = $pdo->prepare("
            SELECT id, template_name, template_category, subject, content, is_birthday_template
            FROM email_templates 
            WHERE is_birthday_template = 1 OR template_name LIKE '%birthday%'
            ORDER BY template_name
        ");
        $stmt->execute();
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>📋 All Birthday Templates (" . count($templates) . " found):</h3>";
    }
    
    foreach ($templates as $template) {
        echo "<div style='border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px;'>";
        echo "<h3>📧 Template: " . htmlspecialchars($template['template_name']) . "</h3>";
        echo "<p><strong>ID:</strong> " . $template['id'] . "</p>";
        echo "<p><strong>Category:</strong> " . htmlspecialchars($template['template_category'] ?? 'None') . "</p>";
        echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>";
        echo "<p><strong>Is Birthday Template:</strong> " . ($template['is_birthday_template'] ? 'Yes' : 'No') . "</p>";
        
        // Analyze image placeholders in content
        $content = $template['content'];
        $imagePatterns = [
            'birthday_member_image_url',
            'birthday_member_photo_url', 
            'member_image_url',
            'member_image',
            'birthday_member_image'
        ];
        
        echo "<h4>🖼️ Image Placeholder Analysis:</h4>";
        $hasImages = false;
        foreach ($imagePatterns as $pattern) {
            if (strpos($content, '{' . $pattern . '}') !== false) {
                echo "<span style='color: green;'>✅ {" . $pattern . "}</span><br>";
                $hasImages = true;
            }
        }
        
        if (!$hasImages) {
            echo "<span style='color: orange;'>⚠️ No image placeholders found</span>";
        }
        
        // Check for img tags
        if (preg_match_all('/<img[^>]*src="([^"]*)"[^>]*>/i', $content, $matches)) {
            echo "<h4>🏷️ Image Tags Found:</h4>";
            foreach ($matches[1] as $src) {
                echo "<code>" . htmlspecialchars($src) . "</code><br>";
            }
        }
        
        // Show a snippet of the content
        echo "<h4>📄 Content Preview:</h4>";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>";
        echo "<pre>" . htmlspecialchars(substr($content, 0, 500)) . (strlen($content) > 500 ? '...' : '') . "</pre>";
        echo "</div>";
        
        echo "</div>";
    }
    
    // Check automated email settings for notifications
    echo "<h2>⚙️ Automated Email Settings</h2>";
    $stmt = $pdo->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'notification'");
    $stmt->execute();
    $notificationSettings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($notificationSettings) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Notification Settings Found</h3>";
        echo "<p><strong>Days Before:</strong> " . $notificationSettings['days_before'] . "</p>";
        echo "<p><strong>Template IDs:</strong> " . htmlspecialchars($notificationSettings['template_ids']) . "</p>";
        echo "<p><strong>Is Active:</strong> " . ($notificationSettings['is_active'] ? 'Yes' : 'No') . "</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Notification Settings Found</h3>";
        echo "<p>The automated notification system may not be configured.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<h2>🎯 Next Steps</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<p>Based on the templates found, I will:</p>";
echo "<ol>";
echo "<li>Identify which templates are used for upcoming birthday notifications</li>";
echo "<li>Check if they use the same sendEmail() function (which we've already fixed)</li>";
echo "<li>Verify that member images are being processed correctly</li>";
echo "<li>Apply any additional fixes needed for image embedding</li>";
echo "</ol>";
echo "</div>";
?>
