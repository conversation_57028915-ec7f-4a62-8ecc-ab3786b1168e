<?php
require_once 'config.php';

echo "🔧 Precise Fix for Duplicate Member Names\n";
echo "=========================================\n\n";

try {
    // Fix Template 58 - Clean Birthday Notification
    echo "🎯 Fixing Template 58 (Clean Birthday Notification)...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 58");
    $stmt->execute();
    $template58 = $stmt->fetch();
    
    if ($template58) {
        $content = $template58['content'];
        
        echo "📄 Original content around member image:\n";
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 400);
            echo $preview . "\n\n";
        }
        
        // More precise replacement - remove the h3 and h2 tags after {member_image}
        $content = preg_replace(
            '/(\{member_image\})\s*<h3[^>]*>\{birthday_member_full_name\}<\/h3>\s*<h2[^>]*>🎉\s*\{birthday_member_name\}\s*🎉<\/h2>/',
            '$1',
            $content
        );
        
        // Also remove any standalone h3 or h2 with member names right after the image
        $content = preg_replace(
            '/(\{member_image\})\s*<h3[^>]*>\{birthday_member_[^}]+\}<\/h3>/',
            '$1',
            $content
        );
        
        $content = preg_replace(
            '/(\{member_image\})\s*<h2[^>]*>🎉[^}]*\{birthday_member_[^}]+\}[^<]*🎉<\/h2>/',
            '$1',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 58");
        $stmt->execute([$content]);
        
        echo "✅ Updated Template 58\n";
        
        // Show the result
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 200);
            echo "📄 After fix:\n" . $preview . "\n\n";
        }
    }
    
    // Fix Template 37 - Member Upcoming Birthday Notification 1
    echo "🎯 Fixing Template 37 (Member Upcoming Birthday Notification 1)...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 37");
    $stmt->execute();
    $template37 = $stmt->fetch();
    
    if ($template37) {
        $content = $template37['content'];
        
        echo "📄 Original content around member image:\n";
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 300);
            echo $preview . "\n\n";
        }
        
        // Remove the member-name div after {member_image}
        $content = preg_replace(
            '/(\{member_image\})\s*<div class="member-name">🎉[^}]*\{birthday_member_full_name\}[^<]*🎉<\/div>/',
            '$1',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 37");
        $stmt->execute([$content]);
        
        echo "✅ Updated Template 37\n";
        
        // Show the result
        $pos = strpos($content, '{member_image}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 200);
            echo "📄 After fix:\n" . $preview . "\n\n";
        }
    }
    
    // Fix Template 47 - Member Upcoming Birthday Notification 3
    echo "🎯 Fixing Template 47 (Member Upcoming Birthday Notification 3)...\n";
    
    $stmt = $pdo->prepare("SELECT content FROM email_templates WHERE id = 47");
    $stmt->execute();
    $template47 = $stmt->fetch();
    
    if ($template47) {
        $content = $template47['content'];
        
        echo "📄 Original content around member image:\n";
        $pos = strpos($content, '{birthday_member_image_url}');
        if ($pos !== false) {
            $preview = substr($content, $pos, 300);
            echo $preview . "\n\n";
        }
        
        // Remove the member-name div after the image
        $content = preg_replace(
            '/<div class="member-name">\{birthday_member_first_name\}<\/div>/',
            '',
            $content
        );
        
        // Update the template
        $stmt = $pdo->prepare("UPDATE email_templates SET content = ? WHERE id = 47");
        $stmt->execute([$content]);
        
        echo "✅ Updated Template 47\n";
    }
    
    echo "\n🎉 All templates fixed!\n";
    echo "📧 Email notifications will now show only the member image without duplicate names.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
