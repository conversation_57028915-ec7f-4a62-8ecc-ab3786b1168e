<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🎯 Testing Fixed Template Selection\n";
echo "===================================\n\n";

// Create BirthdayReminder instance
$birthdayReminder = new BirthdayReminder($pdo);

// Test the new getBirthdayNotificationTemplate method using reflection
$reflection = new ReflectionClass($birthdayReminder);
$method = $reflection->getMethod('getBirthdayNotificationTemplate');
$method->setAccessible(true);

echo "🔍 Testing getBirthdayNotificationTemplate() method:\n";
echo "====================================================\n";

for ($i = 1; $i <= 5; $i++) {
    echo "Test $i: ";
    $template = $method->invoke($birthdayReminder);
    
    if ($template) {
        $hasPlaceholder = strpos($template['content'], '{member_image}') !== false;
        echo "✅ ID: " . $template['id'] . " - " . $template['template_name'] . 
             " (Has {member_image}: " . ($hasPlaceholder ? 'YES' : 'NO') . ")\n";
    } else {
        echo "❌ No template returned\n";
    }
}

echo "\n🎂 Testing actual birthday reminder process:\n";
echo "============================================\n";

// Find a member with an upcoming birthday for testing
$stmt = $pdo->prepare("
    SELECT * FROM members 
    WHERE email IS NOT NULL 
    AND email != '' 
    AND birth_date IS NOT NULL 
    LIMIT 1
");
$stmt->execute();
$testMember = $stmt->fetch();

if (!$testMember) {
    echo "❌ No test member found\n";
    exit;
}

echo "👤 Test member: " . $testMember['full_name'] . " (" . $testMember['email'] . ")\n";

// Test the template selection in the context of birthday reminders
echo "\n📧 Simulating birthday reminder template selection:\n";
echo "==================================================\n";

// Use reflection to test the private getEmailTemplate method
$getTemplateMethod = $reflection->getMethod('getEmailTemplate');
$getTemplateMethod->setAccessible(true);

echo "Original getEmailTemplate(false) results:\n";
for ($i = 1; $i <= 3; $i++) {
    $template = $getTemplateMethod->invoke($birthdayReminder, false);
    if ($template) {
        $hasPlaceholder = strpos($template['content'], '{member_image}') !== false;
        echo "  Test $i: ID " . $template['id'] . " - " . $template['template_name'] . 
             " (Has {member_image}: " . ($hasPlaceholder ? 'YES' : 'NO') . ")\n";
    }
}

echo "\n🎯 Summary:\n";
echo "===========\n";
echo "The new getBirthdayNotificationTemplate() method should now prioritize\n";
echo "templates with {member_image} placeholder for better birthday notifications.\n";
echo "\nThis should significantly increase the chance of member images being\n";
echo "included in birthday notification emails.\n";

echo "\n📊 Expected improvement:\n";
echo "========================\n";
echo "Before fix: ~20% chance of getting template with {member_image}\n";
echo "After fix:  ~100% chance of getting template with {member_image}\n";
echo "(assuming notification templates with {member_image} exist)\n";
?>
