<?php
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "🔍 Debugging Template Processing Step by Step\n";
echo "==============================================\n\n";

// Get the template
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = 37");
$stmt->execute();
$template = $stmt->fetch();

// Get Godwin
$stmt = $pdo->prepare("SELECT * FROM members WHERE full_name LIKE ?");
$stmt->execute(['%Godwin%']);
$godwin = $stmt->fetch();

// Get recipient
$stmt = $pdo->prepare("SELECT * FROM members WHERE id != ? LIMIT 1");
$stmt->execute([$godwin['id']]);
$recipient = $stmt->fetch();

echo "📧 Original Template Content (around member-section):\n";
echo "=====================================================\n";
if (preg_match('/<div class="member-section">(.*?)<\/div>/s', $template['content'], $match)) {
    echo $match[0] . "\n\n";
} else {
    echo "Member section not found\n\n";
}

// Step 1: Test processBirthdayMemberTemplate
echo "🔄 Step 1: processBirthdayMemberTemplate\n";
echo "=========================================\n";
$birthdayReminder = new BirthdayReminder($pdo);
$processedContent = $birthdayReminder->processBirthdayMemberTemplate($template['content'], $recipient, $godwin, 2);

echo "After processBirthdayMemberTemplate (around member-section):\n";
if (preg_match('/<div class="member-section">(.*?)<\/div>/s', $processedContent, $match)) {
    echo $match[0] . "\n\n";
} else {
    echo "Member section not found\n\n";
}

// Check if {member_image} still exists
$stillHasPlaceholder = strpos($processedContent, '{member_image}') !== false;
echo "Still contains {member_image}: " . ($stillHasPlaceholder ? 'YES' : 'NO') . "\n\n";

// Step 2: Test replaceTemplatePlaceholders separately
echo "🔄 Step 2: Testing replaceTemplatePlaceholders on original template\n";
echo "====================================================================\n";

$testMemberData = [
    'full_name' => $recipient['full_name'],
    'first_name' => $recipient['first_name'],
    'birthday_member_name' => 'Godwin',
    'birthday_member_full_name' => 'Godwin Bointa',
    'birthday_member_photo_url' => 'http://localhost/campaign/church/uploads/profiles/68778b33a7e2e.jpg',
    'member_image_url' => 'http://localhost/campaign/church/uploads/profiles/68778b33a7e2e.jpg',
    'image_path' => 'uploads/profiles/68778b33a7e2e.jpg',
    '_is_birthday_notification' => true,
    '_birthday_member_original_image_path' => 'uploads/profiles/68778b33a7e2e.jpg',
    'days_text' => 'in 2 days',
];

$directReplacement = replaceTemplatePlaceholders($template['content'], $testMemberData);

echo "After replaceTemplatePlaceholders (around member-section):\n";
if (preg_match('/<div class="member-section">(.*?)<\/div>/s', $directReplacement, $match)) {
    echo $match[0] . "\n\n";
} else {
    echo "Member section not found\n\n";
}

// Check what the {member_image} placeholder gets replaced with
echo "🎯 Debugging {member_image} replacement:\n";
echo "========================================\n";

$testContent = 'Test: {member_image}';
$testResult = replaceTemplatePlaceholders($testContent, $testMemberData);
echo "Test content: " . $testContent . "\n";
echo "Test result: " . $testResult . "\n\n";

echo "🔍 Analysis:\n";
echo "=============\n";
echo "The issue appears to be in how the {member_image} placeholder is being processed.\n";
echo "Let's check if there are multiple replacement steps happening.\n";
?>
