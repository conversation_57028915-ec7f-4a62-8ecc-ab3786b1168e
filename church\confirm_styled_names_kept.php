<?php
require_once 'config.php';

echo "✅ Confirming Styled Names Are Kept\n";
echo "===================================\n\n";

try {
    // Check all templates to confirm styled names are still there
    $templates = [37, 46, 58];
    
    foreach ($templates as $templateId) {
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch();
        
        if ($template) {
            echo "📋 Template $templateId: {$template['template_name']}\n";
            echo str_repeat("-", 50) . "\n";
            
            $content = $template['content'];
            
            // Look for styled names with emojis
            $styledPatterns = [
                '/🎂[^}]*\{[^}]*name[^}]*\}[^}]*🎂/',
                '/\{[^}]*name[^}]*\}[^<]*🎂/',
                '/<h[1-6][^>]*>[^<]*🎂[^<]*\{[^}]*name[^}]*\}[^<]*🎂[^<]*<\/h[1-6]>/',
                '/<[^>]*>[^<]*\{[^}]*name[^}]*\}[^<]*<\/[^>]*>.*🎂/'
            ];
            
            $foundStyled = false;
            
            foreach ($styledPatterns as $pattern) {
                if (preg_match_all($pattern, $content, $matches)) {
                    $foundStyled = true;
                    echo "✅ Found styled name pattern:\n";
                    foreach ($matches[0] as $match) {
                        echo "   " . htmlspecialchars($match) . "\n";
                    }
                }
            }
            
            // Also look for any name placeholders and show their context
            $namePatterns = [
                '{birthday_member_name}',
                '{birthday_member_full_name}',
                '{birthday_member_first_name}'
            ];
            
            echo "\n🔍 All name placeholder contexts:\n";
            foreach ($namePatterns as $pattern) {
                $pos = 0;
                $occurrence = 1;
                
                while (($pos = strpos($content, $pattern, $pos)) !== false) {
                    $start = max(0, $pos - 80);
                    $end = min(strlen($content), $pos + 120);
                    $context = substr($content, $start, $end - $start);
                    
                    echo "   $occurrence. $pattern context:\n";
                    echo "      " . htmlspecialchars($context) . "\n";
                    
                    // Check if this occurrence has emojis
                    if (strpos($context, '🎂') !== false || strpos($context, '🎉') !== false) {
                        echo "      ✅ This is a STYLED name (with emojis) - KEPT\n";
                    } else {
                        echo "      📝 This is a regular name usage\n";
                    }
                    
                    $pos++;
                    $occurrence++;
                }
            }
            
            if (!$foundStyled) {
                echo "⚠️ No styled names found in this template\n";
            }
            
            echo "\n" . str_repeat("=", 60) . "\n\n";
        }
    }
    
    echo "🎯 SUMMARY OF WHAT WAS ACCOMPLISHED:\n";
    echo str_repeat("=", 50) . "\n";
    echo "✅ REMOVED: Plain name immediately after {member_image}\n";
    echo "✅ KEPT: All styled names with emojis (🎂 Name 🎂)\n";
    echo "✅ KEPT: Names in celebration suggestions\n";
    echo "✅ KEPT: Names in email opening text\n";
    echo "✅ KEPT: Names in mailto links\n\n";
    
    echo "📧 CURRENT EMAIL STRUCTURE:\n";
    echo "1. 📸 Member image\n";
    echo "2. 📅 Birthday details (date, age)\n";
    echo "3. 🎂 Styled member name with emojis\n";
    echo "4. 🎉 Celebration suggestions\n";
    echo "5. 📖 Scripture or closing\n\n";
    
    echo "✅ The duplicate name issue is FIXED!\n";
    echo "The email now shows the member's name appropriately\n";
    echo "without the visual duplication right after the image.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
