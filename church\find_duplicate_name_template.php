<?php
require_once 'config.php';

echo "🔍 Finding Email Template with Duplicate Member Names\n";
echo "====================================================\n\n";

try {
    // Get all email templates that might be used for birthday notifications
    $stmt = $pdo->prepare("
        SELECT id, template_name, subject, content 
        FROM email_templates 
        WHERE (template_name LIKE '%Birthday%' OR template_name LIKE '%Notification%')
        AND content LIKE '%{member_image%'
        ORDER BY template_name
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll();

    if (empty($templates)) {
        echo "❌ No templates found with member images\n";
        exit;
    }

    echo "📧 Found " . count($templates) . " templates with member images:\n\n";

    foreach ($templates as $template) {
        echo "🎯 Template ID: " . $template['id'] . "\n";
        echo "📝 Name: " . $template['template_name'] . "\n";
        echo "📧 Subject: " . $template['subject'] . "\n";
        
        // Check for duplicate member name patterns
        $content = $template['content'];
        
        // Count occurrences of member name placeholders
        $namePatterns = [
            '{birthday_member_name}',
            '{birthday_member_full_name}',
            '{member_name}',
            '{full_name}'
        ];
        
        $duplicateFound = false;
        foreach ($namePatterns as $pattern) {
            $count = substr_count($content, $pattern);
            if ($count > 1) {
                echo "⚠️  DUPLICATE FOUND: '$pattern' appears $count times\n";
                $duplicateFound = true;
            }
        }
        
        // Look for member image followed by member name patterns
        if (preg_match('/\{[^}]*member[^}]*image[^}]*\}.*?\{[^}]*member[^}]*name[^}]*\}/s', $content)) {
            echo "🖼️  Pattern: Member image followed by member name found\n";
        }
        
        // Show content around member image
        if (strpos($content, '{member_image}') !== false || 
            strpos($content, '{birthday_member_image') !== false) {
            
            echo "📄 Content preview around member image:\n";
            
            // Find position of member image
            $imagePos = strpos($content, '{member_image}');
            if ($imagePos === false) {
                $imagePos = strpos($content, '{birthday_member_image');
            }
            
            if ($imagePos !== false) {
                $start = max(0, $imagePos - 200);
                $length = 600;
                $preview = substr($content, $start, $length);
                
                // Clean up for display
                $preview = strip_tags($preview);
                $preview = preg_replace('/\s+/', ' ', $preview);
                $preview = trim($preview);
                
                echo "\"" . $preview . "\"\n";
            }
        }
        
        if ($duplicateFound) {
            echo "🚨 THIS TEMPLATE HAS DUPLICATE MEMBER NAMES!\n";
            
            // Show the full content for analysis
            echo "\n📋 Full Template Content:\n";
            echo "========================\n";
            echo $content . "\n";
            echo "========================\n";
        }
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }

    // Also check for templates that might be used in the birthday notification system
    echo "\n🎂 Checking Birthday Notification Templates:\n";
    echo "===========================================\n";
    
    $stmt = $pdo->prepare("
        SELECT id, template_name, subject, content 
        FROM email_templates 
        WHERE is_birthday_template = 0
        AND template_name LIKE '%Notification%'
        ORDER BY template_name
    ");
    $stmt->execute();
    $notificationTemplates = $stmt->fetchAll();
    
    foreach ($notificationTemplates as $template) {
        echo "\n📧 Notification Template: " . $template['template_name'] . " (ID: " . $template['id'] . ")\n";
        
        // Check if this template has member image and multiple names
        $content = $template['content'];
        $hasImage = (strpos($content, '{member_image}') !== false || 
                    strpos($content, '{birthday_member_image') !== false);
        
        if ($hasImage) {
            echo "🖼️  Has member image placeholder\n";
            
            // Count member name occurrences
            $nameCount = 0;
            $nameCount += substr_count($content, '{birthday_member_name}');
            $nameCount += substr_count($content, '{birthday_member_full_name}');
            $nameCount += substr_count($content, '{member_name}');
            
            if ($nameCount > 1) {
                echo "⚠️  DUPLICATE NAMES: $nameCount member name placeholders found\n";
                echo "📄 Template content:\n";
                echo $content . "\n";
                echo "========================\n";
            }
        }
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n✅ Template analysis complete!\n";
?>
