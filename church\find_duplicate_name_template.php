<?php
require_once 'config.php';

echo "🔍 Finding Birthday Template with Duplicate Name Issue\n";
echo "====================================================\n\n";

try {
    // Find birthday notification templates that contain member_image
    $stmt = $pdo->prepare("
        SELECT id, template_name, content 
        FROM email_templates 
        WHERE (template_name LIKE '%Notification%' OR template_name LIKE '%Birthday%')
        AND content LIKE '%{member_image}%'
        ORDER BY id
    ");
    $stmt->execute();
    $templates = $stmt->fetchAll();
    
    if (empty($templates)) {
        echo "❌ No birthday notification templates found with {member_image}\n";
        exit;
    }
    
    echo "📋 Found " . count($templates) . " templates with {member_image}:\n\n";
    
    foreach ($templates as $template) {
        echo "🎯 Template ID: {$template['id']}\n";
        echo "📝 Name: {$template['template_name']}\n";
        echo "📄 Content Analysis:\n";
        
        $content = $template['content'];
        
        // Look for member image placeholder
        if (strpos($content, '{member_image}') !== false) {
            echo "   ✅ Contains {member_image}\n";
            
            // Find context around member_image
            $pos = strpos($content, '{member_image}');
            $start = max(0, $pos - 200);
            $end = min(strlen($content), $pos + 400);
            $context = substr($content, $start, $end - $start);
            
            echo "   📍 Context around {member_image}:\n";
            echo "   " . str_repeat("-", 50) . "\n";
            echo "   " . htmlspecialchars($context) . "\n";
            echo "   " . str_repeat("-", 50) . "\n";
            
            // Look for potential duplicate name patterns
            $namePatterns = [
                '{full_name}',
                '{first_name}',
                '{birthday_member_name}',
                '{member_name}',
                'Sandra Stern' // Specific case from the screenshot
            ];
            
            $nameOccurrences = [];
            foreach ($namePatterns as $pattern) {
                $count = substr_count($content, $pattern);
                if ($count > 0) {
                    $nameOccurrences[$pattern] = $count;
                }
            }
            
            if (!empty($nameOccurrences)) {
                echo "   🔍 Name placeholder occurrences:\n";
                foreach ($nameOccurrences as $pattern => $count) {
                    echo "      - $pattern: $count times\n";
                    if ($count > 1) {
                        echo "        ⚠️  POTENTIAL DUPLICATE!\n";
                    }
                }
            }
            
            // Look for HTML structure that might cause duplication
            if (preg_match_all('/(<[^>]*>)?\s*\{[^}]*name[^}]*\}\s*(<[^>]*>)?/i', $content, $matches, PREG_OFFSET_CAPTURE)) {
                echo "   🏗️  Name placeholder HTML structure:\n";
                foreach ($matches[0] as $match) {
                    $text = trim($match[0]);
                    $position = $match[1];
                    echo "      Position $position: " . htmlspecialchars($text) . "\n";
                }
            }
        }
        
        echo "\n" . str_repeat("=", 80) . "\n\n";
    }
    
    // Now let's specifically look for the template that might be causing the issue
    echo "🎯 SEARCHING FOR SPECIFIC DUPLICATE PATTERN...\n\n";
    
    foreach ($templates as $template) {
        $content = $template['content'];
        
        // Look for pattern where member image is followed by name, then another name
        if (preg_match('/\{member_image\}.*?(\{[^}]*name[^}]*\}).*?(\{[^}]*name[^}]*\})/is', $content, $matches)) {
            echo "🚨 FOUND POTENTIAL DUPLICATE in Template ID: {$template['id']}\n";
            echo "📝 Template: {$template['template_name']}\n";
            echo "🔍 Pattern found:\n";
            echo "   First name: {$matches[1]}\n";
            echo "   Second name: {$matches[2]}\n";
            
            // Show more context
            $fullMatch = $matches[0];
            echo "   📄 Full context:\n";
            echo "   " . str_repeat("-", 50) . "\n";
            echo "   " . htmlspecialchars($fullMatch) . "\n";
            echo "   " . str_repeat("-", 50) . "\n\n";
        }
        
        // Also look for hardcoded names that might be duplicated
        if (strpos($content, 'Sandra Stern') !== false) {
            echo "🎯 FOUND TEMPLATE WITH 'Sandra Stern' - Template ID: {$template['id']}\n";
            echo "📝 Template: {$template['template_name']}\n";
            
            // Count occurrences
            $count = substr_count($content, 'Sandra Stern');
            echo "🔢 'Sandra Stern' appears $count times\n";
            
            if ($count > 1) {
                echo "⚠️  CONFIRMED DUPLICATE!\n";
                
                // Find all positions
                $pos = 0;
                $positions = [];
                while (($pos = strpos($content, 'Sandra Stern', $pos)) !== false) {
                    $positions[] = $pos;
                    $pos++;
                }
                
                echo "📍 Positions: " . implode(', ', $positions) . "\n";
                
                // Show context for each occurrence
                foreach ($positions as $i => $pos) {
                    $start = max(0, $pos - 100);
                    $end = min(strlen($content), $pos + 150);
                    $context = substr($content, $start, $end - $start);
                    
                    echo "   Occurrence " . ($i + 1) . " context:\n";
                    echo "   " . str_repeat("-", 40) . "\n";
                    echo "   " . htmlspecialchars($context) . "\n";
                    echo "   " . str_repeat("-", 40) . "\n";
                }
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
