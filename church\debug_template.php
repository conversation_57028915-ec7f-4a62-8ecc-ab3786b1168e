<?php
require_once 'config.php';

echo "🔍 Debugging Member Upcoming Birthday Notification Template\n";
echo "==========================================================\n\n";

// Get the Member Upcoming Birthday Notification template
$stmt = $pdo->prepare('SELECT * FROM email_templates WHERE template_name = ?');
$stmt->execute(['Member Upcoming Birthday Notification 1']);
$template = $stmt->fetch();

if ($template) {
    echo "✅ Template found: " . $template['template_name'] . "\n\n";
    
    echo "📝 Template Content:\n";
    echo "==================\n";
    echo $template['content'] . "\n\n";
    
    echo "🔍 Placeholder Analysis:\n";
    echo "========================\n";
    echo "Contains {member_image}: " . (strpos($template['content'], '{member_image}') !== false ? 'YES' : 'NO') . "\n";
    echo "Contains {birthday_member_image}: " . (strpos($template['content'], '{birthday_member_image}') !== false ? 'YES' : 'NO') . "\n";
    
    // Show context around member_image if found
    if (strpos($template['content'], '{member_image}') !== false) {
        $pos = strpos($template['content'], '{member_image}');
        $start = max(0, $pos - 100);
        $context = substr($template['content'], $start, 200);
        echo "\n📍 Context around {member_image}:\n";
        echo $context . "\n";
    }
} else {
    echo "❌ Template 'Member Upcoming Birthday Notification 1' not found\n";
    
    // Show available templates
    echo "\n📋 Available templates:\n";
    $stmt = $pdo->query("SELECT template_name FROM email_templates WHERE template_name LIKE '%Birthday%'");
    while ($row = $stmt->fetch()) {
        echo "- " . $row['template_name'] . "\n";
    }
}
?>
